# Core FastAPI dependencies
fastapi>=0.116.0
uvicorn>=0.35.0
pydantic>=2.11.7

# Storage and cloud services
boto3>=1.39.4
redis>=5.0.0

# Database
sqlalchemy>=2.0.0
asyncpg>=0.29.0
alembic>=1.13.0

# HTTP and async
aiohttp>=3.12.14
aiofiles>=24.1.0
python-multipart>=0.0.20
requests>=2.31.0

# Security and rate limiting
slowapi>=0.1.9
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# Image and media processing
Pillow>=10.1.0,<11.0.0
ffmpeg-python>=0.2.0
opencv-python>=4.8.0
pytesseract>=0.3.10
moviepy==1.0.3

# AI and media tools
faster-whisper>=1.0.0
yt-dlp>=2025.6.30
youtube-transcript-api~=1.0.0
openai>=1.0.0
groq>=0.11.0
google-generativeai>=0.8.0 
grpcio-status<1.73.0 
protobuf>=3.20.2,<6.0.0 
librosa>=0.10.0
webrtcvad-wheels>=2.0.0
pydub>=0.25.1

# Music generation (MusicGen) - Simplified implementation using audiocraft
audiocraft>=1.3.0
torch>=2.0.0
torchaudio>=2.0.0
scipy>=1.11.0
numpy>=1.24.0

# Utilities
python-dotenv>=1.0.1
psutil>=6.1.0
loguru>=0.7.0

# Edge TTS (Microsoft Edge Text-to-Speech)
edge-tts>=6.1.0
emoji>=2.10.1

# Kokoro ONNX TTS (Internal high-quality TTS)
kokoro-onnx
soundfile>=0.12.0

# ONNX runtime for ML models
onnxruntime
huggingface_hub

# Document processing
markitdown[all]>=0.1.0
python-docx>=1.0.1
openpyxl>=3.1.5
pdfplumber>=0.11.4
beautifulsoup4>=4.12.3
lxml>=5.3.0

# Google Langextract - AI-powered structured data extraction
langextract>=1.0.5

# Marker - Professional document processing and conversion
# marker-pdf[full]>=0.2.12

# MCP server support
sse-starlette>=2.1.3
pydantic-core>=2.11.0