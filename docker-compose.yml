services:
  postgres:
    image: postgres:16-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ouinhi}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-ouinhi}"]
      interval: 10s
      timeout: 3s
      retries: 3

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  api:
    build:
      context: .
      target: production
      cache_from:
        - ${DOCKER_REGISTRY:-}ouinhi-api:latest
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "${API_PORT:-8000}:8000"
    volumes:
      - kokoro_models:/app/models
      - huggingface_cache:/root/.cache/huggingface
      - temp_storage:/app/temp
      # Temporarily disabled: - music_files:/app/static/music
    deploy:
      resources:
        limits:
          memory: 40G  # Allow generous memory for Marker model downloads and processing
        reservations:
          memory: 4G
    environment:
      # Core API configuration
      API_KEY: ${API_KEY}
      
      # Admin authentication
      ADMIN_USERNAME: ${ADMIN_USERNAME}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD}
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      
      # Dashboard authentication
      DEFAULT_USERNAME: ${DEFAULT_USERNAME}
      DEFAULT_PASSWORD: ${DEFAULT_PASSWORD}
      
      # S3 Storage configuration
      S3_ENDPOINT_URL: ${S3_ENDPOINT_URL}
      S3_ACCESS_KEY: ${S3_ACCESS_KEY}
      S3_SECRET_KEY: ${S3_SECRET_KEY}
      S3_BUCKET_NAME: ${S3_BUCKET_NAME}
      S3_REGION: ${S3_REGION}
      
      # Database configuration using environment variables
      DATABASE_URL: postgresql+asyncpg://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-ouinhi}
      POSTGRES_DB: ${POSTGRES_DB:-ouinhi}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      
      # Redis configuration
      REDIS_URL: ${REDIS_URL:-redis://:${REDIS_PASSWORD}@redis:6379/0}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      
      # AI Services
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      POLLINATIONS_API_KEY: ${POLLINATIONS_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
      TOGETHER_API_KEY: ${TOGETHER_API_KEY}
      PERPLEXITY_API_KEY: ${PERPLEXITY_API_KEY}
      
      # TTS Configuration
      TTS_PROVIDER: ${TTS_PROVIDER:-kokoro}
      KOKORO_MODEL_PATH: ${KOKORO_MODEL_PATH:-/app/models}
      
      # Postiz Integration
      POSTIZ_API_KEY: ${POSTIZ_API_KEY}
      POSTIZ_API_URL: ${POSTIZ_API_URL:-https://api.postiz.com/public/v1}
      
      # ComfyUI Integration
      COMFYUI_URL: ${COMFYUI_URL}
      COMFYUI_USERNAME: ${COMFYUI_USERNAME}
      COMFYUI_PASSWORD: ${COMFYUI_PASSWORD}
      COMFYUI_API_KEY: ${COMFYUI_API_KEY}
      
      # Model pre-loading configuration (simplified implementation)
      ENABLE_MODEL_PRELOAD: ${ENABLE_MODEL_PRELOAD:-true}
      MUSICGEN_MODEL_SIZE: ${MUSICGEN_MODEL_SIZE:-small}
      # Note: Only 'small' model supported in simplified implementation
      TRANSFORMERS_CACHE: /root/.cache/huggingface

      # Music generation configuration
      ENABLE_MODEL_WARMUP: ${ENABLE_MODEL_WARMUP:-true}
      LOCAL_STORAGE_PATH: ${LOCAL_STORAGE_PATH:-/app/temp}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health", "--connect-timeout", "5"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:
  redis_data:
  kokoro_models:
  huggingface_cache:
  temp_storage:
  # music_files:  # Temporarily disabled

networks:
  default:
    driver: bridge
        