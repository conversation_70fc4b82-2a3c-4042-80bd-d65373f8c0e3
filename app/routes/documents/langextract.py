"""
Routes for AI-powered structured data extraction using Google Langextract.

This module provides endpoints for extracting structured information from unstructured text
using large language models (LLMs) with source grounding and interactive visualization.
"""
import os
import uuid
import logging
from typing import Any

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends, status
from fastapi.responses import JSONResponse

from app.utils.auth import get_api_key
from app.services.documents.langextract_service import langextract_service
from app.services.job_queue import job_queue
from app.models import (
    JobResponse, JobStatusResponse, JobType, JobStatus,
    LangextractRequest, LangextractResult
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/documents", tags=["documents", "langextract"])


@router.get("/langextract/models")
async def get_supported_models():
    """
    Get comprehensive information about supported AI models for data extraction.
    
    Returns detailed information about supported AI models, extraction capabilities,
    and available features using Google Langextract.
    
    Returns:
        Dict containing:
        - supported_models: Available AI models (Gemini, OpenAI)
        - extraction_types: Types of data that can be extracted
        - input_formats: Supported input formats
        - features: Available extraction features
        - available: Whether the service is available
    """
    return langextract_service.get_supported_models()


async def process_langextract_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for Langextract data extraction job processing."""
    return await langextract_service.extract_structured_data(data)


@router.post("/langextract", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def extract_structured_data(
    input_text: str = Form(None, description="Direct text input for data extraction (optional)"),
    file: UploadFile = File(None, description="Document file for data extraction (PDF, DOCX, TXT, HTML) - alternative to text/URL"),
    file_url: str = Form(None, description="URL of document file for data extraction - alternative to text/file"),
    extraction_schema: str = Form('{"entities": ["person", "organization", "location"], "relationships": ["works_for", "located_in"]}', 
                                  description="JSON schema defining what to extract"),
    extraction_prompt: str = Form("Extract all people, organizations, and locations from the text. Also identify relationships between entities.",
                                  description="Custom prompt for extraction (used if use_custom_prompt=true)"),
    use_custom_prompt: bool = Form(False, description="Use custom prompt (true) or JSON schema (false)"),
    model: str = Form("gemini", description="AI model to use: 'gemini' (primary) or 'openai' (fallback)"),
    _: str = Depends(get_api_key),  # API key validation
):
    """
    Extract structured data from text using AI-powered analysis.
    
    This endpoint uses Google Langextract to extract structured information like entities,
    relationships, and attributes from unstructured text with precise source grounding.
    
    **Supported AI Models:**
    - **Gemini (Google)**: Primary model with advanced language understanding
    - **OpenAI**: Fallback model for reliability and redundancy
    
    **Extraction Types:**
    - **Named Entities**: People, organizations, locations, dates, etc.
    - **Relationships**: Connections between entities (works_for, located_in, etc.)
    - **Attributes**: Properties and characteristics of entities
    - **Custom Schema**: User-defined extraction patterns
    
    **Input Methods:**
    - **Direct Text**: Paste text directly for immediate processing
    - **File Upload**: Upload PDF, DOCX, TXT, or HTML files
    - **URL**: Extract data from web documents
    
    **Key Features:**
    - **Source Grounding**: Shows exactly where each extracted item was found
    - **Schema Flexibility**: Define custom extraction schemas or use prompts
    - **Multi-Model Support**: Automatic fallback between Gemini and OpenAI
    - **Interactive Visualization**: Results include visualization data
    
    **Extraction Configuration:**
    Choose between two extraction methods:
    1. **Schema-based**: Define JSON structure of what to extract
    2. **Prompt-based**: Use natural language to describe extraction goals
    
    **Processing:**
    This is an asynchronous operation. Use the returned job_id to poll for results.
    Processing time varies based on text length and complexity.
    
    Args:
        input_text: Direct text input (alternative to file/URL)
        file: Document file upload (alternative to text/URL)  
        file_url: URL of document to extract from (alternative to text/file)
        extraction_schema: JSON schema defining extraction structure
        extraction_prompt: Natural language extraction prompt
        use_custom_prompt: Whether to use prompt (true) or schema (false)
        model: AI model to use ('gemini' or 'openai')
        
    Returns:
        JobResponse with job_id for tracking the extraction progress
        
    Raises:
        HTTPException: If parameters are invalid or service unavailable
    """
    try:
        # Check if Langextract service is available
        if not langextract_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Langextract service is not available. Please install 'langextract>=1.0.5' package."
            )
        
        # Validate input parameters - at least one input method required
        if not input_text and not file and not file_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one input method must be provided: input_text, file, or file_url"
            )
        
        # Validate only one input method is used
        input_count = sum(bool(x) for x in [input_text, file, file_url])
        if input_count > 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Provide only one input method: input_text, file, or file_url"
            )
        
        # Validate model parameter
        if model not in ["gemini", "openai"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Model must be 'gemini' or 'openai'"
            )
        
        # Check API key availability for selected model
        if model == "gemini" and not os.getenv('GEMINI_API_KEY'):
            if not os.getenv('OPENAI_API_KEY'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Neither GEMINI_API_KEY nor OPENAI_API_KEY is configured"
                )
            else:
                logger.warning("GEMINI_API_KEY not found, will fallback to OpenAI")
                model = "openai"
        elif model == "openai" and not os.getenv('OPENAI_API_KEY'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="OPENAI_API_KEY is required for OpenAI model"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_data = {
            "input_text": input_text,
            "extraction_schema": extraction_schema,
            "extraction_prompt": extraction_prompt,
            "use_custom_prompt": use_custom_prompt,
            "model": model
        }
        
        if file:
            # Handle file upload
            if file.size and file.size > 10 * 1024 * 1024:  # 10MB limit for text extraction
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail="File size exceeds 10MB limit for data extraction"
                )
            
            # Validate file type by extension
            if file.filename:
                allowed_extensions = {'.pdf', '.docx', '.doc', '.txt', '.html', '.htm'}
                file_ext = os.path.splitext(file.filename.lower())[1]
                if file_ext not in allowed_extensions:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Unsupported file type '{file_ext}'. Supported: {', '.join(allowed_extensions)}"
                    )
            
            # Process file immediately to avoid storing binary data in job params
            file_content = await file.read()
            job_data["input_filename"] = file.filename or "uploaded_document"
            
            # Create wrapper that processes binary data directly
            async def process_langextract_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
                return await langextract_service.process_extraction_with_file_data(
                    file_content=file_content,
                    params=data
                )
        elif file_url:
            # Handle URL input
            job_data["file_url"] = file_url
            job_data["input_filename"] = os.path.basename(file_url) or "document_from_url"
            
            # Create wrapper for URL-based processing
            async def process_langextract_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
                return await langextract_service.extract_structured_data(data)
        else:
            # Handle direct text input
            job_data["input_filename"] = "direct_text_input"
            
            # Create wrapper for text processing
            async def process_langextract_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
                return await langextract_service.extract_structured_data(data)
        
        # Queue the job using consistent pattern
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.LANGEXTRACT_DATA_EXTRACTION,
            process_func=process_langextract_wrapper,
            data=job_data
        )
        
        logger.info(f"Created Langextract extraction job {job_id} with model {model} for: {job_data.get('input_filename', 'unknown')}")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Langextract extraction job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create data extraction job"
        )


@router.get("/langextract/{job_id}", response_model=JobStatusResponse)
async def get_extraction_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a Langextract data extraction job.
    
    Poll this endpoint to monitor the progress of your data extraction.
    Extraction time varies based on input size, complexity, and chosen AI model.
    
    **Processing Stages:**
    1. `pending` → Job queued for processing
    2. `processing` → AI model analyzing and extracting data
    3. `completed` → Extraction finished successfully
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "extracted_data": {
            "person": [
                {
                    "value": "John Smith",
                    "sources": [
                        {
                            "start": 0,
                            "end": 10,
                            "text": "John Smith"
                        }
                    ]
                }
            ],
            "organization": [...],
            "location": [...]
        },
        "total_extractions": 15,
        "processing_time": 2.3,
        "model_used": "gemini",
        "source_grounding_enabled": true
    }
    ```
    
    **Key Features of Results:**
    - **Extracted Data**: Structured data organized by entity type
    - **Source Grounding**: Exact text locations for each extraction
    - **Processing Stats**: Total extractions, time taken, model used
    - **S3 Storage**: Results stored in S3 for long-term access
    
    Args:
        job_id: The ID of the extraction job to check
        _: API key for authentication
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job_info = await job_queue.get_job_info(job_id)
        
        if not job_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        # Ensure status is a JobStatus enum
        job_status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
        
        return JobStatusResponse(
            job_id=job_id,
            status=job_status,
            result=job_info.result,
            error=job_info.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Langextract job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )


@router.post("/langextract/sync", response_model=LangextractResult)
async def extract_structured_data_sync(
    request: LangextractRequest,
    _: str = Depends(get_api_key)
):
    """
    Extract structured data from text synchronously.
    
    This endpoint provides immediate synchronous data extraction for smaller texts.
    For larger documents or when you need asynchronous processing, use the main
    /langextract endpoint instead.
    
    **Note:** This endpoint blocks until extraction is complete, so it's only
    suitable for smaller texts (< 5000 characters) that extract quickly.
    
    **Use Cases:**
    - Quick entity extraction from short texts
    - Real-time analysis of user input
    - Testing extraction schemas and prompts
    - Integration with synchronous workflows
    
    Args:
        request: Data extraction request with text and configuration
        _: API key for authentication
        
    Returns:
        LangextractResult: Immediate extraction result with source grounding
        
    Raises:
        HTTPException: If extraction fails or service unavailable
    """
    try:
        # Check if Langextract service is available
        if not langextract_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Langextract service is not available. Please install 'langextract>=1.0.5' package."
            )
        
        # Validate input
        if not request.input_text and not request.file_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either input_text or file_url is required for synchronous extraction"
            )
        
        # Check text length for synchronous processing
        if request.input_text and len(request.input_text) > 5000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Text too long for synchronous processing (max 5000 characters). Use async /langextract endpoint."
            )
        
        # Prepare extraction data
        extraction_data = {
            "input_text": request.input_text,
            "file_url": str(request.file_url) if request.file_url else None,
            "extraction_schema": request.extraction_schema,
            "extraction_prompt": request.extraction_prompt,
            "use_custom_prompt": request.use_custom_prompt,
            "model": request.model,
            "input_filename": "synchronous_extraction"
        }
        
        # Process synchronously
        result = await langextract_service.extract_structured_data(extraction_data)
        
        logger.info(f"Synchronous Langextract extraction completed with {result['total_extractions']} extractions")
        
        return LangextractResult(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Synchronous Langextract extraction failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Data extraction failed: {str(e)}"
        )