"""
Marker Document Processing Routes

High-quality document conversion using Marker library.
Supports PDF, DOCX, images, and more with advanced features.
"""

import os
import tempfile
import aiofiles
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from typing import Optional, Dict, Any
import logging

from app.models import (
    JobResponse, JobStatusResponse, JobType, 
    MarkerConversionRequest, MarkerConversionResult, MarkerSupportedFormatsResponse
)
from app.services.job_queue import job_queue
from app.services.marker_service import marker_service
from app.utils.auth import get_api_key

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/convert", response_model=JobResponse)
async def convert_document_with_marker(
    file: Optional[UploadFile] = File(None),
    url: Optional[str] = Form(None),
    output_format: str = Form("markdown"),
    force_ocr: bool = Form(False),
    preserve_images: bool = Form(True),
    use_llm: bool = Form(False),
    paginate_output: bool = Form(False),
    llm_service: Optional[str] = Form(None),
    api_key: str = Depends(get_api_key)
):
    """
    Convert documents using Marker for high-quality processing.
    
    Features:
    - Professional document conversion
    - Advanced table formatting
    - Mathematical equation processing
    - Multi-language support
    - Image extraction
    - LLM-enhanced accuracy
    
    Supports: PDF, DOCX, PPTX, XLSX, images, HTML, EPUB, and more.
    """
    if not file and not url:
        raise HTTPException(status_code=400, detail="Either file or URL must be provided")
    
    if file and url:
        raise HTTPException(status_code=400, detail="Provide either file or URL, not both")
    
    # Validate output format
    supported_formats = ["markdown", "json", "html", "chunks"]
    if output_format not in supported_formats:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid output format. Supported: {supported_formats}"
        )
    
    # Validate LLM service if provided
    if llm_service and llm_service not in ["openai", "gemini"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid LLM service. Supported: openai, gemini"
        )
    
    # Initialize variables
    file_path = None
    original_filename = "document"
    
    try:
        # Create wrapper function for job queue
        async def marker_conversion_wrapper(job_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
            return await marker_service.convert_document(data)
        
        # Handle file upload
        
        if file:
            # Validate file type
            allowed_extensions = {
                '.pdf', '.docx', '.doc', '.pptx', '.ppt', '.xlsx', '.xls',
                '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff',
                '.html', '.htm', '.epub', '.txt'
            }
            
            file_ext = os.path.splitext(file.filename or "")[1].lower()
            if file_ext not in allowed_extensions:
                raise HTTPException(
                    status_code=400,
                    detail=f"Unsupported file type. Allowed: {', '.join(allowed_extensions)}"
                )
            
            # Save uploaded file
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
                content = await file.read()
                temp_file.write(content)
                file_path = temp_file.name
                original_filename = file.filename or "document"
        
        elif url:
            # For URL processing, we'll need to download the file first
            import aiohttp
            
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status != 200:
                            raise HTTPException(
                                status_code=400,
                                detail=f"Failed to download file from URL: HTTP {response.status}"
                            )
                        
                        # Determine file extension from URL or content type
                        content_type = response.headers.get('content-type', '')
                        file_ext = '.pdf'  # default
                        
                        if 'pdf' in content_type:
                            file_ext = '.pdf'
                        elif 'word' in content_type or 'document' in content_type:
                            file_ext = '.docx'
                        elif 'image' in content_type:
                            if 'png' in content_type:
                                file_ext = '.png'
                            elif 'jpeg' in content_type or 'jpg' in content_type:
                                file_ext = '.jpg'
                        
                        # Save downloaded file
                        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
                            content = await response.read()
                            temp_file.write(content)
                            file_path = temp_file.name
                            original_filename = url.split('/')[-1] or "document"
                            
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to download file from URL: {str(e)}"
                )
        
        # Prepare job data
        job_data = {
            "file_path": file_path,
            "output_format": output_format,
            "force_ocr": force_ocr,
            "preserve_images": preserve_images,
            "use_llm": use_llm,
            "paginate_output": paginate_output,
            "llm_service": llm_service,
            "original_filename": original_filename
        }
        
        # Generate job ID
        import uuid
        job_id = str(uuid.uuid4())
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MARKER_DOCUMENT_CONVERSION,
            process_func=marker_conversion_wrapper,
            data=job_data
        )
        
        logger.info(f"Marker conversion job created: {job_id} for file: {original_filename}")
        return JobResponse(job_id=job_id)
        
    except Exception as e:
        # Clean up temp file if it was created
        if file_path and os.path.exists(file_path):
            try:
                os.unlink(file_path)
            except:
                pass
        
        logger.error(f"Failed to create Marker conversion job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create conversion job: {str(e)}")

@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_marker_conversion_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """
    Get the status of a Marker document conversion job.
    
    Returns job status and results when completed.
    """
    try:
        from app.services.database_service import db_job_service
        
        job = await db_job_service.get_job(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return JobStatusResponse(
            job_id=job_id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except Exception as e:
        logger.error(f"Failed to get Marker job status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")

@router.get("/formats/supported", response_model=MarkerSupportedFormatsResponse)
async def get_supported_formats(
    api_key: str = Depends(get_api_key)
):
    """
    Get list of supported input/output formats and features.
    
    Returns comprehensive information about Marker capabilities.
    """
    try:
        formats_info = await marker_service.get_supported_formats()
        return MarkerSupportedFormatsResponse(**formats_info)
        
    except Exception as e:
        logger.error(f"Failed to get supported formats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get supported formats: {str(e)}")

@router.post("/convert-sync", response_model=MarkerConversionResult)
async def convert_document_sync(
    file: UploadFile = File(...),
    output_format: str = Form("markdown"),
    force_ocr: bool = Form(False),
    preserve_images: bool = Form(True),
    use_llm: bool = Form(False),
    paginate_output: bool = Form(False),
    llm_service: Optional[str] = Form(None),
    api_key: str = Depends(get_api_key)
):
    """
    Convert document synchronously using Marker (for small files).
    
    WARNING: This endpoint processes documents synchronously and may timeout
    for large files. Use the async /convert endpoint for production use.
    """
    # Validate file type
    allowed_extensions = {
        '.pdf', '.docx', '.doc', '.pptx', '.ppt', '.xlsx', '.xls',
        '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff',
        '.html', '.htm', '.epub', '.txt'
    }
    
    file_ext = os.path.splitext(file.filename or "")[1].lower()
    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type. Allowed: {', '.join(allowed_extensions)}"
        )
    
    # Check file size (limit to 10MB for sync processing)
    max_size = 10 * 1024 * 1024  # 10MB
    content = await file.read()
    if len(content) > max_size:
        raise HTTPException(
            status_code=400,
            detail="File too large for synchronous processing. Use async /convert endpoint."
        )
    
    file_path = None
    try:
        # Save uploaded file
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
            temp_file.write(content)
            file_path = temp_file.name
        
        # Prepare conversion parameters
        params = {
            "file_path": file_path,
            "output_format": output_format,
            "force_ocr": force_ocr,
            "preserve_images": preserve_images,
            "use_llm": use_llm,
            "paginate_output": paginate_output,
            "llm_service": llm_service,
            "original_filename": file.filename or "document"
        }
        
        # Process document
        result = await marker_service.convert_document(params)
        
        return MarkerConversionResult(**result)
        
    except Exception as e:
        logger.error(f"Synchronous Marker conversion failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Conversion failed: {str(e)}")
        
    finally:
        # Clean up temp file
        if file_path and os.path.exists(file_path):
            try:
                os.unlink(file_path)
            except:
                pass