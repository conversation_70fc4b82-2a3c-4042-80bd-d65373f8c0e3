"""Document processing routes."""
from fastapi import APIRouter
from app.routes.documents.to_markdown import router as to_markdown_router
from app.routes.documents.langextract import router as langextract_router
from app.routes.documents.marker import router as marker_router

# Create a main router that includes all document-related routes
router = APIRouter()
router.include_router(to_markdown_router)
router.include_router(langextract_router)
router.include_router(marker_router, prefix="/v1/documents/marker", tags=["marker"])