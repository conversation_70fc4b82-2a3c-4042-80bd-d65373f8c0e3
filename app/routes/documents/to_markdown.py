"""
Routes for document to Markdown conversion using Microsoft MarkItDown.

This module provides endpoints for converting various document formats 
(PDF, Word, Excel, PowerPoint, etc.) to Markdown format.
"""
import os
import uuid
import base64
import logging
from typing import Any

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends, status
from fastapi.responses import JSONResponse

from app.utils.auth import get_api_key
from app.services.documents.markitdown_service import markitdown_service
from app.services.job_queue import job_queue
from app.models import (
    JobResponse, JobStatusResponse, JobType, JobStatus,
    DocumentToMarkdownRequest, DocumentToMarkdownResult
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/documents", tags=["documents"])


@router.get("/formats")
async def get_supported_formats():
    """
    Get comprehensive list of supported document formats for conversion.
    
    Returns detailed information about supported document formats and conversion
    capabilities using Microsoft MarkItDown.
    
    Returns:
        Dict containing:
        - supported_formats: Organized by document type
        - features: Available conversion features
        - requirements: Library requirements
        - available: Whether the service is available
    """
    return markitdown_service.get_supported_formats()


async def process_markdown_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for document to Markdown conversion job processing."""
    return await markitdown_service.process_document_to_markdown(data)


@router.post("/to-markdown", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def convert_document_to_markdown(
    file: UploadFile = File(None, description="Document file to convert (PDF, Word, Excel, etc.) - either file OR url required"),
    url: str = Form(None, description="URL of document file to convert - either file OR url required"),
    include_metadata: bool = Form(True, description="Whether to include document metadata in output"),
    preserve_formatting: bool = Form(True, description="Whether to preserve document formatting like tables and lists"),
    cookies_url: str = Form(None, description="URL to download cookies file for YouTube/restricted content access"),
    _: str = Depends(get_api_key),  # API key validation
):
    """
    Convert documents to Markdown format using Microsoft MarkItDown.
    
    This endpoint converts various document formats (PDF, Word, Excel, PowerPoint, etc.)
    to Markdown format while preserving structure, tables, and formatting.
    
    **Supported Formats:**
    - PDF documents (.pdf)
    - Microsoft Word (.docx, .doc)
    - Microsoft Excel (.xlsx, .xls)
    - Microsoft PowerPoint (.pptx, .ppt)
    - HTML documents (.html, .htm)
    - Plain text files (.txt)
    - Images with text (.jpg, .png, .gif) - OCR extraction
    - Audio files (.mp3, .wav, .m4a, .aac, .flac, .ogg) - Speech transcription
    - Video files (.mp4, .avi, .mov, .mkv, .webm) - Speech transcription
    
    **Features:**
    - Structure preservation (headers, lists, tables)
    - Token-efficient output optimized for LLM processing
    - Metadata extraction (when available)
    - Table conversion to Markdown format
    - Image OCR for text extraction
    
    **Processing:**
    This is an asynchronous operation. Use the returned job_id to poll for results.
    Processing time varies based on document size and complexity.
    
    Args:
        file: Document file upload (alternative to url)
        url: URL of document file to convert (alternative to file)
        include_metadata: Include document metadata in the result
        preserve_formatting: Preserve document structure and formatting
        
    Returns:
        JobResponse with job_id for tracking the conversion progress
        
    Raises:
        HTTPException: If parameters are invalid or service unavailable
    """
    try:
        # Check if MarkItDown service is available
        if not markitdown_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="MarkItDown service is not available. Please install 'markitdown[all]' package."
            )
        
        # Validate input parameters
        if not file and not url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either file or url parameter must be provided"
            )
        
        if file and url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Provide either file or url, not both"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_data = {
            "include_metadata": include_metadata,
            "preserve_formatting": preserve_formatting,
            "output_options": {},  # Future extension point
            "cookies_url": cookies_url
        }
        
        if file:
            # Handle file upload
            if file.size and file.size > 50 * 1024 * 1024:  # 50MB limit for documents
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail="File size exceeds 50MB limit"
                )
            
            # Validate file type by extension
            if file.filename:
                allowed_extensions = {
                    # Document formats
                    '.pdf', '.docx', '.doc', '.pptx', '.ppt', '.xlsx', '.xls',
                    # Text formats
                    '.txt', '.md', '.html', '.htm',
                    # Image formats (with OCR)
                    '.jpg', '.jpeg', '.png', '.gif',
                    # Audio formats (with transcription)
                    '.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg',
                    # Video formats (with transcription)
                    '.mp4', '.avi', '.mov', '.mkv', '.webm'
                }
                file_ext = os.path.splitext(file.filename.lower())[1]
                if file_ext not in allowed_extensions:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Unsupported file type '{file_ext}'. Use /formats endpoint to see supported formats."
                    )
            
            # Process file immediately instead of storing binary data in job params
            file_content = await file.read()
            job_data["input_filename"] = file.filename or "uploaded_document"
            
            # Create wrapper that processes binary data directly
            async def process_markdown_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
                return await markitdown_service.process_document_with_file_data(
                    file_content=file_content,
                    params=data
                )
        else:
            # Handle URL input
            job_data["file_url"] = url
            job_data["input_filename"] = os.path.basename(url) or "document_from_url"
            
            # Create wrapper for URL-based processing
            async def process_markdown_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
                return await markitdown_service.process_document_to_markdown(data)
        
        # Queue the job using consistent pattern (no binary data in job_data)
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.DOCUMENT_TO_MARKDOWN,
            process_func=process_markdown_wrapper,
            data=job_data  # No binary data stored here
        )
        
        logger.info(f"Created document to Markdown conversion job {job_id} for: {job_data.get('input_filename', 'unknown')}")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating document conversion job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create document conversion job"
        )


@router.get("/to-markdown/{job_id}", response_model=JobStatusResponse)
async def get_markdown_conversion_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a document to Markdown conversion job.
    
    Poll this endpoint to monitor the progress of your document conversion.
    Conversion time varies based on document size and complexity.
    
    **Processing Stages:**
    1. `pending` → Job queued for processing
    2. `processing` → Document being converted to Markdown
    3. `completed` → Conversion finished successfully
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "markdown_content": "# Document Title\\n\\nConverted content...",
        "original_filename": "document.pdf",
        "file_type": "PDF",
        "word_count": 1245,
        "character_count": 8932,
        "processing_time": 3.2,
        "metadata": {
            "title": "Document Title",
            "author": "Author Name"
        }
    }
    ```
    
    **Download Results:**
    - `markdown_content`: The converted document in Markdown format
    - `metadata`: Document metadata (if available and requested)
    - Statistics: word count, character count, processing time
    
    Args:
        job_id: The ID of the conversion job to check
        _: API key for authentication
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job_info = await job_queue.get_job_info(job_id)
        
        if not job_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        # Ensure status is a JobStatus enum
        status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
        
        return JobStatusResponse(
            job_id=job_id,
            status=status,
            result=job_info.result,
            error=job_info.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )


@router.post("/to-markdown/simple", response_model=DocumentToMarkdownResult)
async def convert_document_to_markdown_simple(
    request: DocumentToMarkdownRequest,
    _: str = Depends(get_api_key)
):
    """
    Convert documents to Markdown format (synchronous version).
    
    This endpoint provides synchronous document conversion for smaller files.
    For larger files or when you need asynchronous processing, use the main
    /to-markdown endpoint instead.
    
    **Note:** This endpoint blocks until conversion is complete, so it's only
    suitable for smaller documents (< 5MB) that convert quickly.
    
    Args:
        request: Document conversion request with URL and options
        _: API key for authentication
        
    Returns:
        DocumentToMarkdownResult: Immediate conversion result
        
    Raises:
        HTTPException: If conversion fails or service unavailable
    """
    try:
        # Check if MarkItDown service is available
        if not markitdown_service.is_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="MarkItDown service is not available. Please install 'markitdown[all]' package."
            )
        
        if not request.file_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="file_url is required for synchronous conversion"
            )
        
        # Prepare job data
        job_data = {
            "file_url": str(request.file_url),
            "include_metadata": request.include_metadata,
            "preserve_formatting": request.preserve_formatting,
            "output_options": request.output_options or {},
            "input_filename": os.path.basename(str(request.file_url)) or "document",
            "cookies_url": request.cookies_url
        }
        
        # Process synchronously
        result = await markitdown_service.process_document_to_markdown(job_data)
        
        logger.info(f"Synchronous document conversion completed for: {job_data['input_filename']}")
        
        return DocumentToMarkdownResult(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Synchronous document conversion failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document conversion failed: {str(e)}"
        )