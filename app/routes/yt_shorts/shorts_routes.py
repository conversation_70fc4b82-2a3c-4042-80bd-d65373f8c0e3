"""
Comprehensive YouTube Shorts routes with all advanced features.
"""
import logging
import uuid
from typing import Any, Optional

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field

from app.services.job_queue import job_queue, JobType
from app.services.yt_shorts.shorts_service import youtube_shorts_service
from app.models import JobResponse, JobStatusResponse, JobStatus

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/yt-shorts", tags=["yt-shorts"])

class ComprehensiveYouTubeShortsRequest(BaseModel):
    """Comprehensive YouTube Shorts generation request with all features."""
    
    # Basic parameters
    video_url: str = Field(description="YouTube video URL to generate shorts from")
    max_duration: int = Field(default=60, description="Maximum duration for the short in seconds")
    quality: str = Field(default="high", description="Video quality (low, medium, high, ultra)")
    output_format: str = Field(default="mp4", description="Output video format")
    
    # AI and cropping options
    use_ai_highlight: bool = Field(default=True, description="Use AI to detect best highlight segment")
    crop_to_vertical: bool = Field(default=True, description="Crop to vertical (9:16) format")
    speaker_tracking: bool = Field(default=True, description="Enable advanced speaker tracking")
    
    # Custom time segment (overrides AI if provided)
    custom_start_time: Optional[float] = Field(default=None, description="Custom start time in seconds")
    custom_end_time: Optional[float] = Field(default=None, description="Custom end time in seconds")
    
    # Enhancement options
    enhance_audio: bool = Field(default=True, description="Enhance audio quality for speech")
    smooth_transitions: bool = Field(default=True, description="Add smooth fade transitions")
    create_thumbnail: bool = Field(default=True, description="Create preview thumbnail")
    
    # Advanced options
    target_resolution: str = Field(default="720x1280", description="Target resolution (WxH)")
    audio_enhancement_level: str = Field(default="speech", description="Audio enhancement type (speech, music, auto)")
    face_tracking_sensitivity: str = Field(default="medium", description="Face tracking sensitivity (low, medium, high)")
    
    # Cookie support for YouTube downloads
    cookies_url: Optional[str] = Field(default=None, description="URL to download cookies file for YouTube access (required for restricted videos)")

class ComprehensiveYouTubeShortsResult(BaseModel):
    """Comprehensive YouTube Shorts generation result."""
    
    # Basic results
    url: str = Field(description="S3 URL of the generated short video")
    path: str = Field(description="S3 path of the generated short video")
    duration: float = Field(description="Duration of the generated short in seconds")
    
    # Original video info
    original_title: str = Field(description="Title of the original YouTube video")
    original_duration: float = Field(description="Duration of the original video in seconds")
    
    # Highlight information
    highlight_start: float = Field(description="Start time of the highlighted segment")
    highlight_end: float = Field(description="End time of the highlighted segment")
    ai_generated: bool = Field(description="Whether AI was used to select the highlight")
    
    # Processing information
    is_vertical: bool = Field(description="Whether the video was cropped to vertical format")
    quality: str = Field(description="Processing quality used")
    
    # Additional results
    thumbnail_url: str = Field(default=None, description="S3 URL of the preview thumbnail")
    processing_stats: dict = Field(description="Processing statistics and metadata")
    quality_check: dict = Field(description="Quality verification results")
    
    # Features used
    features_used: dict = Field(description="Features that were applied during processing")

@router.post("/create", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_comprehensive_shorts_job(request: ComprehensiveYouTubeShortsRequest):
    """
    Create a comprehensive YouTube Shorts generation job with all advanced features.
    
    This endpoint provides the full AI-Youtube-Shorts-Generator experience with:
    - Advanced speaker detection and tracking
    - Dynamic face-following crop with smooth transitions
    - AI-powered highlight extraction using GPT-4
    - Professional audio enhancement and speech optimization
    - Intelligent boundary handling and quality optimization
    - Audio-visual synchronization verification
    - Preview thumbnail generation
    
    Advanced Features:
    - Voice Activity Detection (VAD) for precise speaker identification
    - DNN-based face detection with confidence scoring
    - Audio-visual correlation for active speaker detection
    - Dynamic crop center smoothing to prevent jitter
    - Multi-quality processing with professional encoding
    - Comprehensive quality checks and verification
    
    Args:
        request: The comprehensive request with all advanced options
        
    Returns:
        A JobResponse object with the job ID and initial status
        
    Raises:
        HTTPException: If the job cannot be created or parameters are invalid
    """
    try:
        # Validate YouTube URL
        video_url = request.video_url.strip()
        if not video_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Video URL is required"
            )
        
        # Validate YouTube URL format
        youtube_domains = ["youtube.com", "youtu.be", "m.youtube.com", "www.youtube.com"]
        if not any(domain in video_url for domain in youtube_domains):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Please provide a valid YouTube URL"
            )
        
        # Validate duration
        if request.max_duration < 5 or request.max_duration > 300:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Max duration must be between 5 and 300 seconds"
            )
        
        # Validate custom time segments
        if request.custom_start_time is not None and request.custom_end_time is not None:
            if request.custom_start_time < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Custom start time cannot be negative"
                )
            if request.custom_end_time <= request.custom_start_time:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Custom end time must be greater than start time"
                )
            if request.custom_end_time - request.custom_start_time > 300:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Custom segment duration cannot exceed 5 minutes"
                )
        
        # Validate quality
        valid_qualities = ["low", "medium", "high", "ultra"]
        if request.quality not in valid_qualities:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid quality. Supported: {', '.join(valid_qualities)}"
            )
        
        # Validate output format
        valid_formats = ["mp4", "webm", "mov"]
        if request.output_format not in valid_formats:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid output format. Supported: {', '.join(valid_formats)}"
            )
        
        # Validate resolution
        try:
            width, height = map(int, request.target_resolution.split('x'))
            if width < 360 or height < 640 or width > 1920 or height > 3840:
                raise ValueError("Resolution out of range")
        except:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid resolution format. Use WxH (e.g., 720x1280)"
            )
        
        # Validate audio enhancement level
        valid_audio_levels = ["speech", "music", "auto"]
        if request.audio_enhancement_level not in valid_audio_levels:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid audio enhancement level. Supported: {', '.join(valid_audio_levels)}"
            )
        
        # Validate face tracking sensitivity
        valid_sensitivities = ["low", "medium", "high"]
        if request.face_tracking_sensitivity not in valid_sensitivities:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid face tracking sensitivity. Supported: {', '.join(valid_sensitivities)}"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_params = {
            # Basic parameters
            "video_url": video_url,
            "max_duration": request.max_duration,
            "quality": request.quality,
            "output_format": request.output_format,
            
            # AI and cropping options
            "use_ai_highlight": request.use_ai_highlight,
            "crop_to_vertical": request.crop_to_vertical,
            "speaker_tracking": request.speaker_tracking,
            
            # Custom time segments
            "custom_start_time": request.custom_start_time,
            "custom_end_time": request.custom_end_time,
            
            # Enhancement options
            "enhance_audio": request.enhance_audio,
            "smooth_transitions": request.smooth_transitions,
            "create_thumbnail": request.create_thumbnail,
            
            # Advanced options
            "target_resolution": request.target_resolution,
            "audio_enhancement_level": request.audio_enhancement_level,
            "face_tracking_sensitivity": request.face_tracking_sensitivity,
            
            # Cookie support
            "cookies_url": request.cookies_url
        }
        
        # Create wrapper function for job queue
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await youtube_shorts_service.process_shorts_job(_job_id, data)
        
        # Queue the job
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.YOUTUBE_SHORTS,
            process_func=process_wrapper,
            data=job_params
        )
        
        logger.info(f"Created comprehensive YouTube Shorts job {job_id} for: {video_url}")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating comprehensive YouTube Shorts job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create comprehensive YouTube Shorts job"
        )

@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_comprehensive_shorts_job_status(job_id: str):
    """
    Get the status of a comprehensive YouTube Shorts generation job.
    
    Args:
        job_id: The ID of the job to check
        
    Returns:
        A JobStatusResponse object with comprehensive status and results
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job_info = await job_queue.get_job_info(job_id)
        
        if not job_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        # Ensure status is a JobStatus enum
        status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
        
        return JobStatusResponse(
            job_id=job_id,
            status=status,
            result=job_info.result,
            error=job_info.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )

@router.get("/", status_code=status.HTTP_200_OK)
async def get_comprehensive_shorts_info():
    """
    Get comprehensive information about the YouTube Shorts generation endpoint.
    
    Returns:
        Dictionary with detailed endpoint information and examples
    """
    return {
        "endpoint": "/v1/yt-shorts/create",
        "method": "POST",
        "description": "Comprehensive AI-powered YouTube Shorts generation with all advanced features",
        "version": "2.0",
        "advanced_features": [
            "🎯 AI-powered highlight detection using GPT-4",
            "🔊 Voice Activity Detection (VAD) for precise speaker identification",
            "👤 DNN-based face detection with confidence scoring",
            "🎭 Audio-visual correlation for active speaker detection",
            "📱 Dynamic face-following crop with smooth transitions",
            "🎨 Professional video optimization for YouTube Shorts",
            "🔈 Advanced audio enhancement and speech optimization",
            "⚡ Real-time processing with quality verification",
            "🖼️ Automatic thumbnail generation",
            "📊 Comprehensive processing statistics and analytics"
        ],
        "supported_platforms": [
            "YouTube Shorts",
            "TikTok",
            "Instagram Reels",
            "Facebook Reels",
            "Snapchat Spotlight",
            "Pinterest Idea Pins"
        ],
        "technical_specifications": {
            "max_input_duration": "No limit (will be processed in segments)",
            "output_resolutions": ["720x1280", "1080x1920", "480x854"],
            "supported_qualities": ["low", "medium", "high", "ultra"],
            "audio_sample_rates": ["16kHz", "44.1kHz", "48kHz", "96kHz"],
            "video_codecs": ["H.264", "H.265 (HEVC)"],
            "audio_codecs": ["AAC", "MP3", "WAV"]
        },
        "parameters": {
            "basic": {
                "video_url": {
                    "type": "string",
                    "required": True,
                    "description": "YouTube video URL to process"
                },
                "max_duration": {
                    "type": "integer",
                    "default": 60,
                    "range": "5-300",
                    "description": "Maximum duration for the short"
                },
                "quality": {
                    "type": "string",
                    "default": "high",
                    "options": ["low", "medium", "high", "ultra"],
                    "description": "Processing quality level"
                }
            },
            "ai_features": {
                "use_ai_highlight": {
                    "type": "boolean",
                    "default": True,
                    "description": "Use AI to detect best highlight segment"
                },
                "speaker_tracking": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable advanced speaker detection and tracking"
                },
                "face_tracking_sensitivity": {
                    "type": "string",
                    "default": "medium",
                    "options": ["low", "medium", "high"],
                    "description": "Face detection sensitivity level"
                }
            },
            "enhancement": {
                "enhance_audio": {
                    "type": "boolean",
                    "default": True,
                    "description": "Apply audio enhancement for speech clarity"
                },
                "smooth_transitions": {
                    "type": "boolean",
                    "default": True,
                    "description": "Add smooth fade in/out transitions"
                },
                "audio_enhancement_level": {
                    "type": "string",
                    "default": "speech",
                    "options": ["speech", "music", "auto"],
                    "description": "Type of audio enhancement to apply"
                }
            },
            "customization": {
                "crop_to_vertical": {
                    "type": "boolean",
                    "default": True,
                    "description": "Crop to vertical format with face tracking"
                },
                "target_resolution": {
                    "type": "string",
                    "default": "720x1280",
                    "description": "Target output resolution"
                },
                "create_thumbnail": {
                    "type": "boolean",
                    "default": True,
                    "description": "Generate preview thumbnail"
                }
            }
        },
        "examples": [
            {
                "name": "Basic AI-Powered Short",
                "description": "Standard YouTube Shorts generation with AI highlight detection",
                "request": {
                    "video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                    "max_duration": 60,
                    "quality": "high",
                    "use_ai_highlight": True,
                    "crop_to_vertical": True
                }
            },
            {
                "name": "Advanced Speaker Tracking",
                "description": "High-quality short with advanced speaker detection",
                "request": {
                    "video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                    "max_duration": 45,
                    "quality": "ultra",
                    "speaker_tracking": True,
                    "face_tracking_sensitivity": "high",
                    "enhance_audio": True,
                    "smooth_transitions": True
                }
            },
            {
                "name": "Custom Time Segment",
                "description": "Generate short from specific time range",
                "request": {
                    "video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                    "custom_start_time": 30.0,
                    "custom_end_time": 90.0,
                    "quality": "high",
                    "target_resolution": "1080x1920",
                    "audio_enhancement_level": "speech"
                }
            }
        ],
        "processing_pipeline": [
            "1. 📥 Download YouTube video with quality selection",
            "2. 🎵 Extract and enhance audio with speech optimization",
            "3. 📝 Transcribe audio with precise timestamps",
            "4. 🤖 AI-powered highlight detection and analysis",
            "5. ✂️ Extract optimal video segment with transitions",
            "6. 👤 Advanced face detection and speaker tracking",
            "7. 📱 Dynamic vertical cropping with smooth following",
            "8. 🎨 Professional optimization for YouTube Shorts",
            "9. 🖼️ Generate preview thumbnail",
            "10. ☁️ Upload to cloud storage with verification"
        ],
        "quality_assurance": [
            "Audio-video synchronization verification",
            "Resolution and aspect ratio validation",
            "Bitrate and compression optimization",
            "File integrity and corruption checks",
            "Platform compatibility validation"
        ]
    }

@router.post("/analyze", status_code=status.HTTP_200_OK)
async def analyze_video_for_shorts(video_url: str):
    """
    Analyze a YouTube video to provide insights for shorts generation.
    
    Args:
        video_url: YouTube video URL to analyze
        
    Returns:
        Analysis results with recommendations
    """
    try:
        # This would implement video analysis logic
        # For now, return a placeholder response
        return {
            "video_url": video_url,
            "analysis": {
                "duration": "12:34",
                "speaker_count": 2,
                "face_detection_confidence": 0.85,
                "audio_quality": "good",
                "recommended_segments": [
                    {"start": 45, "end": 105, "reason": "High engagement, clear speaker"},
                    {"start": 234, "end": 289, "reason": "Emotional peak, good audio"},
                    {"start": 456, "end": 516, "reason": "Key insight, face visible"}
                ],
                "optimization_suggestions": [
                    "Enable speaker tracking for better cropping",
                    "Use audio enhancement for clarity",
                    "Consider 45-60 second segments for optimal engagement"
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"Error analyzing video: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze video"
        )