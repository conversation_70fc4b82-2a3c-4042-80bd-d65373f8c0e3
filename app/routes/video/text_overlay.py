"""
Routes for text overlay functionality.
"""
import uuid
import logging
from typing import Any
from fastapi import APIRouter, HTTPException, Depends

from app.models import (
    JobResponse, 
    JobStatusResponse, 
    TextOverlayRequest, 
    TextOverlayPresetRequest,
    TextOverlayResult,
    ModernTextOverlayRequest,
    TextOverlayPreviewResponse,
    JobType,
    JobStatus
)
from app.services.job_queue import job_queue
from app.services.video.text_overlay import text_overlay_service

logger = logging.getLogger(__name__)

router = APIRouter(tags=["videos"])


async def process_text_overlay(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """
    Process a text overlay job using the text overlay service.
    
    Args:
        job_id: The job ID
        data: Text overlay parameters
    
    Returns:
        Dict with text overlay results
    """
    return await text_overlay_service.process_text_overlay_job(job_id, data)


@router.post("/text-overlay", response_model=JobResponse)
async def create_text_overlay_job(request: TextOverlayRequest):
    """
    Create a job to add text overlay to a video.
    
    This endpoint adds customizable text overlays to videos with extensive styling options.
    Perfect for titles, subtitles, watermarks, and other text-based video enhancements.
    
    ### 🎬 Text Overlay Features:
    - **Custom positioning**: 9 position options (top-left, center, bottom-right, etc.)
    - **Advanced styling**: Font size, colors, background boxes, opacity
    - **Flexible timing**: Custom duration and positioning offsets
    - **Auto text wrapping**: Intelligent text wrapping for long content
    - **Professional quality**: FFmpeg-powered rendering
    
    ### 📝 Text Options:
    - **Font control**: Size (8-200px), color (named or hex)
    - **Background boxes**: Color, opacity, border width
    - **Positioning**: 9 preset positions + custom offsets
    - **Typography**: Line spacing, auto-wrap options
    
    ### ⚙️ Position Options:
    - `top-left`, `top-center`, `top-right`
    - `center-left`, `center`, `center-right`
    - `bottom-left`, `bottom-center`, `bottom-right`
    
    Args:
        request: Text overlay request with video URL, text, and styling options
        
    Returns:
        JobResponse with job_id that can be used to check the status of the job
    """
    try:
        # Validate text content
        if not request.text.strip():
            raise HTTPException(
                status_code=400,
                detail="Text content cannot be empty"
            )
        
        # Validate position (legacy string field support)
        valid_positions = [
            "top-left", "top-center", "top-right",
            "center-left", "center", "center-right", 
            "bottom-left", "bottom-center", "bottom-right"
        ]
        
        # Check if legacy position string is provided
        if request.options.position:
            if request.options.position not in valid_positions:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid position. Must be one of: {', '.join(valid_positions)}"
                )
        
        # Create job parameters
        job_params = {
            "video_url": str(request.video_url),
            "text": request.text,
            "options": request.options.dict(),
            "preset_used": None
        }
        
        # Create and start the job
        job_id = str(uuid.uuid4())
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.TEXT_OVERLAY,
            process_func=process_text_overlay,
            data=job_params
        )
        
        logger.info(f"Created text overlay job: {job_id}")
        
        return JobResponse(job_id=job_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/text-overlay/presets")
async def get_text_overlay_presets():
    """
    Get available text overlay presets.
    
    Returns a comprehensive list of predefined text overlay styles optimized for different use cases.
    Each preset includes a description and complete styling configuration.
    
    ### 🎨 Available Presets:
    - **title_overlay**: Large title text for video headers
    - **subtitle**: Bottom-positioned subtitle text
    - **watermark**: Subtle branding/watermark text
    - **alert**: Attention-grabbing notification style
    - **modern_caption**: Clean, contemporary caption style
    - **social_post**: Instagram/TikTok optimized text
    - **quote**: Elegant quote/testimonial presentation
    - **news_ticker**: Breaking news style banner
    
    ### 💡 Usage:
    Use preset names with the `/text-overlay/preset/{preset_name}` endpoint
    for quick, professional-looking text overlays.
    
    Returns:
        Dictionary with preset names as keys and preset configurations as values
    """
    try:
        presets = text_overlay_service.get_available_presets()
        return presets
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving presets: {str(e)}")


@router.post("/text-overlay/preset/{preset_name}", response_model=JobResponse)
async def create_text_overlay_with_preset(
    preset_name: str,
    request: TextOverlayPresetRequest
):
    """
    Add text overlay using predefined preset.
    
    Apply professional text overlays using optimized presets for common use cases.
    Presets can be customized by providing option overrides in the request.
    
    ### 🎨 Available Presets:
    - **title_overlay**: Large, prominent title text
    - **subtitle**: Standard subtitle positioning
    - **watermark**: Subtle corner watermark
    - **alert**: Eye-catching alert/notification
    - **modern_caption**: Contemporary caption style
    - **social_post**: Social media optimized
    - **quote**: Elegant quote presentation
    - **news_ticker**: News banner style
    
    ### ⚙️ Customization:
    You can override any preset option by including it in the `options` field.
    Only specified options will be overridden; others use preset defaults.
    
    ### 📝 Example Override:
    ```json
    {
        "options": {
            "font_color": "red",
            "duration": 10
        }
    }
    ```
    This would use the preset but change text color to red and duration to 10 seconds.
    
    Args:
        preset_name: Name of the preset to use
        request: Text overlay preset request with optional overrides
        
    Returns:
        JobResponse with job_id that can be used to check the status of the job
    """
    try:
        # Get available presets
        presets = text_overlay_service.get_available_presets()
        
        # Check if preset exists
        if preset_name not in presets:
            available_presets = ", ".join(presets.keys())
            raise HTTPException(
                status_code=404,
                detail=f"Preset '{preset_name}' not found. Available presets: {available_presets}"
            )
        
        # Validate text content
        if not request.text.strip():
            raise HTTPException(
                status_code=400,
                detail="Text content cannot be empty"
            )
        
        # Get the preset
        preset = presets[preset_name]
        
        # Start with preset options
        final_options = preset["options"].copy()
        
        # Override with any provided options
        if request.options:
            override_dict = request.options.dict(exclude_none=True)
            final_options.update(override_dict)
        
        # Create job parameters
        job_params = {
            "video_url": str(request.video_url),
            "text": request.text,
            "options": final_options,
            "preset_used": preset_name
        }
        
        # Create and start the job
        job_id = str(uuid.uuid4())
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.TEXT_OVERLAY,
            process_func=process_text_overlay,
            data=job_params
        )
        
        logger.info(f"Created text overlay job with preset '{preset_name}': {job_id}")
        
        return JobResponse(job_id=job_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/text-overlay/{job_id}", response_model=JobStatusResponse)
async def get_text_overlay_job_status(job_id: str):
    """
    Get the status of a text overlay job.
    
    This endpoint retrieves the current status of a text overlay job. When the job
    is completed, the `result` field will contain a TextOverlayResult object
    with the following fields:
    - `video_url`: URL to the video with text overlay
    - `duration`: Duration of the output video in seconds
    - `preset_used`: Name of the preset that was used (if applicable)
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing:
        - job_id: The ID of the job
        - status: Current job status (pending, processing, completed, failed)
        - result: If completed, contains the text overlay results (TextOverlayResult)
        - error: If failed, contains error information
    """
    # Get job status using job queue
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    # Return the current status
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/modern-text-overlay", response_model=JobResponse)
async def create_modern_text_overlay_job(request: ModernTextOverlayRequest):
    """
    Create modern text overlay with advanced features including animations, effects, and smart positioning.
    
    🎬 **Modern Text Overlay Features:**
    - **Advanced Typography**: Multiple font families, weights, letter spacing, line height
    - **Professional Effects**: Shadows, strokes, glow effects, background styling
    - **Smart Positioning**: Content-aware positioning, aspect ratio optimization
    - **Text Animations**: Fade, slide, zoom, pulse, bounce, typewriter effects
    - **Performance Optimized**: CPU-optimized processing with caching
    
    🎨 **Typography Options:**
    - Font families: `roboto`, `montserrat`, `inter` with multiple weights
    - Advanced text styling: letter spacing, line height, alignment
    - High-quality font rendering with fallback support
    
    ✨ **Advanced Effects:**
    - **Shadow Effects**: Configurable color, offset, blur
    - **Stroke/Border**: Customizable width and color
    - **Glow Effects**: Configurable size and color for neon-style text
    - **Background Styling**: Semi-transparent backgrounds with padding
    
    🎭 **Animation System:**
    - **Entrance**: `fade_in`, `slide_up`, `slide_down`, `slide_left`, `slide_right`, `zoom_in`
    - **Continuous**: `pulse`, `bounce`
    - **Special**: `typewriter` for character-by-character reveal
    - Configurable duration, delay, and easing
    
    🎯 **Smart Positioning:**
    - `smart`: Aspect ratio and content-aware positioning
    - `content_aware`: Advanced analysis for optimal text placement
    - Traditional presets: `top-left`, `center`, `bottom-right`, etc.
    
    Args:
        request: Modern text overlay request with advanced configuration
        
    Returns:
        JobResponse with job_id for status tracking
    """
    try:
        # Validate text content
        if not request.text.strip():
            raise HTTPException(status_code=400, detail="Text content cannot be empty")
        
        # Create modern job parameters
        job_params = {
            "video_url": str(request.video_url),
            "text": request.text,
            "modern_config": {
                # Basic config
                "duration": request.duration,
                "start_time": request.start_time,
                "position": request.position,
                "auto_wrap": request.auto_wrap,
                "max_width_chars": request.max_width_chars,
                
                # Advanced sections
                "typography": request.typography.dict() if request.typography else {},
                "effects": request.effects.dict() if request.effects else {},
                "animation": request.animation.dict() if request.animation else {}
            }
        }
        
        # Generate preview if requested
        if request.generate_preview:
            try:
                preview_result = await text_overlay_service.get_cached_preview(
                    str(request.video_url), 
                    request.text, 
                    job_params["modern_config"]
                )
                if preview_result:
                    job_params["preview"] = preview_result
            except Exception as e:
                logger.warning(f"Preview generation failed: {e}")
        
        # Create and start the job
        job_id = str(uuid.uuid4())
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.TEXT_OVERLAY,
            process_func=process_text_overlay,
            data=job_params
        )
        
        logger.info(f"Created modern text overlay job: {job_id}")
        
        return JobResponse(job_id=job_id)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/modern-presets")
async def get_modern_text_overlay_presets():
    """
    Get modern text overlay presets with advanced styling options.
    
    Returns comprehensive modern presets designed for:
    - **Social Media**: TikTok viral, Instagram Story optimized styles
    - **Video Platforms**: YouTube title, thumbnail-ready styling
    - **Creative**: Neon glow, futuristic effects
    - **Professional**: Clean, minimalist designs
    
    ### 🎨 Modern Preset Categories:
    
    **Social Media Optimized:**
    - `tiktok_viral`: Bold, engaging style for high TikTok engagement
    - `instagram_story`: Clean aesthetic perfect for Instagram Stories
    
    **Video Platform Ready:**
    - `youtube_title`: Eye-catching titles for YouTube thumbnails
    
    **Creative & Artistic:**
    - `neon_glow`: Futuristic neon styling with glow effects
    
    Each preset includes complete typography, effects, animation, and positioning configuration
    that can be customized with override parameters.
    
    Returns:
        Dictionary of modern presets with full configuration details
    """
    try:
        modern_presets = text_overlay_service.get_modern_presets()
        return {
            "presets": modern_presets,
            "total_count": len(modern_presets),
            "categories": list(set(preset.get("category", "general") for preset in modern_presets.values()))
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving modern presets: {str(e)}")


@router.post("/modern-preset/{preset_name}", response_model=JobResponse)
async def create_modern_preset_overlay(
    preset_name: str,
    video_url: str,
    text: str,
    overrides: dict = None,
    generate_preview: bool = False
):
    """
    Create text overlay using modern preset with optional customization.
    
    🎨 **Available Modern Presets:**
    - `tiktok_viral`: TikTok-optimized viral content styling
    - `instagram_story`: Instagram Story clean aesthetic
    - `youtube_title`: YouTube thumbnail eye-catching titles
    - `neon_glow`: Futuristic neon effects with glow
    
    ### ⚙️ Override Examples:
    
    **Typography Overrides:**
    ```json
    {
        "typography": {
            "font_size": 60,
            "font_color": "red"
        }
    }
    ```
    
    **Effects Overrides:**
    ```json
    {
        "effects": {
            "glow_enabled": true,
            "glow_color": "blue@0.8"
        }
    }
    ```
    
    **Animation Overrides:**
    ```json
    {
        "animation": {
            "type": "bounce",
            "duration": 2.0
        }
    }
    ```
    
    Args:
        preset_name: Name of the modern preset to use
        video_url: URL of the video to overlay text on
        text: Text content for the overlay
        overrides: Optional configuration overrides
        generate_preview: Whether to generate a preview
        
    Returns:
        JobResponse with job_id for status tracking
    """
    try:
        # Get available modern presets
        modern_presets = text_overlay_service.get_modern_presets()
        
        # Check if preset exists
        if preset_name not in modern_presets:
            available_presets = ", ".join(modern_presets.keys())
            raise HTTPException(
                status_code=404,
                detail=f"Modern preset '{preset_name}' not found. Available: {available_presets}"
            )
        
        # Validate text content
        if not text.strip():
            raise HTTPException(status_code=400, detail="Text content cannot be empty")
        
        # Create job parameters
        job_params = {
            "video_url": video_url,
            "text": text,
            "preset_used": preset_name,
            "options": overrides or {}
        }
        
        # Generate preview if requested
        if generate_preview:
            try:
                # Build config for preview
                preset_config = modern_presets[preset_name]
                preview_config = {
                    **preset_config.get('options', {}),
                    'typography': preset_config.get('typography', {}),
                    'effects': preset_config.get('effects', {}),
                    'animation': preset_config.get('animation', {})
                }
                if overrides:
                    preview_config.update(overrides)
                
                preview_result = await text_overlay_service.get_cached_preview(
                    video_url, text, preview_config
                )
                if preview_result:
                    job_params["preview"] = preview_result
            except Exception as e:
                logger.warning(f"Preview generation failed: {e}")
        
        # Create and start the job
        job_id = str(uuid.uuid4())
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.TEXT_OVERLAY,
            process_func=process_text_overlay,
            data=job_params
        )
        
        logger.info(f"Created modern preset overlay job '{preset_name}': {job_id}")
        
        return JobResponse(job_id=job_id)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/preview", response_model=TextOverlayPreviewResponse)
async def generate_text_overlay_preview(
    video_url: str,
    text: str,
    options: dict = None
):
    """
    Generate a fast preview of text overlay for real-time feedback.
    
    🎬 **Preview Features:**
    - **Fast Generation**: 3-second preview in 640x360 resolution
    - **Smart Caching**: Cached for 1 hour for better performance
    - **Real-time Feedback**: Perfect for interactive UI development
    - **Format Support**: Both legacy and modern configuration formats
    
    ### 💡 Usage Examples:
    
    **Legacy Format:**
    ```json
    {
        "font_size": 48,
        "font_color": "white",
        "position": "bottom-center"
    }
    ```
    
    **Modern Format:**
    ```json
    {
        "typography": {
            "font_family": "montserrat",
            "font_weight": "bold",
            "font_size": 52
        },
        "effects": {
            "shadow_enabled": true
        },
        "animation": {
            "type": "fade_in"
        }
    }
    ```
    
    Args:
        video_url: URL of the video for preview
        text: Text content to preview
        options: Overlay configuration (legacy or modern format)
        
    Returns:
        TextOverlayPreviewResponse with preview URL and metadata
    """
    try:
        # Validate inputs
        if not video_url.strip():
            raise HTTPException(status_code=400, detail="Video URL is required")
        if not text.strip():
            raise HTTPException(status_code=400, detail="Text content is required")
        
        # Use default options if none provided
        if not options:
            options = {"font_size": 48, "font_color": "white", "position": "bottom-center"}
        
        # Generate cached preview
        preview_result = await text_overlay_service.get_cached_preview(video_url, text, options)
        
        if not preview_result:
            raise HTTPException(status_code=500, detail="Preview generation failed")
        
        return TextOverlayPreviewResponse(
            preview_url=preview_result["preview_url"],
            duration=preview_result["duration"],
            resolution=preview_result["resolution"],
            generated_at=preview_result["generated_at"]
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Preview generation error: {str(e)}")


@router.get("/all-presets")
async def get_all_text_overlay_presets():
    """
    Get both legacy and modern text overlay presets combined.
    
    Returns comprehensive preset collection including:
    - **Legacy Presets**: Classic text overlay styles (backward compatible)
    - **Modern Presets**: Advanced styling with animations and effects
    
    ### 📚 Preset Categories:
    - **Classic**: Traditional text overlay styles
    - **Social Media**: Platform-optimized styling
    - **Video Platform**: YouTube, streaming-ready presets  
    - **Creative**: Artistic and effect-heavy styles
    - **Professional**: Business and corporate styling
    
    Each preset includes metadata about type (legacy/modern), category, and available features.
    
    Returns:
        Dictionary containing all available presets with categorization
    """
    try:
        all_presets = text_overlay_service.get_all_presets()
        
        # Categorize presets
        categories = {}
        for name, preset in all_presets.items():
            category = preset.get("category", "general")
            if category not in categories:
                categories[category] = []
            categories[category].append(name)
        
        return {
            "presets": all_presets,
            "categories": categories,
            "total_count": len(all_presets),
            "legacy_count": len([p for p in all_presets.values() if p.get("type") == "legacy"]),
            "modern_count": len([p for p in all_presets.values() if p.get("type") == "modern"])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving presets: {str(e)}")