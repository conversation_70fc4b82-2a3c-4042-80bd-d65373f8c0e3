"""
Postiz integration routes for social media scheduling.
"""
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from loguru import logger

from app.utils.auth import get_api_key
from app.services.postiz_service import postiz_service
from app.services.job_queue import job_queue
from app.models import JobStatus


router = APIRouter(prefix="/postiz", tags=["postiz"])


class PostizIntegrationResponse(BaseModel):
    """Response model for Postiz integrations."""
    id: str
    name: str
    provider: str


class SchedulePostRequest(BaseModel):
    """Request model for scheduling a post."""
    content: str = Field(..., description="Post content text")
    integrations: List[str] = Field(..., description="List of integration IDs to post to")
    post_type: str = Field(default="now", description="Post type: now, schedule, or draft")
    schedule_date: Optional[datetime] = Field(None, description="Schedule date for scheduled posts")
    tags: Optional[List[str]] = Field(None, description="Optional tags for the post")


class ScheduleJobPostRequest(BaseModel):
    """Request model for scheduling a post from a completed job."""
    job_id: str = Field(..., description="ID of the completed job")
    content: Optional[str] = Field(None, description="Custom post content (uses suggested if not provided)")
    integrations: List[str] = Field(..., description="List of integration IDs to post to")
    post_type: str = Field(default="now", description="Post type: now, schedule, or draft")
    schedule_date: Optional[datetime] = Field(None, description="Schedule date for scheduled posts")
    tags: Optional[List[str]] = Field(None, description="Optional tags for the post")


class PostizResponse(BaseModel):
    """Generic Postiz API response."""
    success: bool
    message: str
    data: Optional[dict] = None


@router.get("/integrations", response_model=List[PostizIntegrationResponse])
async def get_integrations(_: str = Depends(get_api_key)):
    """Get available Postiz social media integrations."""
    try:
        integrations = await postiz_service.get_integrations()
        return [
            PostizIntegrationResponse(
                id=integration.id,
                name=integration.name,
                provider=integration.provider
            )
            for integration in integrations
        ]
    except ValueError as e:
        # Handle configuration errors (like missing API key)
        logger.error(f"Postiz configuration error: {e}")
        raise HTTPException(status_code=500, detail=f"Postiz configuration error: {str(e)}")
    except Exception as e:
        logger.error(f"Failed to get Postiz integrations: {e}")
        # Provide more specific error message
        error_msg = str(e)
        if "401" in error_msg or "Invalid API key" in error_msg:
            raise HTTPException(status_code=500, detail="Invalid Postiz API key. Please check your POSTIZ_API_KEY environment variable.")
        elif "Connection" in error_msg or "timeout" in error_msg.lower():
            raise HTTPException(status_code=500, detail="Failed to connect to Postiz API. Please check your POSTIZ_API_URL.")
        else:
            raise HTTPException(status_code=500, detail=f"Failed to get integrations: {error_msg}")


@router.post("/schedule", response_model=PostizResponse)
async def schedule_post(request: SchedulePostRequest, _: str = Depends(get_api_key)):
    """Schedule a post to social media platforms."""
    try:
        if request.post_type == "schedule" and not request.schedule_date:
            raise HTTPException(status_code=400, detail="Schedule date is required for scheduled posts")
        
        result = await postiz_service.schedule_post(
            content=request.content,
            integrations=request.integrations,
            post_type=request.post_type,
            schedule_date=request.schedule_date,
            tags=request.tags
        )
        
        return PostizResponse(
            success=True,
            message="Post scheduled successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Failed to schedule post: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to schedule post: {str(e)}")


@router.post("/schedule-job", response_model=PostizResponse)
async def schedule_job_post(request: ScheduleJobPostRequest, _: str = Depends(get_api_key)):
    """Schedule a post from a completed job result."""
    try:
        # Get the job information
        job_info = await job_queue.get_job_info(request.job_id)
        if not job_info:
            raise HTTPException(status_code=404, detail="Job not found")
        
        status_value = job_info.status.value if isinstance(job_info.status, JobStatus) else job_info.status
        if status_value != "completed":
            raise HTTPException(status_code=400, detail="Job must be completed to schedule")
        
        # Check if job has scheduling information
        scheduling_info = None
        if isinstance(job_info.result, dict) and "scheduling" in job_info.result:
            scheduling_info = job_info.result["scheduling"]
        
        # If no explicit scheduling info, check if job has media content that can be scheduled
        if not scheduling_info or not scheduling_info.get("available"):
            # Check if this is a video/image/audio job with media content
            schedulable_operations = [
                'footage_to_video', 'aiimage_to_video', 'scenes_to_video',
                'short_video_creation', 'image_to_video', 'image_generation', 'audio_generation'
            ]
            
            has_media_content = False
            if isinstance(job_info.result, dict):
                # Check for video, image, or audio URLs
                media_keys = [
                    'final_video_url', 'video_url', 'video_with_audio_url',
                    'image_url', 'thumbnail_url', 'audio_url', 'file_url'
                ]
                has_media_content = any(
                    key in job_info.result and job_info.result[key]
                    for key in media_keys
                )
            
            # Allow scheduling if it's a schedulable operation with media content
            operation = getattr(job_info, 'job_type', '').lower()
            if not (operation in schedulable_operations and has_media_content):
                raise HTTPException(status_code=400, detail="Job is not available for scheduling")
            
            # Create temporary scheduling info for jobs without explicit metadata
            content_type = "unknown"
            if isinstance(job_info.result, dict):
                video_keys = ['final_video_url', 'video_url']
                result_dict = job_info.result  # Type narrowing for Pylance
                content_type = "video" if any(k in result_dict for k in video_keys) else "unknown"
            
            scheduling_info = {
                "available": True,
                "content_type": content_type,
                "suggested_content": f"✨ Check out this amazing {operation.replace('_', ' ')} creation! #AI #automation #creation"
            }
        
        # Use custom content or suggested content
        content = request.content
        if not content:
            content = scheduling_info.get("suggested_content", "Check out this amazing creation!")
        
        # Handle media content from job result
        media_url = None
        media_type = None
        if isinstance(job_info.result, dict):
            # Look for various possible media URL fields (prioritize video URLs)
            video_keys = ["final_video_url", "video_url", "video_with_audio_url"]
            image_keys = ["image_url", "thumbnail_url"]
            audio_keys = ["audio_url"]
            other_keys = ["file_url", "url"]
            
            # Check for video content first
            for key in video_keys:
                if key in job_info.result and job_info.result[key]:
                    media_url = job_info.result[key]
                    media_type = "video"
                    break
            
            # Then check for images
            if not media_url:
                for key in image_keys:
                    if key in job_info.result and job_info.result[key]:
                        media_url = job_info.result[key]
                        media_type = "image"
                        break
            
            # Then check for audio
            if not media_url:
                for key in audio_keys:
                    if key in job_info.result and job_info.result[key]:
                        media_url = job_info.result[key]
                        media_type = "audio"
                        break
            
            # Finally check other URLs
            if not media_url:
                for key in other_keys:
                    if key in job_info.result and job_info.result[key]:
                        media_url = job_info.result[key]
                        media_type = "unknown"
                        break
        
        if request.post_type == "schedule" and not request.schedule_date:
            raise HTTPException(status_code=400, detail="Schedule date is required for scheduled posts")
        
        # Choose the appropriate scheduling method based on media type
        if media_url and media_type == "video":
            result = await postiz_service.schedule_video_post(
                video_url=media_url,
                content=content,
                integrations=request.integrations,
                post_type=request.post_type,
                schedule_date=request.schedule_date,
                tags=request.tags
            )
        elif media_url and media_type == "image":
            # For images, download and upload as attachment
            result = await postiz_service.schedule_post(
                content=content,
                integrations=request.integrations,
                post_type=request.post_type,
                schedule_date=request.schedule_date,
                media_paths=[media_url] if media_url.startswith('http') else None,
                tags=request.tags
            )
        else:
            # For other content types or no media, use regular posting
            result = await postiz_service.schedule_post(
                content=content,
                integrations=request.integrations,
                post_type=request.post_type,
                schedule_date=request.schedule_date,
                tags=request.tags
            )
        
        return PostizResponse(
            success=True,
            message=f"Post from job {request.job_id} scheduled successfully",
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to schedule job post: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to schedule job post: {str(e)}")


@router.post("/schedule-now", response_model=PostizResponse)
async def schedule_post_now(request: SchedulePostRequest, _: str = Depends(get_api_key)):
    """Schedule a post to be published immediately."""
    try:
        result = await postiz_service.schedule_post_now(
            content=request.content,
            integrations=request.integrations,
            tags=request.tags
        )
        
        return PostizResponse(
            success=True,
            message="Post published successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Failed to publish post: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to publish post: {str(e)}")


@router.post("/create-draft", response_model=PostizResponse)
async def create_draft_post(request: SchedulePostRequest, _: str = Depends(get_api_key)):
    """Create a draft post."""
    try:
        result = await postiz_service.create_draft_post(
            content=request.content,
            integrations=request.integrations,
            tags=request.tags
        )
        
        return PostizResponse(
            success=True,
            message="Draft post created successfully",
            data=result
        )
        
    except Exception as e:
        logger.error(f"Failed to create draft: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create draft: {str(e)}")


@router.get("/job/{job_id}/scheduling-info")
async def get_job_scheduling_info(job_id: str, _: str = Depends(get_api_key)):
    """Get scheduling information for a completed job."""
    try:
        job_info = await job_queue.get_job_info(job_id)
        if not job_info:
            raise HTTPException(status_code=404, detail="Job not found")
        
        scheduling_info = None
        if isinstance(job_info.result, dict) and "scheduling" in job_info.result:
            scheduling_info = job_info.result["scheduling"]
        
        return {
            "job_id": job_id,
            "job_status": job_info.status.value if isinstance(job_info.status, JobStatus) else job_info.status,
            "scheduling_available": scheduling_info.get("available", False) if scheduling_info else False,
            "content_type": scheduling_info.get("content_type", "unknown") if scheduling_info else "unknown",
            "suggested_content": scheduling_info.get("suggested_content", "") if scheduling_info else "",
            "marked_at": scheduling_info.get("marked_at") if scheduling_info else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job scheduling info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get scheduling info: {str(e)}")