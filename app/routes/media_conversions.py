"""
Routes for media conversions.
"""
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status, Request
from app.models import JobResponse, JobStatusResponse, MediaConversionRequest
from app.services.media_conversion_service import media_conversion_service, SUPPORTED_FORMATS, QUALITY_PRESETS, detect_media_type
from app.services.job_queue import job_queue, JobType
from app.utils.auth import get_api_key
import uuid
import logging
import base64

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/conversions", tags=["conversions"])


@router.get("/formats")
async def get_supported_formats():
    """
    Get comprehensive list of all supported conversion formats with codec details.
    
    Returns detailed information about supported media formats, quality presets,
    and conversion capabilities for audio, video, and image formats.
    
    Returns:
        Dict containing:
        - supported_formats: Organized by media type (audio, video, image)
        - quality_presets: Available quality settings
        - total_formats: Total number of supported formats
        - format_list: Flat list of all supported formats
    """
    # Create a flat list of all formats for easy reference
    all_formats = []
    for media_type, formats in SUPPORTED_FORMATS.items():
        for format_name, format_info in formats.items():
            all_formats.append({
                "format": format_name,
                "media_type": media_type,
                "codec": format_info.get("codec", "unknown"),
                "description": format_info.get("description", "")
            })
    
    return {
        "object": "formats",
        "supported_formats": SUPPORTED_FORMATS,
        "quality_presets": {
            preset: info for preset, info in QUALITY_PRESETS.items()
        },
        "total_formats": sum(len(formats) for formats in SUPPORTED_FORMATS.values()),
        "format_list": all_formats,
        "media_types": list(SUPPORTED_FORMATS.keys())
    }


@router.post("/", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def convert_media(
    request: Request,
    file: UploadFile | None = File(None),
    url: str | None = Form(None), 
    output_format: str | None = Form(None),
    quality: str = Form("medium"),
    custom_options: str | None = Form(None),
    _: str = Depends(get_api_key),  # API key validation (not used in function)
):
    """
    Convert media files between formats using FFmpeg with comprehensive format support.
    
    This endpoint supports conversion between audio, video, and image formats using FFmpeg.
    Supports both file uploads and URL-based conversions with quality presets and custom options.
    
    Args:
        file: Media file to upload and convert (optional, either file or url required)
        url: URL of media file to convert (optional, either file or url required)
        output_format: Target format for conversion (e.g., 'mp3', 'mp4', 'webp')
        quality: Quality preset for conversion ('low', 'medium', 'high', 'lossless')
        custom_options: Custom FFmpeg options (e.g., '-vf scale=1280:-1')
        
    Returns:
        JobResponse with job_id for tracking the conversion progress
        
    Raises:
        HTTPException: If parameters are invalid or conversion fails
    """
    try:
        # Check if this is a JSON request for URL-based conversion
        content_type = request.headers.get("content-type", "")
        if content_type.startswith("application/json"):
            # Handle JSON request for URL-based conversion
            body = await request.json()
            json_request = MediaConversionRequest(**body)
            
            if not json_request.input_url:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="URL parameter is required for JSON requests"
                )
                
            url = str(json_request.input_url)
            output_format = json_request.output_format
            quality = json_request.quality or "medium"
            custom_options = json_request.custom_options
            file = None
        else:
            # Handle form data request
            if not file and not url:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Either 'file' or 'url' parameter is required"
                )
            
            if file and url:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Provide either 'file' or 'url', not both"
                )
            
            if not output_format:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="output_format parameter is required"
                )
        
        # Validate output format
        all_formats = set()
        for format_list in SUPPORTED_FORMATS.values():
            all_formats.update(format_list)
        
        if output_format.lower() not in all_formats:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported output format '{output_format}'. Use /formats endpoint to see supported formats."
            )
        
        # Validate quality preset
        if quality not in QUALITY_PRESETS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid quality preset '{quality}'. Supported presets: {', '.join(QUALITY_PRESETS.keys())}"
            )

        # Create job
        job_id = str(uuid.uuid4())
        
        # Handle file upload or URL
        if file:
            # For file uploads, we'll pass the file content as base64
            file_content = await file.read()
            file_data = base64.b64encode(file_content).decode('utf-8')
            
            # Detect media type from file
            input_type = detect_media_type(file.content_type or '', file.filename or '')
            if input_type == "unknown":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot determine media type from uploaded file. Please ensure the file has a proper extension and content type."
                )
            
            job_data = {
                "input_type": input_type,
                "file_data": file_data,
                "filename": file.filename,
                "content_type": file.content_type,
                "output_format": output_format.lower(),
                "quality": quality,
                "custom_options": custom_options
            }
        else:
            # For URL-based conversions - type will be detected in service
            job_data = {
                "input_type": "url", 
                "input_url": url,
                "output_format": output_format.lower(),
                "quality": quality,
                "custom_options": custom_options
            }
        
        # Create a wrapper function for URL-based processing
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await media_conversion_service.process_conversion(data)
        
        # Queue the job using consistent pattern (no binary data in job_data)
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MEDIA_CONVERSION,
            process_func=process_wrapper,
            data=job_data  # No binary data stored here
        )
        
        logger.info(f"Created media conversion job {job_id} for format: {output_format} (input: {'file' if file else 'url'})")
        
        return JobResponse(
            job_id=job_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating media conversion job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create media conversion job"
        )


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_conversion_status(job_id: str):
    """
    Get the status of a media conversion job.
    
    Args:
        job_id: The ID of the conversion job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )
