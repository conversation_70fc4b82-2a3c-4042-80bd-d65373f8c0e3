"""
Video management API endpoints.
Provides endpoints for managing persistent video records.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from fastapi.security import HTTPBearer
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
import logging

from app.utils.auth import get_api_key
from app.services.video_service import video_service
from app.database import VideoType
from app.config import (
    get_caption_style_preset, 
    get_available_caption_style_presets, 
    apply_caption_style_preset,
    get_style_recommendations,
    get_caption_best_practices
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/videos", tags=["videos"])
security = HTTPBearer()

# Pydantic models for requests/responses
class VideoInfo(BaseModel):
    id: str
    title: str
    description: Optional[str]
    video_type: str
    final_video_url: str
    video_with_audio_url: Optional[str]
    audio_url: Optional[str]
    srt_url: Optional[str] 
    thumbnail_url: Optional[str]
    duration_seconds: Optional[float]
    resolution: Optional[str]
    file_size_mb: Optional[float]
    word_count: Optional[int]
    segments_count: Optional[int]
    script_text: Optional[str]
    voice_provider: Optional[str]
    voice_name: Optional[str]
    language: Optional[str]
    processing_time_seconds: Optional[float]
    background_videos_used: Optional[List[str]]
    tags: Optional[List[str]]
    download_count: int
    last_accessed: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class VideoUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None

class VideoListResponse(BaseModel):
    videos: List[VideoInfo]
    total: int
    page: int
    limit: int

@router.get("/", response_model=VideoListResponse)
async def list_videos(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Number of videos per page"), 
    video_type: Optional[str] = Query(None, description="Filter by video type"),
    search: Optional[str] = Query(None, description="Search in title, description, and script"),
    api_key: str = Depends(get_api_key)
):
    """Get list of videos with filtering and pagination."""
    try:
        offset = (page - 1) * limit
        
        # Parse video type
        parsed_video_type = None
        if video_type:
            try:
                parsed_video_type = VideoType(video_type.lower())
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid video type: {video_type}"
                )
        
        # Get videos
        videos = await video_service.get_all_videos(
            limit=limit,
            offset=offset,
            video_type=parsed_video_type,
            search_query=search
        )
        
        # Convert to response format
        video_infos = []
        for video in videos:
            video_info = VideoInfo(
                id=video.id,
                title=video.title,
                description=video.description,
                video_type=video.video_type.value,
                final_video_url=video.final_video_url,
                video_with_audio_url=video.video_with_audio_url,
                audio_url=video.audio_url,
                srt_url=video.srt_url,
                thumbnail_url=video.thumbnail_url,
                duration_seconds=video.duration_seconds,
                resolution=video.resolution,
                file_size_mb=video.file_size_mb,
                word_count=video.word_count,
                segments_count=video.segments_count,
                script_text=video.script_text,
                voice_provider=video.voice_provider,
                voice_name=video.voice_name,
                language=video.language,
                processing_time_seconds=video.processing_time_seconds,
                background_videos_used=video.background_videos_used,
                tags=video.tags,
                download_count=video.download_count,
                last_accessed=video.last_accessed,
                created_at=video.created_at,
                updated_at=video.updated_at
            )
            video_infos.append(video_info)
        
        # Get total count (simplified for now)
        total = len(video_infos) + offset  # This is approximate
        
        return VideoListResponse(
            videos=video_infos,
            total=total,
            page=page,
            limit=limit
        )
        
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to list videos: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve videos"
        )

@router.get("/{video_id}", response_model=VideoInfo)
async def get_video(
    video_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get detailed video information by ID."""
    try:
        video = await video_service.get_video(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Update access tracking
        await video_service.update_video_access(video_id)
        
        return VideoInfo(
            id=video.id,
            title=video.title,
            description=video.description,
            video_type=video.video_type.value,
            final_video_url=video.final_video_url,
            video_with_audio_url=video.video_with_audio_url,
            audio_url=video.audio_url,
            srt_url=video.srt_url,
            thumbnail_url=video.thumbnail_url,
            duration_seconds=video.duration_seconds,
            resolution=video.resolution,
            file_size_mb=video.file_size_mb,
            word_count=video.word_count,
            segments_count=video.segments_count,
            script_text=video.script_text,
            voice_provider=video.voice_provider,
            voice_name=video.voice_name,
            language=video.language,
            processing_time_seconds=video.processing_time_seconds,
            background_videos_used=video.background_videos_used,
            tags=video.tags,
            download_count=video.download_count,
            last_accessed=video.last_accessed,
            created_at=video.created_at,
            updated_at=video.updated_at
        )
        
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to get video {video_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve video"
        )

@router.put("/{video_id}", response_model=VideoInfo)
async def update_video(
    video_id: str,
    updates: VideoUpdateRequest,
    api_key: str = Depends(get_api_key)
):
    """Update video metadata."""
    try:
        # Verify video exists
        video = await video_service.get_video(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Prepare updates
        update_data = {}
        if updates.title is not None:
            update_data['title'] = updates.title
        if updates.description is not None:
            update_data['description'] = updates.description
        if updates.tags is not None:
            update_data['tags'] = updates.tags
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No updates provided"
            )
        
        # Apply updates
        success = await video_service.update_video(video_id, update_data)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update video"
            )
        
        # Return updated video
        updated_video = await video_service.get_video(video_id)
        if not updated_video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found after update"
            )
            
        return VideoInfo(
            id=updated_video.id,
            title=updated_video.title,
            description=updated_video.description,
            video_type=updated_video.video_type.value,
            final_video_url=updated_video.final_video_url,
            video_with_audio_url=updated_video.video_with_audio_url,
            audio_url=updated_video.audio_url,
            srt_url=updated_video.srt_url,
            thumbnail_url=updated_video.thumbnail_url,
            duration_seconds=updated_video.duration_seconds,
            resolution=updated_video.resolution,
            file_size_mb=updated_video.file_size_mb,
            word_count=updated_video.word_count,
            segments_count=updated_video.segments_count,
            script_text=updated_video.script_text,
            voice_provider=updated_video.voice_provider,
            voice_name=updated_video.voice_name,
            language=updated_video.language,
            processing_time_seconds=updated_video.processing_time_seconds,
            background_videos_used=updated_video.background_videos_used,
            tags=updated_video.tags,
            download_count=updated_video.download_count,
            last_accessed=updated_video.last_accessed,
            created_at=updated_video.created_at,
            updated_at=updated_video.updated_at
        )
        
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to update video {video_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update video"
        )

@router.delete("/{video_id}")
async def delete_video(
    video_id: str,
    api_key: str = Depends(get_api_key)
):
    """Soft delete a video."""
    try:
        success = await video_service.soft_delete_video(video_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        return {"message": f"Video {video_id} deleted successfully"}
        
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to delete video {video_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete video"
        )

@router.get("/{video_id}/download")
async def download_video(
    video_id: str,
    format: str = Query("mp4", description="Download format (mp4, audio, srt)"),
    api_key: str = Depends(get_api_key)
):
    """Get download URL for video or related files."""
    try:
        video = await video_service.get_video(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Update access tracking
        await video_service.update_video_access(video_id)
        
        # Get appropriate URL based on format
        if format == "mp4":
            url = video.final_video_url
        elif format == "mp4_no_captions":
            url = video.video_with_audio_url
        elif format == "audio":
            url = video.audio_url
        elif format == "srt":
            url = video.srt_url
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid format. Supported: mp4, mp4_no_captions, audio, srt"
            )
        
        if not url:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Format '{format}' not available for this video"
            )
        
        return {"download_url": url}
        
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Failed to get download URL for video {video_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get download URL"
        )

@router.get("/stats/overview")
async def get_video_stats(api_key: str = Depends(get_api_key)):
    """Get video statistics overview."""
    try:
        stats = await video_service.get_video_stats()
        return stats
    except Exception as e:
        logger.error(f"Failed to get video stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve video statistics"
        )


# Caption Style Presets Endpoints

@router.get("/caption-styles/presets")
async def get_caption_style_presets(api_key: str = Depends(get_api_key)):
    """
    Get all available caption style presets with their default values.
    
    Returns a mapping of style names to their preset configurations including
    caption_color, font_size, font_family, and words_per_line.
    """
    try:
        available_styles = get_available_caption_style_presets()
        presets = {}
        
        for style_name in available_styles:
            try:
                preset = get_caption_style_preset(style_name)
                presets[style_name] = preset
            except KeyError:
                logger.warning(f"Style preset not found: {style_name}")
                continue
        
        return {
            "presets": presets,
            "available_styles": available_styles,
            "total_styles": len(available_styles)
        }
    except Exception as e:
        logger.error(f"Failed to get caption style presets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve caption style presets"
        )


@router.get("/caption-styles/presets/{style_name}")
async def get_caption_style_preset_details(
    style_name: str,
    api_key: str = Depends(get_api_key)
):
    """
    Get detailed preset configuration for a specific caption style.
    
    Args:
        style_name: Name of the caption style (e.g., 'viral_bounce', 'modern_neon')
        
    Returns:
        Preset configuration with caption_color, font_size, font_family, words_per_line
    """
    try:
        preset = get_caption_style_preset(style_name)
        return {
            "style_name": style_name,
            "preset": preset
        }
    except KeyError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Caption style preset '{style_name}' not found"
        )
    except Exception as e:
        logger.error(f"Failed to get caption style preset '{style_name}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve caption style preset"
        )


@router.post("/caption-styles/apply-preset")
async def apply_caption_style_preset_endpoint(
    request: Dict[str, Any],
    api_key: str = Depends(get_api_key)
):
    """
    Apply a caption style preset to current parameters, preserving user overrides.
    
    Request body:
    {
        "style_name": "viral_bounce",
        "current_params": {
            "caption_color": "#FF0000",  // user override
            "font_size": null            // will use preset value
        }
    }
    
    Returns the merged parameters with preset values applied where not overridden.
    """
    style_name = None
    try:
        style_name = request.get("style_name")
        current_params = request.get("current_params", {})
        
        if not style_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="style_name is required"
            )
        
        result_params = apply_caption_style_preset(style_name, current_params)
        
        return {
            "style_name": style_name,
            "applied_params": result_params,
            "preset_applied": True
        }
    except KeyError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Caption style preset '{style_name or 'unknown'}' not found"
        )
    except Exception as e:
        logger.error(f"Failed to apply caption style preset: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to apply caption style preset"
        )


@router.get("/caption-styles/recommendations")
async def get_style_recommendations_endpoint(
    content_type: str = Query(default="youtube_shorts", description="Content type for recommendations"),
    api_key: str = Depends(get_api_key)
):
    """
    Get caption style recommendations for specific content types.
    
    Args:
        content_type: Type of content ('tiktok_viral', 'youtube_shorts', 'instagram_reels', 
                     'professional', 'educational', 'entertainment')
        
    Returns:
        List of recommended caption styles for the content type
    """
    try:
        recommendations = get_style_recommendations(content_type)
        
        return {
            "content_type": content_type,
            "recommended_styles": recommendations,
            "total_recommendations": len(recommendations)
        }
    except Exception as e:
        logger.error(f"Failed to get style recommendations for '{content_type}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve style recommendations"
        )


@router.get("/caption-styles/best-practices")
async def get_caption_best_practices_endpoint(api_key: str = Depends(get_api_key)):
    """
    Get caption best practices and guidelines.
    
    Returns comprehensive guidelines for caption styling, positioning,
    and optimization for different content types and platforms.
    """
    try:
        best_practices = get_caption_best_practices()
        
        return {
            "best_practices": best_practices,
            "last_updated": "2025",
            "version": "2025"
        }
    except Exception as e:
        logger.error(f"Failed to get caption best practices: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve caption best practices"
        )