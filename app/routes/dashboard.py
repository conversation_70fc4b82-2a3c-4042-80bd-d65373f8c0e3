"""
Dashboard API endpoints for web UI management.
Provides stats, user management, and system information.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta
import logging

from app.utils.auth import get_api_key
from app.services.database_service import db_job_service
from app.services.api_key_service import api_key_service
from app.services.user_service import user_service
from app.services.settings_service import settings_service
from app.models import JobStatus, JobType
import os
import shutil
from pathlib import Path

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/dashboard", tags=["dashboard"])
security = HTTPBearer()

async def calculate_average_processing_time() -> float:
    """Calculate average processing time for completed jobs in seconds."""
    try:
        from app.services.database_service import db_job_service
        
        # Get completed jobs
        completed_jobs = await db_job_service.get_jobs_by_status(JobStatus.COMPLETED)
        
        if not completed_jobs:
            return 0.0
        
        total_time = 0
        valid_jobs = 0
        
        # Only process the most recent 100 jobs for performance
        recent_jobs = completed_jobs[:100]
        
        for job in recent_jobs:
            # Calculate processing time from created_at to updated_at
            try:
                created_at = datetime.fromisoformat(job.created_at.replace('Z', '+00:00'))
                updated_at = datetime.fromisoformat(job.updated_at.replace('Z', '+00:00'))
                processing_time = (updated_at - created_at).total_seconds()
                
                # Only include reasonable processing times (between 1 second and 1 hour)
                if 1 <= processing_time <= 3600:
                    total_time += processing_time
                    valid_jobs += 1
            except (ValueError, AttributeError):
                continue
        
        return total_time / valid_jobs if valid_jobs > 0 else 0.0
    except Exception as e:
        logger.error(f"Error calculating average processing time: {e}")
        return 0.0

async def calculate_storage_usage() -> tuple[Optional[float], Optional[float]]:
    """Calculate storage usage from static files and job outputs."""
    try:
        # Get storage paths from environment or use defaults
        static_path = os.getenv('STATIC_FILES_PATH', '/app/static')
        data_path = os.getenv('DATA_PATH', '/app/data')
        
        total_used = 0
        paths_to_check = [static_path, data_path]
        
        for path in paths_to_check:
            if os.path.exists(path):
                try:
                    # Calculate directory size
                    total_size = 0
                    for dirpath, dirnames, filenames in os.walk(path):
                        for filename in filenames:
                            filepath = os.path.join(dirpath, filename)
                            try:
                                total_size += os.path.getsize(filepath)
                            except (OSError, IOError):
                                continue
                    total_used += total_size
                except Exception as e:
                    logger.warning(f"Failed to calculate size for {path}: {e}")
        
        # Convert to GB
        used_gb = total_used / (1024 ** 3)
        
        # Get total disk space for the mount point
        try:
            disk_usage = shutil.disk_usage('/')
            total_gb = disk_usage.total / (1024 ** 3)
        except Exception as e:
            logger.warning(f"Failed to get total disk space: {e}")
            total_gb = 100.0  # Fallback to 100GB
        
        logger.info(f"Storage usage: {used_gb:.2f}GB of {total_gb:.2f}GB")
        return round(used_gb, 2), round(total_gb, 2)
        
    except Exception as e:
        logger.warning(f"Failed to calculate storage usage: {e}")
        return None, None

# Pydantic models for requests/responses
class DashboardStats(BaseModel):
    total_videos: int
    active_jobs: int
    completed_jobs: int
    failed_jobs: int
    total_users: int
    active_api_keys: int
    storage_used_gb: Optional[float] = None
    storage_total_gb: Optional[float] = None
    avg_processing_time_seconds: Optional[float] = None

class UserInfo(BaseModel):
    id: str
    email: str
    role: str
    created_at: datetime
    last_login: Optional[datetime]
    is_active: bool
    projects_count: int
    api_keys_count: int

class ApiKeyInfo(BaseModel):
    id: str
    name: str
    key: str
    user_id: str
    user_email: str
    is_active: bool
    created_at: datetime
    last_used: Optional[datetime]
    expires_at: Optional[datetime]
    usage_count: int
    rate_limit: int
    permissions: List[str]

class SystemSettings(BaseModel):
    auto_refresh: bool
    email_notifications: bool
    api_logging: bool
    max_concurrent_jobs: int
    default_video_resolution: str
    storage_retention_days: int

class CreateUserRequest(BaseModel):
    email: str
    role: str
    password: Optional[str] = None

class CreateApiKeyRequest(BaseModel):
    name: str
    user_id: str
    rate_limit: int = 100
    permissions: List[str] = []
    expires_at: Optional[datetime] = None

@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(api_key: str = Depends(get_api_key)):
    """Get dashboard statistics overview."""
    try:
        # Get real job statistics from database
        job_counts = await db_job_service.get_job_count_by_status()
        
        # Count completed video creation jobs specifically
        # These are the job types that actually create videos (not just process them)
        video_creation_types = {
            JobType.AIIMAGE_TO_VIDEO,
            JobType.FOOTAGE_TO_VIDEO, 
            JobType.SCENES_TO_VIDEO,
            JobType.SHORT_VIDEO_CREATION,
            JobType.IMAGE_TO_VIDEO,
            JobType.YOUTUBE_SHORTS
        }
        
        # Get count of completed video creation jobs efficiently using database query
        total_videos = await db_job_service.get_video_creation_jobs_count(video_creation_types)
        logger.info(f"Dashboard stats: Found {total_videos} completed video creation jobs")
        
        # Calculate average processing time for completed jobs
        avg_processing_time = await calculate_average_processing_time()
        
        # Calculate storage usage
        storage_used, storage_total = await calculate_storage_usage()
        
        # Get real user and API key stats
        try:
            user_stats = await user_service.get_user_stats()
            api_key_stats = await api_key_service.get_api_key_stats()
        except Exception as e:
            logger.warning(f"Failed to get user/API key stats: {e}")
            user_stats = {"total_users": 0}
            api_key_stats = {"active_keys": 0}

        stats = DashboardStats(
            total_videos=total_videos,
            active_jobs=job_counts.get(JobStatus.PROCESSING, 0) + job_counts.get(JobStatus.PENDING, 0),
            completed_jobs=job_counts.get(JobStatus.COMPLETED, 0),
            failed_jobs=job_counts.get(JobStatus.FAILED, 0),
            total_users=user_stats.get("total_users", 0),
            active_api_keys=api_key_stats.get("active_keys", 0),
            storage_used_gb=storage_used,
            storage_total_gb=storage_total,
            avg_processing_time_seconds=avg_processing_time
        )
        return stats
    except Exception as e:
        logger.error(f"Failed to get dashboard stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard statistics"
        )

class RecentActivity(BaseModel):
    id: str
    type: str
    title: str
    timestamp: str
    status: str
    details: Optional[str] = None
    operation: Optional[str] = None
    progress: Optional[int] = None

@router.get("/recent-activity", response_model=List[RecentActivity])
async def get_recent_activity(
    limit: int = 10,
    api_key: str = Depends(get_api_key)
):
    """Get recent activity from jobs and system events."""
    try:
        # Get recent jobs from database
        recent_jobs = await db_job_service.get_all_jobs(limit=limit * 2)  # Get more to filter
        
        # Define video creation job types for better activity classification
        video_creation_types = {
            JobType.AIIMAGE_TO_VIDEO,
            JobType.FOOTAGE_TO_VIDEO, 
            JobType.SCENES_TO_VIDEO,
            JobType.SHORT_VIDEO_CREATION,
            JobType.IMAGE_TO_VIDEO,
            JobType.YOUTUBE_SHORTS
        }
        
        activities = []
        # Sort jobs by most recent (updated_at or created_at) and take the most recent ones
        sorted_jobs = sorted(recent_jobs, key=lambda job: job.updated_at or job.created_at, reverse=True)
        for job in sorted_jobs[:limit]:  # Get the most recent ones
            # Determine activity type and title based on job operation
            if job.operation in video_creation_types:
                if job.status == JobStatus.COMPLETED:
                    activity_type = 'video_created'
                    title = f"Video created: {job.operation.replace('_', ' ').title()}"
                else:
                    activity_type = 'job_completed'
                    title = f"Video creation {'failed' if job.status == JobStatus.FAILED else 'processing'}: {job.operation.replace('_', ' ').title()}"
            elif job.operation == JobType.AI_SCRIPT_GENERATION:
                activity_type = 'job_completed'
                title = f"Script generation {'completed' if job.status == JobStatus.COMPLETED else 'failed'}"
            else:
                activity_type = 'job_completed'
                title = f"Job {job.operation.replace('_', ' ').title()} {'completed' if job.status == JobStatus.COMPLETED else 'failed'}"
            
            # Calculate time ago
            import datetime
            now = datetime.datetime.utcnow()
            job_time = job.updated_at if job.updated_at else job.created_at
            if isinstance(job_time, str):
                job_time = datetime.datetime.fromisoformat(job_time.replace('Z', '+00:00'))
            
            time_diff = now - job_time.replace(tzinfo=None)
            if time_diff.total_seconds() < 60:
                timestamp = "Just now"
            elif time_diff.total_seconds() < 3600:
                minutes = int(time_diff.total_seconds() / 60)
                timestamp = f"{minutes} minute{'s' if minutes != 1 else ''} ago"
            elif time_diff.total_seconds() < 86400:
                hours = int(time_diff.total_seconds() / 3600)
                timestamp = f"{hours} hour{'s' if hours != 1 else ''} ago"
            else:
                days = int(time_diff.total_seconds() / 86400)
                timestamp = f"{days} day{'s' if days != 1 else ''} ago"
            
            # Map job status to activity status
            if job.status == JobStatus.COMPLETED:
                status = 'success'
            elif job.status == JobStatus.FAILED:
                status = 'error'
            else:
                status = 'info'
            
            activities.append(RecentActivity(
                id=job.id,
                type=activity_type,
                title=title,
                timestamp=timestamp,
                status=status,
                operation=str(job.operation),
                progress=getattr(job, 'progress', None),
                details=f"Operation: {str(job.operation).replace('_', ' ').title()}" if job.operation else None
            ))
        
        return activities
    except Exception as e:
        logger.error(f"Failed to get recent activity: {e}")
        # Return empty list on error rather than failing
        return []

@router.get("/users", response_model=List[UserInfo])
async def get_users(
    page: int = 1,
    limit: int = 50,
    search: Optional[str] = None,
    role: Optional[str] = None,
    api_key: str = Depends(get_api_key)
):
    """Get list of users with filtering and pagination."""
    try:
        result = await user_service.list_users(
            page=page,
            limit=limit,
            search=search,
            role_filter=role
        )
        
        users = []
        for user_data in result["users"]:
            users.append(UserInfo(
                id=user_data["id"],
                email=user_data["email"],
                role=user_data["role"],
                created_at=datetime.fromisoformat(user_data["created_at"]),
                last_login=datetime.fromisoformat(user_data["last_login"]) if user_data["last_login"] else None,
                is_active=user_data["is_active"],
                projects_count=user_data["projects_count"],
                api_keys_count=user_data["api_keys_count"]
            ))
        
        return users
    except Exception as e:
        logger.error(f"Failed to get users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )

@router.post("/users", response_model=UserInfo)
async def create_user(
    user_data: CreateUserRequest,
    api_key: str = Depends(get_api_key)
):
    """Create a new user."""
    try:
        create_data = {
            "email": user_data.email,
            "role": user_data.role,
            "password": user_data.password or "temp_password_123"  # Generate secure password if not provided
        }
        
        result = await user_service.create_user(create_data)
        
        return UserInfo(
            id=result["id"],
            email=result["email"],
            role=result["role"],
            created_at=datetime.fromisoformat(result["created_at"]),
            last_login=datetime.fromisoformat(result["last_login"]) if result["last_login"] else None,
            is_active=result["is_active"],
            projects_count=0,
            api_keys_count=0
        )
    except Exception as e:
        logger.error(f"Failed to create user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {str(e)}"
        )

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: str,
    api_key: str = Depends(get_api_key)
):
    """Delete a user."""
    try:
        success = await user_service.delete_user(int(user_id))
        if not success:
            raise HTTPException(status_code=404, detail="User not found")
        return {"message": f"User {user_id} deleted successfully"}
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid user ID")
    except Exception as e:
        logger.error(f"Failed to delete user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )

@router.get("/api-keys", response_model=List[ApiKeyInfo])
async def get_api_keys(
    page: int = 1,
    limit: int = 50,
    search: Optional[str] = None,
    status_filter: Optional[str] = None,
    api_key: str = Depends(get_api_key)
):
    """Get list of API keys with filtering and pagination."""
    try:
        result = await api_key_service.list_api_keys(
            page=page,
            limit=limit,
            search=search,
            status_filter=status_filter
        )
        
        api_keys = []
        for key_data in result["api_keys"]:
            api_keys.append(ApiKeyInfo(
                id=key_data["key_id"],
                name=key_data["name"],
                key=key_data["key"],
                user_id=key_data["user_id"],
                user_email=key_data["user_email"],
                is_active=key_data["is_active"],
                created_at=datetime.fromisoformat(key_data["created_at"]),
                last_used=datetime.fromisoformat(key_data["last_used"]) if key_data["last_used"] else None,
                expires_at=datetime.fromisoformat(key_data["expires_at"]) if key_data["expires_at"] else None,
                usage_count=key_data["usage_count"],
                rate_limit=key_data["rate_limit"] or 100,
                permissions=key_data["permissions"]
            ))
        
        return api_keys
    except Exception as e:
        logger.error(f"Failed to get API keys: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve API keys"
        )

@router.post("/api-keys", response_model=ApiKeyInfo)
async def create_api_key(
    key_data: CreateApiKeyRequest,
    api_key: str = Depends(get_api_key)
):
    """Create a new API key."""
    try:
        create_data = {
            "name": key_data.name,
            "user_id": key_data.user_id,
            "rate_limit": key_data.rate_limit,
            "expires_at": key_data.expires_at,
            "is_active": True
        }
        
        result = await api_key_service.create_api_key(create_data)
        
        return ApiKeyInfo(
            id=result["key_id"],
            name=result["name"],
            key=result["key"],  # Full key is returned only on creation
            user_id=result["user_id"],
            user_email=result["user_email"],
            is_active=result["is_active"],
            created_at=datetime.fromisoformat(result["created_at"]),
            last_used=datetime.fromisoformat(result["last_used"]) if result["last_used"] else None,
            expires_at=datetime.fromisoformat(result["expires_at"]) if result["expires_at"] else None,
            usage_count=result["usage_count"],
            rate_limit=result["rate_limit"],
            permissions=result["permissions"]
        )
    except Exception as e:
        logger.error(f"Failed to create API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create API key: {str(e)}"
        )

@router.put("/api-keys/{key_id}", response_model=ApiKeyInfo)
async def update_api_key(
    key_id: str,
    key_data: CreateApiKeyRequest,
    api_key: str = Depends(get_api_key)
):
    """Update an API key."""
    try:
        update_data = {
            "name": key_data.name,
            "rate_limit": key_data.rate_limit,
            "expires_at": key_data.expires_at,
            "is_active": True  # Default to active for updates
        }
        
        result = await api_key_service.update_api_key(key_id, update_data)
        if not result:
            raise HTTPException(status_code=404, detail="API key not found")
        
        return ApiKeyInfo(
            id=result["key_id"],
            name=result["name"],
            key=result["key"],  # Masked key
            user_id=result["user_id"],
            user_email=result["user_email"],
            is_active=result["is_active"],
            created_at=datetime.fromisoformat(result["created_at"]),
            last_used=datetime.fromisoformat(result["last_used"]) if result["last_used"] else None,
            expires_at=datetime.fromisoformat(result["expires_at"]) if result["expires_at"] else None,
            usage_count=result["usage_count"],
            rate_limit=result["rate_limit"],
            permissions=result["permissions"]
        )
    except Exception as e:
        logger.error(f"Failed to update API key {key_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update API key: {str(e)}"
        )

@router.delete("/api-keys/{key_id}")
async def delete_api_key(
    key_id: str,
    api_key: str = Depends(get_api_key)
):
    """Delete an API key."""
    try:
        success = await api_key_service.delete_api_key(key_id)
        if not success:
            raise HTTPException(status_code=404, detail="API key not found")
        return {"message": f"API key {key_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Failed to delete API key {key_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete API key"
        )

@router.get("/settings", response_model=SystemSettings)
async def get_system_settings(api_key: str = Depends(get_api_key)):
    """Get current system settings."""
    try:
        settings_data = await settings_service.get_all_settings()
        
        settings = SystemSettings(
            auto_refresh=settings_data.get("auto_refresh", True),
            email_notifications=settings_data.get("email_notifications", True),
            api_logging=settings_data.get("api_logging", True),
            max_concurrent_jobs=settings_data.get("max_concurrent_jobs", 5),
            default_video_resolution=settings_data.get("default_video_resolution", "1080x1920"),
            storage_retention_days=settings_data.get("storage_retention_days", 90)
        )
        return settings
    except Exception as e:
        logger.error(f"Failed to get settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve settings"
        )

@router.put("/settings", response_model=SystemSettings)
async def update_system_settings(
    settings: SystemSettings,
    api_key: str = Depends(get_api_key)
):
    """Update system settings."""
    try:
        # Convert Pydantic model to dict
        settings_dict = {
            "auto_refresh": settings.auto_refresh,
            "email_notifications": settings.email_notifications,
            "api_logging": settings.api_logging,
            "max_concurrent_jobs": settings.max_concurrent_jobs,
            "default_video_resolution": settings.default_video_resolution,
            "storage_retention_days": settings.storage_retention_days
        }
        
        # Update settings in database
        success = await settings_service.update_settings(settings_dict, updated_by="admin")
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save settings to database"
            )
        
        logger.info(f"Successfully updated system settings: {settings_dict}")
        return settings
    except Exception as e:
        logger.error(f"Failed to update settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update settings"
        )

@router.get("/system-info")
async def get_system_info(api_key: str = Depends(get_api_key)):
    """Get system information and health status."""
    try:
        system_info = await settings_service.get_system_info()
        return system_info
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system information"
        )