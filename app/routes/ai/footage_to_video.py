import uuid
from typing import Any
from fastapi import APIRouter, HTTPException, Depends
from app.models import (
    JobResponse, JobStatusResponse, JobType, JobStatus,
    FootageToVideoRequest, FootageToVideoResult
)
from app.services.job_queue import job_queue
from app.services.ai.unified_video_pipeline import unified_video_pipeline
from app.services.ai.topic_discovery_service import topic_discovery_service
from app.utils.auth import get_api_key
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/ai", tags=["AI Video Generation"])


async def process_unified_video_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for unified video pipeline job processing."""
    return await unified_video_pipeline.process_unified_video_generation(data)


@router.post("/footage-to-video", response_model=JobResponse)
async def generate_video_from_topic(
    request: FootageToVideoRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate a complete video from a topic using end-to-end AI pipeline.
    
    Creates an asynchronous job that performs the complete footage-to-video workflow:
    1. **Script Generation**: AI creates engaging script from your topic
    2. **Audio Generation**: Text-to-speech converts script to narration
    3. **Visual Content**: Finds background videos OR generates AI images based on media_type
    4. **Video Composition**: Automatically syncs visuals with audio timing
    5. **Caption Addition**: Adds styled captions with modern effects
    6. **Final Rendering**: Produces ready-to-publish video content
    
    **Perfect for:**
    - YouTube Shorts automation
    - Social media content creation
    - Educational video production
    - Marketing video generation
    - Viral content pipelines
    
    **Media Types Available:**
    - `media_type: "video"` → Uses stock videos from Pexels/Pixabay
    - `media_type: "image"` → Generates AI images and creates motion effects
    
    **When using `media_type: "image"`:**
    - Automatically routes to AI image generation pipeline
    - Creates custom images perfectly matched to your script
    - Applies Ken Burns effects and motion to make images cinematic
    - Better visual consistency and topic alignment than stock footage
    
    **AI Models Used:**
    - Script: OpenAI GPT-4o / Groq Mixtral-8x7b
    - Video Search: AI visual concept extraction
    - AI Images: Together.ai FLUX / Pollinations.ai / Flux
    - Background Videos: Pexels/Pixabay API integration
    - Voice: Kokoro TTS / Microsoft Edge TTS
    
    **Auto-Topic Discovery:**
    Set `auto_topic: true` to automatically discover trending topics based on `script_type`.
    When enabled, the `topic` parameter becomes optional and the system will:
    - Search for trending news and content related to the script type
    - Generate appropriate topics using Google Search or Perplexity
    - Create videos about current events and trending subjects
    
    **Output Specifications:**
    - Format: MP4 with H.264 codec
    - Resolution: Customizable (default 1920x1080)
    - Audio: High-quality TTS narration
    - Captions: Modern TikTok-style effects
    - Duration: Matches script length (typically 20-120 seconds)
    
    **Processing Time:** 
    - Video mode: 2-5 minutes
    - Image mode: 3-8 minutes (AI image generation takes longer)
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    return await process_footage_to_video_request(request)


async def process_footage_to_video_request(request: FootageToVideoRequest) -> JobResponse:
    """Process a validated footage-to-video request."""
    job_id = str(uuid.uuid4())
    
    try:
        logger.info(f"Topic-to-video request received: {request.model_dump()}")
        
        # Validate input: either topic provided or auto_topic enabled or custom_script provided
        if not request.auto_topic and not request.topic and not request.custom_script:
            raise HTTPException(
                status_code=400,
                detail="Either 'topic' must be provided or 'auto_topic' must be set to true or 'custom_script' must be provided"
            )
        
        # Prepare data for job processing
        # Note: Auto-topic discovery will be done during job processing to avoid timeouts
        job_data = request.model_dump()
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.FOOTAGE_TO_VIDEO,
            process_func=process_unified_video_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create footage-to-video job: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to create footage-to-video job: {str(e)}")


@router.get("/footage-to-video/{job_id}", response_model=JobStatusResponse)
async def get_footage_to_video_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a footage-to-video generation job.
    
    Poll this endpoint to monitor the progress of your video generation.
    The pipeline involves multiple AI processing steps, so please allow
    several minutes for completion.
    
    **Processing Stages:**
    1. `pending` → Job queued
    2. `processing` → Pipeline running (script → audio → videos → composition)
    3. `completed` → Video ready with all assets
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "final_video_url": "https://s3.../final_video.mp4",
        "video_with_audio_url": "https://s3.../video_no_captions.mp4",
        "script_generated": "Amazing facts you didn't know...",
        "audio_url": "https://s3.../narration.wav", 
        "background_videos_used": ["https://pexels.../video1.mp4", "..."],
        "srt_url": "https://s3.../captions.srt",
        "video_duration": 45.2,
        "processing_time": 180.5,
        "word_count": 142,
        "segments_count": 15
    }
    ```
    
    **Download Links:**
    - `final_video_url`: Your finished video with captions ready for upload
    - `video_with_audio_url`: Video with audio but without captions (clean version)
    - `audio_url`: Just the narration audio file
    - `srt_url`: Caption file for manual editing (if captions enabled)
    
    **Metadata:**
    - `processing_time`: Total pipeline execution time in seconds
    - `word_count`: Words in generated script
    - `segments_count`: Number of background video segments used
    - `background_videos_used`: Source URLs of background footage
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )