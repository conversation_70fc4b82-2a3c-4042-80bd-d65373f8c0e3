import uuid
from typing import Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from app.models import (
    JobResponse, JobStatusResponse, JobType, JobStatus,
    AIScriptGenerationRequest, AIScriptGenerationResult
)
from app.services.job_queue import job_queue
from app.services.ai.script_generator import script_generator
from app.services.ai.topic_discovery_service import topic_discovery_service
from app.services.ai.news_research_service import NewsResearchService
from app.utils.auth import get_api_key

router = APIRouter(prefix="/v1/ai", tags=["AI Script Generation"])

# Request models
class NewsResearchRequest(BaseModel):
    searchTerm: str
    targetLanguage: str = "en"
    maxResults: int = 5

class ResearchTopicRequest(BaseModel):
    """Frontend-compatible research topic request model."""
    searchTerm: str
    targetLanguage: str = "en"

class FrontendScriptGenerationRequest(BaseModel):
    """Frontend-compatible script generation request model."""
    topic: str
    script_type: str = "facts"
    language: str = "en"
    max_duration: int = 60  # Changed from target_duration to match frontend
    style: str = "engaging"


# Initialize services
news_research_service = NewsResearchService()

async def process_script_generation_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for script generation job processing."""
    return await script_generator.generate_script(data)


@router.post("/script-generation", response_model=JobResponse)
@router.post("/script/generate", response_model=JobResponse)  # Alternative endpoint for frontend compatibility
async def generate_script(
    request: FrontendScriptGenerationRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate a video script from a topic using AI.
    
    Creates an asynchronous job to generate a script optimized for short-form video content
    like YouTube Shorts. Supports multiple AI providers (OpenAI GPT-4o, Groq Mixtral-8x7b)
    with automatic fallback based on API key availability.
    
    **Features:**
    - Multiple script types: facts, story, educational
    - Customizable duration and word count targets
    - Multi-language support (30+ languages)
    - Dual AI provider support with smart selection
    - Auto-topic discovery using trending web search
    - Optimized prompts for viral content creation
    - Robust error handling and JSON parsing
    
    **Auto-Topic Discovery:**
    Set `auto_topic: true` to automatically discover trending topics based on `script_type`.
    When enabled, the `topic` parameter becomes optional and the system will:
    - Search for trending news and content related to the script type
    - Generate appropriate topics using Google Search or Perplexity
    - Adapt topics to match the specified script style
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        # Validate input
        if not request.topic:
            raise HTTPException(
                status_code=400, 
                detail="Topic is required"
            )
        
        # Map frontend request to backend format
        # Map language code to full language name for script generation
        language_mapping = {
            'en': 'english',
            'fr': 'french', 
            'es': 'spanish',
            'de': 'german',
            'it': 'italian',
            'pt': 'portuguese',
            'ru': 'russian',
            'zh': 'chinese',
            'ja': 'japanese',
            'ko': 'korean',
            'ar': 'arabic',
            'hi': 'hindi',
            'th': 'thai',
            'vi': 'vietnamese',
            'pl': 'polish',
            'nl': 'dutch',
        }
        script_language = language_mapping.get(request.language.lower(), request.language)
        
        job_data = {
            "topic": request.topic,
            "script_type": request.script_type,
            "language": script_language,  # Use mapped language name
            "max_duration": request.max_duration,  # Direct mapping now
            "target_words": int(request.max_duration * 2.8),  # Estimate words from duration
            "provider": "auto",
            "auto_topic": False
        }
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.AI_SCRIPT_GENERATION,
            process_func=process_script_generation_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create script generation job: {str(e)}")


@router.get("/script-generation/{job_id}", response_model=JobStatusResponse)
@router.get("/script/generate/{job_id}", response_model=JobStatusResponse)  # Alternative endpoint for frontend compatibility
async def get_script_generation_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a script generation job.
    
    Poll this endpoint to check the progress of your script generation job.
    When completed, the result will include the generated script, word count,
    estimated duration, and AI provider information.
    
    **Job States:**
    - `pending`: Job is queued for processing
    - `processing`: Script is being generated by AI
    - `completed`: Script generation finished successfully
    - `failed`: Generation failed (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "script": "Generated script text...",
        "word_count": 142,
        "estimated_duration": 50.7,
        "provider_used": "groq",
        "model_used": "mixtral-8x7b-32768"
    }
    ```
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )


async def process_news_research_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for news research job processing."""
    search_term = data.get('searchTerm', '')
    max_results = data.get('maxResults', 5)
    
    result = await news_research_service.research_topic(search_term, max_results)
    return result




@router.post("/news-research", response_model=JobResponse)
async def research_topic(
    request: NewsResearchRequest,
    _: str = Depends(get_api_key)
):
    """
    Research a topic using news APIs to gather recent information.
    
    Creates an asynchronous job to research the specified topic using Google Search
    and Perplexity APIs. Returns recent news articles, summaries, and structured data
    that can be used for content creation.
    
    **Features:**
    - Real-time news research using Google Search API
    - Fallback to Perplexity API for enhanced results
    - Configurable maximum number of results
    - Multi-language support
    - Recent news focus (last 7 days)
    - Structured output with summaries and source information
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        # Prepare data for job processing
        job_data = request.dict()
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.RESEARCH_NEWS,  # We'll need to add this to JobType enum
            process_func=process_news_research_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create research job: {str(e)}")


@router.get("/news-research/{job_id}", response_model=JobStatusResponse)
async def get_research_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a news research job.
    
    Poll this endpoint to check the progress of your news research job.
    When completed, the result will include articles, summaries, and source information.
    
    **Job States:**
    - `pending`: Job is queued for processing
    - `processing`: Research is being conducted
    - `completed`: Research finished successfully  
    - `failed`: Research failed (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "articles": [...],
        "summary": "Overall topic summary...",
        "sources": [...],
        "research_date": "2024-01-15",
        "topic": "searched topic"
    }
    ```
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/research-topic", response_model=JobResponse)
async def research_topic_frontend(
    request: ResearchTopicRequest,
    _: str = Depends(get_api_key)
):
    """
    Research a topic for frontend compatibility.
    
    Creates an asynchronous job to research the specified topic using news APIs.
    This endpoint is designed to match the frontend API expectations.
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        # Map frontend request to backend format
        job_data = {
            "searchTerm": request.searchTerm,
            "targetLanguage": request.targetLanguage,
            "maxResults": 5
        }
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.RESEARCH_NEWS,
            process_func=process_news_research_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create research job: {str(e)}")


@router.get("/research-topic/{job_id}", response_model=JobStatusResponse)
async def get_research_topic_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a research topic job for frontend compatibility.
    
    Poll this endpoint to check the progress of your research job.
    The result format matches frontend expectations.
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/script/generate/sync")
async def generate_script_sync(
    request: FrontendScriptGenerationRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate a video script synchronously from a topic using AI.
    
    This endpoint provides immediate script generation without the need for job polling.
    Perfect for frontend interfaces that need instant results.
    
    **Features:**
    - Immediate response (no job queue)
    - Multiple script types: facts, story, educational, motivation, etc.
    - Customizable duration and word count targets
    - Multi-language support (30+ languages)
    - Dual AI provider support with smart selection
    - Optimized prompts for viral content creation
    
    **Returns:** Direct script generation result with metadata.
    """
    try:
        # Validate input
        if not request.topic:
            raise HTTPException(
                status_code=400, 
                detail="Topic is required"
            )
        
        # Map frontend request to backend format
        # Map language code to full language name for script generation
        language_mapping = {
            'en': 'english',
            'fr': 'french', 
            'es': 'spanish',
            'de': 'german',
            'it': 'italian',
            'pt': 'portuguese',
            'ru': 'russian',
            'zh': 'chinese',
            'ja': 'japanese',
            'ko': 'korean',
            'ar': 'arabic',
            'hi': 'hindi',
            'th': 'thai',
            'vi': 'vietnamese',
            'pl': 'polish',
            'nl': 'dutch',
        }
        script_language = language_mapping.get(request.language.lower(), request.language)
        
        script_data = {
            "topic": request.topic,
            "script_type": request.script_type,
            "language": script_language,  # Use mapped language name
            "max_duration": request.max_duration,  # Direct mapping now
            "target_words": int(request.max_duration * 2.8),
            "provider": "auto",
            "auto_topic": False
        }
        
        # Generate script directly using the service
        result = await script_generator.generate_script(script_data)
        
        return {
            "success": True,
            "data": result
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate script: {str(e)}")
