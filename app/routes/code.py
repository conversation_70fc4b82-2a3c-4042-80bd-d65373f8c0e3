"""
Routes for code execution.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from app.models import CodeExecutionRequest, JobResponse, JobStatusResponse
from app.services.code_execution_service import code_execution_service
from app.services.job_queue import job_queue
from app.utils.auth import get_api_key
import uuid
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/code", tags=["code"])


@router.post("/execute/python", response_model=JobResponse)
async def execute_python(
    request: CodeExecutionRequest,
    api_key: str = Depends(get_api_key),
):
    """
    Execute Python code.
    """
    try:
        job_id = str(uuid.uuid4())
        result = await code_execution_service.execute_python(
            job_id=job_id,
            code=request.code,
            timeout=request.timeout or 30,
        )
        return JobResponse(job_id=result["job_id"])
    except Exception as e:
        logger.error(f"Failed to create code execution job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/execute/python/{job_id}", response_model=JobStatusResponse)
async def get_execution_status(job_id: str, api_key: str = Depends(get_api_key)):
    """
    Get the status of a Python code execution job.
    
    Args:
        job_id: The ID of the job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )
