"""
MCP (Model Context Protocol) server endpoints for AI agent video creation.
"""

import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import StreamingResponse
from sse_starlette import EventSourceResponse
from pydantic import BaseModel, Field

from app.utils.auth import get_api_key
from app.services.job_queue import job_queue
from app.services.ai.short_video_creation import short_video_service
from app.services.ai.scenes_video_service import ScenesVideoService
from app.models import JobType, JobStatus
from app.utils.logging import get_logger

logger = get_logger()

router = APIRouter(prefix="/mcp", tags=["mcp"])

# MCP Protocol Models
class MCPRequest(BaseModel):
    jsonrpc: str = "2.0"
    id: Optional[str] = None
    method: str
    params: Optional[Dict[str, Any]] = None

class MCPResponse(BaseModel):
    jsonrpc: str = "2.0"
    id: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None

class MCPTool(BaseModel):
    name: str
    description: str
    input_schema: Dict[str, Any]

class CreateVideoParams(BaseModel):
    scenes: List[Dict[str, Any]]
    voice_provider: str = "kokoro"
    voice_name: str = "af_bella"
    language: str = "en"
    background_music: Optional[str] = None
    resolution: str = "1080x1920"
    fps: int = 30
    caption_style: Optional[str] = None
    caption_color: Optional[str] = None
    caption_position: Optional[str] = None

class VideoStatusParams(BaseModel):
    job_id: str

# MCP Tools Configuration
MCP_TOOLS = [
    MCPTool(
        name="create-short-video",
        description="Create a short-form video from scenes with AI-powered content generation",
        input_schema={
            "type": "object",
            "properties": {
                "scenes": {
                    "type": "array",
                    "description": "List of video scenes with text, duration, and search terms",
                    "items": {
                        "type": "object",
                        "properties": {
                            "text": {"type": "string", "description": "Scene narration text"},
                            "duration": {"type": "number", "description": "Scene duration in seconds"},
                            "searchTerms": {
                                "type": "array", 
                                "items": {"type": "string"},
                                "description": "Search terms for background video"
                            }
                        },
                        "required": ["text", "duration", "searchTerms"]
                    }
                },
                "voice_provider": {
                    "type": "string", 
                    "description": "TTS provider (kokoro, edge)",
                    "default": "kokoro"
                },
                "voice_name": {
                    "type": "string",
                    "description": "Voice name for TTS",
                    "default": "af_bella"
                },
                "language": {
                    "type": "string",
                    "description": "Language code (en, es, fr, etc.)",
                    "default": "en"
                },
                "background_music": {
                    "type": "string",
                    "description": "Background music mood (optional)"
                },
                "resolution": {
                    "type": "string",
                    "description": "Video resolution",
                    "default": "1080x1920"
                },
                "fps": {
                    "type": "integer",
                    "description": "Frames per second",
                    "default": 30
                },
                "caption_style": {
                    "type": "string",
                    "description": "Caption style (classic, viral_bounce, viral_cyan, viral_yellow, viral_green, bounce, typewriter, fade_in, highlight, underline, word_by_word, modern_neon, cinematic_glow, social_pop)",
                    "default": "viral_bounce"
                },
                "caption_color": {
                    "type": "string",
                    "description": "Color for caption highlighting in viral styles (e.g., '#00FFFF', '#FFFF00', '#00FF00')"
                },
                "caption_position": {
                    "type": "string",
                    "description": "Caption position on video (top, center, bottom)",
                    "default": "center"
                }
            },
            "required": ["scenes"]
        }
    ),
    MCPTool(
        name="get-video-status",
        description="Check the status of a video creation job",
        input_schema={
            "type": "object",
            "properties": {
                "job_id": {
                    "type": "string",
                    "description": "The job ID returned from create-short-video"
                }
            },
            "required": ["job_id"]
        }
    ),
    MCPTool(
        name="list-tts-voices",
        description="List available TTS voices organized by provider and language",
        input_schema={
            "type": "object",
            "properties": {
                "provider": {
                    "type": "string",
                    "description": "Filter by TTS provider (optional)"
                },
                "language": {
                    "type": "string", 
                    "description": "Filter by language code (optional)"
                }
            }
        }
    ),
    MCPTool(
        name="validate-voice-combination",
        description="Validate if a voice and provider combination is valid",
        input_schema={
            "type": "object",
            "properties": {
                "voice_name": {
                    "type": "string",
                    "description": "Voice name to validate"
                },
                "provider": {
                    "type": "string",
                    "description": "TTS provider to validate against"
                }
            },
            "required": ["voice_name", "provider"]
        }
    ),
    MCPTool(
        name="create-scenes-video",
        description="Create a video from scenes with explicit search terms for background videos",
        input_schema={
            "type": "object",
            "properties": {
                "scenes": {
                    "type": "array",
                    "description": "List of scenes with text content and search terms for background videos",
                    "items": {
                        "type": "object",
                        "properties": {
                            "text": {"type": "string", "description": "Text content for this scene segment"},
                            "searchTerms": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Search terms for finding background video for this scene"
                            },
                            "duration": {
                                "type": "number",
                                "description": "Optional duration for this scene in seconds"
                            }
                        },
                        "required": ["text", "searchTerms"]
                    }
                },
                "config": {
                    "type": "object",
                    "description": "Configuration options for video creation",
                    "properties": {
                        "voice": {"type": "string", "description": "Voice for TTS", "default": "af_heart"},
                        "provider": {"type": "string", "description": "TTS provider", "default": "kokoro"},
                        "music": {"type": "string", "description": "Background music mood", "default": "chill"},
                        "captionPosition": {"type": "string", "description": "Caption position", "default": "bottom"},
                        "orientation": {"type": "string", "description": "Video orientation", "default": "portrait"},
                        "musicVolume": {"type": "string", "description": "Background music volume", "default": "medium"},
                        "resolution": {"type": "string", "description": "Video resolution", "default": "1080x1920"},
                        "captionStyle": {"type": "string", "description": "Caption style", "default": "viral_bounce"},
                        "captionColor": {"type": "string", "description": "Caption color override"},
                        "language": {"type": "string", "description": "Language code", "default": "en"},
                        "footageProvider": {"type": "string", "description": "Background video provider (pexels, pixabay, ai_generated)", "default": "pexels"},
                        "footageQuality": {"type": "string", "description": "Video quality for AI generation (standard, high, ultra)", "default": "high"},
                        "searchSafety": {"type": "string", "description": "Content safety level (strict, moderate, off)", "default": "moderate"}
                    }
                }
            },
            "required": ["scenes"]
        }
    )
]

# MCP Resources
MCP_RESOURCES = [
    {
        "uri": "voice-guide://providers",
        "name": "TTS Voice Provider Guide",
        "description": "Comprehensive guide to TTS voice and provider combinations",
        "mimeType": "text/markdown"
    }
]

@router.get("/sse")
async def mcp_sse_endpoint(request: Request, api_key: str = Depends(get_api_key)):
    """
    MCP Server-Sent Events endpoint for AI agents to connect.
    """
    async def event_generator():
        try:
            # Send initial connection message
            yield {
                "event": "connected",
                "data": json.dumps({
                    "jsonrpc": "2.0",
                    "method": "notifications/initialized",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {},
                            "resources": {}
                        },
                        "serverInfo": {
                            "name": "Ouinhi Video Creator",
                            "version": "1.0.0"
                        }
                    }
                })
            }
            
            # Keep connection alive
            while True:
                if await request.is_disconnected():
                    break
                
                # Send heartbeat every 30 seconds
                yield {
                    "event": "heartbeat",
                    "data": json.dumps({
                        "timestamp": datetime.now().isoformat()
                    })
                }
                
                # Wait before next heartbeat
                import asyncio
                await asyncio.sleep(30)
                
        except Exception as e:
            logger.error(f"SSE connection error: {e}")
            yield {
                "event": "error",
                "data": json.dumps({
                    "error": str(e)
                })
            }
    
    return EventSourceResponse(event_generator())

@router.post("/messages")
async def mcp_messages_endpoint(
    request: MCPRequest,
    api_key: str = Depends(get_api_key)
) -> MCPResponse:
    """
    MCP message handling endpoint for AI agents.
    """
    logger.debug(f"MCP request: {request.method}")
    
    try:
        # Handle different MCP methods
        if request.method == "initialize":
            return MCPResponse(
                id=request.id,
                result={
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {},
                        "resources": {}
                    },
                    "serverInfo": {
                        "name": "Ouinhi Video Creator",
                        "version": "1.0.0"
                    }
                }
            )
        
        elif request.method == "tools/list":
            return MCPResponse(
                id=request.id,
                result={
                    "tools": [tool.model_dump() for tool in MCP_TOOLS]
                }
            )
        
        elif request.method == "resources/list":
            return MCPResponse(
                id=request.id,
                result={
                    "resources": MCP_RESOURCES
                }
            )
        
        elif request.method == "tools/call":
            return await handle_tool_call(request)
        
        elif request.method == "resources/read":
            return await handle_resource_read(request)
        
        else:
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32601,
                    "message": f"Method not found: {request.method}"
                }
            )
    
    except Exception as e:
        logger.error(f"MCP message handling error: {e}")
        return MCPResponse(
            id=request.id,
            error={
                "code": -32603,
                "message": f"Internal error: {str(e)}"
            }
        )

async def handle_tool_call(request: MCPRequest) -> MCPResponse:
    """Handle MCP tool calls."""
    if not request.params:
        return MCPResponse(
            id=request.id,
            error={
                "code": -32602,
                "message": "Missing parameters"
            }
        )
    
    tool_name = request.params.get("name")
    arguments = request.params.get("arguments", {})
    
    if tool_name == "create-short-video":
        return await create_video_tool(request.id, arguments)
    
    elif tool_name == "get-video-status":
        return await get_video_status_tool(request.id, arguments)
    
    elif tool_name == "list-tts-voices":
        return await list_tts_voices_tool(request.id, arguments)
    
    elif tool_name == "validate-voice-combination":
        return await validate_voice_combination_tool(request.id, arguments)
    
    elif tool_name == "create-scenes-video":
        return await create_scenes_video_tool(request.id, arguments)
    
    else:
        return MCPResponse(
            id=request.id,
            error={
                "code": -32602,
                "message": f"Unknown tool: {tool_name}"
            }
        )

async def create_video_tool(request_id: str, arguments: Dict[str, Any]) -> MCPResponse:
    """Handle create-short-video tool call."""
    try:
        # Validate required parameters
        scenes = arguments.get("scenes")
        if not scenes:
            return MCPResponse(
                id=request_id,
                error={
                    "code": -32602,
                    "message": "Missing required parameter: scenes"
                }
            )
        
        # Create job ID
        job_id = str(uuid.uuid4())
        
        # Prepare video creation parameters
        video_params = {
            "scenes": scenes,
            "voice_provider": arguments.get("voice_provider", "kokoro"),
            "voice_name": arguments.get("voice_name", "af_bella"),
            "language": arguments.get("language", "en"),
            "background_music": arguments.get("background_music"),
            "resolution": arguments.get("resolution", "1080x1920"),
            "fps": arguments.get("fps", 30),
            "caption_style": arguments.get("caption_style", "viral_bounce"),
            "caption_color": arguments.get("caption_color"),
            "caption_position": arguments.get("caption_position", "center"),
            "max_duration": arguments.get("max_duration", 60)  # Default 60 seconds
        }
        
        # Create wrapper function for job queue
        async def process_video_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await short_video_service.create_short_video(data)
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.SHORT_VIDEO_CREATION,
            process_func=process_video_wrapper,
            data=video_params
        )
        
        logger.info(f"Created short video job {job_id} via MCP")
        
        return MCPResponse(
            id=request_id,
            result={
                "job_id": job_id,
                "status": "pending",
                "message": "Video creation job started successfully"
            }
        )
        
    except Exception as e:
        logger.error(f"Error creating video via MCP: {e}")
        return MCPResponse(
            id=request_id,
            error={
                "code": -32603,
                "message": f"Failed to create video: {str(e)}"
            }
        )

async def create_scenes_video_tool(request_id: str, arguments: Dict[str, Any]) -> MCPResponse:
    """Handle create-scenes-video tool call."""
    try:
        # Validate required parameters
        scenes = arguments.get("scenes")
        if not scenes:
            return MCPResponse(
                id=request_id,
                error={
                    "code": -32602,
                    "message": "Missing required parameter: scenes"
                }
            )
        
        # Create job ID
        job_id = str(uuid.uuid4())
        
        # Prepare scenes video creation parameters
        scenes_video_params = {
            "scenes": scenes,
            "config": arguments.get("config", {})
        }
        
        # Create wrapper function for job queue
        async def process_scenes_video_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            service = ScenesVideoService()
            return await service.create_video(data)
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.SCENES_TO_VIDEO,
            process_func=process_scenes_video_wrapper,
            data=scenes_video_params
        )
        
        logger.info(f"Created scenes video job {job_id} via MCP")
        
        return MCPResponse(
            id=request_id,
            result={
                "job_id": job_id,
                "status": "pending",
                "message": f"Video creation job started with {len(scenes)} scenes"
            }
        )
        
    except Exception as e:
        logger.error(f"Error creating scenes video via MCP: {e}")
        return MCPResponse(
            id=request_id,
            error={
                "code": -32603,
                "message": f"Failed to create scenes video: {str(e)}"
            }
        )

async def get_video_status_tool(request_id: str, arguments: Dict[str, Any]) -> MCPResponse:
    """Handle get-video-status tool call."""
    try:
        job_id = arguments.get("job_id")
        if not job_id:
            return MCPResponse(
                id=request_id,
                error={
                    "code": -32602,
                    "message": "Missing required parameter: job_id"
                }
            )
        
        # Get job status
        job_info = await job_queue.get_job_info(job_id)
        if not job_info:
            return MCPResponse(
                id=request_id,
                error={
                    "code": -32602,
                    "message": f"Job not found: {job_id}"
                }
            )
        
        # Prepare response
        result = {
            "job_id": job_id,
            "status": job_info.status.value,
            "progress": getattr(job_info, 'progress', 0)
        }
        
        if job_info.status == JobStatus.COMPLETED and job_info.result:
            result["result"] = job_info.result
        elif job_info.status == JobStatus.FAILED and job_info.error:
            result["error"] = job_info.error
        
        return MCPResponse(
            id=request_id,
            result=result
        )
        
    except Exception as e:
        logger.error(f"Error getting video status via MCP: {e}")
        return MCPResponse(
            id=request_id,
            error={
                "code": -32603,
                "message": f"Failed to get video status: {str(e)}"
            }
        )

async def list_tts_voices_tool(request_id: str, arguments: Dict[str, Any]) -> MCPResponse:
    """Handle list-tts-voices tool call using actual TTS service."""
    try:
        from app.services.audio.tts_service import tts_service
        
        provider_filter = arguments.get("provider")
        language_filter = arguments.get("language")
        
        # Get voices from actual TTS service
        available_voices = await tts_service.get_available_voices(provider_filter)
        
        # Transform the voices to frontend-friendly format with language grouping
        voices_by_provider = {}
        
        for provider, voice_list in available_voices.items():
            if not voice_list:
                continue
                
            # Group voices by language for this provider
            voices_by_language = {}
            
            for voice in voice_list:
                # Extract language code from locale (e.g., "en-US" -> "en")
                locale = voice.get("language", "en")
                lang_code = locale.split("-")[0] if locale else "en"
                
                # Apply language filter if specified
                if language_filter and lang_code != language_filter:
                    continue
                
                if lang_code not in voices_by_language:
                    voices_by_language[lang_code] = []
                
                # Add voice with additional metadata
                voice_entry = {
                    "name": voice.get("name", ""),
                    "display_name": voice.get("display_name", voice.get("name", "")),
                    "locale": locale,
                    "gender": voice.get("gender", "unknown")
                }
                
                voices_by_language[lang_code].append(voice_entry)
            
            if voices_by_language:
                voices_by_provider[provider] = voices_by_language
        
        # Calculate total voices
        total_voices = sum(
            len(voice_list) 
            for provider_voices in voices_by_provider.values() 
            for voice_list in provider_voices.values()
        )
        
        return MCPResponse(
            id=request_id,
            result={
                "voices": voices_by_provider,
                "providers": list(voices_by_provider.keys()),
                "total_voices": total_voices,
                "source": "tts_service"  # Indicate this is from live TTS service
            }
        )
        
    except Exception as e:
        logger.error(f"Error listing TTS voices via MCP: {e}")
        return MCPResponse(
            id=request_id,
            error={
                "code": -32603,
                "message": f"Failed to list TTS voices: {str(e)}"
            }
        )

async def validate_voice_combination_tool(request_id: str, arguments: Dict[str, Any]) -> MCPResponse:
    """Handle validate-voice-combination tool call."""
    try:
        voice_name = arguments.get("voice_name")
        provider = arguments.get("provider")
        
        if not voice_name or not provider:
            return MCPResponse(
                id=request_id,
                error={
                    "code": -32602,
                    "message": "Missing required parameters: voice_name and provider"
                }
            )
        
        # Get voices from list_tts_voices
        voices_response = await list_tts_voices_tool(request_id, {"provider": provider})
        if voices_response.error:
            return voices_response
        
        voices = voices_response.result["voices"]
        
        # Check if combination is valid
        is_valid = False
        matching_languages = []
        
        if provider in voices:
            for lang, voice_list in voices[provider].items():
                if voice_name in voice_list:
                    is_valid = True
                    matching_languages.append(lang)
        
        return MCPResponse(
            id=request_id,
            result={
                "valid": is_valid,
                "voice_name": voice_name,
                "provider": provider,
                "supported_languages": matching_languages
            }
        )
        
    except Exception as e:
        logger.error(f"Error validating voice combination via MCP: {e}")
        return MCPResponse(
            id=request_id,
            error={
                "code": -32603,
                "message": f"Failed to validate voice combination: {str(e)}"
            }
        )

async def handle_resource_read(request: MCPRequest) -> MCPResponse:
    """Handle MCP resource read requests."""
    if not request.params:
        return MCPResponse(
            id=request.id,
            error={
                "code": -32602,
                "message": "Missing parameters"
            }
        )
    
    resource_uri = request.params.get("uri")
    
    if resource_uri == "voice-guide://providers":
        # Return TTS voice provider guide
        guide_content = """# TTS Voice Provider Guide

## Kokoro TTS (Local Service)
High-quality neural voices optimized for natural speech:

### English (en)
- **af_bella**: Female, warm and friendly
- **af_nicole**: Female, professional
- **af_sarah**: Female, clear and articulate  
- **am_adam**: Male, deep and authoritative
- **am_michael**: Male, conversational
- **bf_emma**: Female, British accent
- **bf_isabella**: Female, expressive
- **bm_george**: Male, British accent
- **bm_lewis**: Male, casual

### Other Languages
- **Spanish (es)**: af_isabella
- **French (fr)**: af_emma
- **German (de)**: bf_emma
- **Japanese (ja)**: af_sarah
- **Korean (ko)**: af_bella
- **Portuguese (pt)**: af_isabella
- **Russian (ru)**: af_nicole
- **Chinese (zh)**: af_bella

## Edge TTS (Microsoft)
Cloud-based neural voices with extensive language support:

### English (en)
- **en-US-JennyNeural**: Female, friendly American
- **en-US-GuyNeural**: Male, conversational American
- **en-US-AriaNeural**: Female, news anchor style
- **en-GB-SoniaNeural**: Female, British
- **en-GB-RyanNeural**: Male, British
- **en-AU-NatashaNeural**: Female, Australian

### International
- **Spanish**: es-ES-ElviraNeural, es-MX-DaliaNeural
- **French**: fr-FR-DeniseNeural, fr-CA-SylvieNeural
- **German**: de-DE-KatjaNeural, de-DE-ConradNeural
- **Japanese**: ja-JP-NanamiNeural, ja-JP-KeitaNeural
- **Korean**: ko-KR-SunHiNeural, ko-KR-InJoonNeural
- **Portuguese**: pt-BR-FranciscaNeural, pt-PT-RaquelNeural
- **Russian**: ru-RU-SvetlanaNeural, ru-RU-DmitryNeural
- **Chinese**: zh-CN-XiaoxiaoNeural, zh-TW-HsiaoChenNeural

## Recommendations
- **Kokoro**: Best for English content, fastest processing
- **Edge TTS**: Best for multilingual content, more voice variety
"""
        
        return MCPResponse(
            id=request.id,
            result={
                "contents": [
                    {
                        "uri": resource_uri,
                        "mimeType": "text/markdown",
                        "text": guide_content
                    }
                ]
            }
        )
    
    else:
        return MCPResponse(
            id=request.id,
            error={
                "code": -32602,
                "message": f"Unknown resource: {resource_uri}"
            }
        )