"""
Routes for music generation using Meta's MusicGen model.
"""
from fastapi import APIRouter, HTTPException
from app.models import MusicGenerationRequest, JobResponse, JobStatusResponse, JobStatus
from app.services.job_queue import job_queue, JobType
from app.services.audio.music_generation import music_generation_service
import uuid
import logging
from typing import Any

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/music", tags=["audio"])


@router.post("", response_model=JobResponse)
async def create_music_generation_job(request: MusicGenerationRequest):
    """
    Create a job to generate music from a text description using MusicGen.

    This endpoint generates high-quality music from natural language descriptions
    using Meta's MusicGen model (simplified implementation).

    Args:
        request: Music generation request
            - description: Text description of the music (e.g., "lo-fi music with a soothing melody")
            - provider: Provider to use ("musicgen" only in simplified implementation)
            - duration: Duration in seconds (1-20s, default: 8)
            - model_size: Model size ("small" only)
            - output_format: Audio format ("wav" only in simplified implementation)

    Returns:
        JobResponse with job_id that can be used to check the status of the job

    Examples:
        - "lo-fi music with a soothing melody"
        - "upbeat electronic dance music"
        - "acoustic guitar melody in major key"
        - "orchestral music with strings and piano"
        - "jazz music with saxophone solo"
    """
    try:
        # Validate request
        if not request.description or not request.description.strip():
            raise HTTPException(status_code=400, detail="Description cannot be empty")
        
        if len(request.description) > 500:
            raise HTTPException(status_code=400, detail="Description exceeds maximum length of 500 characters")
        
        # Validate provider (simplified implementation only supports MusicGen)
        if request.provider.lower() not in ["musicgen"]:
            raise HTTPException(status_code=400, detail="Provider must be 'musicgen'. Lyria is not available in the simplified implementation.")

        # Validate duration for MusicGen
        if request.duration > 20:
            raise HTTPException(status_code=400, detail="MusicGen maximum duration is 20 seconds in the simplified implementation.")
        
        # Validate model size
        if request.model_size.lower() not in ["small"]:
            raise HTTPException(status_code=400, detail="Model size must be 'small'")
        
        # Validate output format
        if request.output_format.lower() not in ["wav", "mp3"]:
            raise HTTPException(status_code=400, detail="Output format must be 'wav' or 'mp3'")
        
        # Create job parameters
        job_params = {
            "description": request.description.strip(),
            "provider": request.provider.lower(),
            "duration": request.duration,
            "model_size": request.model_size.lower(),
            "output_format": request.output_format.lower()
        }
        
        # Note: Lyria-specific parameters are ignored in simplified implementation
        
        # Create a new job
        job_id = str(uuid.uuid4())
        
        # Create a wrapper function that matches job queue signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await music_generation_service.process_music_generation(_job_id, data)
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MUSIC_GENERATION,
            process_func=process_wrapper,
            data=job_params
        )
        
        logger.info(f"Created music generation job {job_id} with description: {request.description[:50]}...")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create music generation job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/info")
async def get_music_generation_info():
    """
    Get information about the music generation endpoint and its capabilities.
    
    Returns:
        Dictionary with endpoint information, supported models, formats, and examples
    """
    return {
        "endpoint": "/v1/audio/music",
        "method": "POST",
        "description": "Generate music from text descriptions using MusicGen (simplified implementation)",
        "providers": {
            "musicgen": {
                "name": "Meta MusicGen Small",
                "type": "Text-to-audio generation (short clips)",
                "supported_models": ["small"],
                "max_duration": 20,
                "supported_formats": ["wav"],
                "features": ["Fast generation", "Reliable short clips", "High-quality output"],
                "note": "Simplified implementation supports 1-20 second clips. Generation time scales with duration."
            }
        },
        "parameters": {
            "description": {
                "type": "string",
                "required": True,
                "max_length": 500,
                "description": "Text description of the music to generate"
            },
            "provider": {
                "type": "string",
                "required": False,
                "default": "musicgen",
                "options": ["musicgen"],
                "description": "Music generation provider (only MusicGen available in simplified implementation)"
            },
            "duration": {
                "type": "integer",
                "required": False,
                "default": 8,
                "min": 1,
                "max": 20,
                "description": "Duration in seconds (MusicGen: 1-20s in simplified implementation)"
            },
            "model_size": {
                "type": "string",
                "required": False,
                "default": "small",
                "options": ["small"],
                "description": "Model size to use"
            },
            "output_format": {
                "type": "string",
                "required": False,
                "default": "wav",
                "options": ["wav"],
                "description": "Output audio format (only WAV supported in simplified implementation)"
            }
        },
        "examples": [
            {
                "description": "lo-fi music with a soothing melody",
                "duration": 8,
                "model_size": "small"
            },
            {
                "description": "upbeat electronic dance music",
                "duration": 12,
                "model_size": "small"
            },
            {
                "description": "acoustic guitar melody in major key",
                "duration": 10,
                "model_size": "small"
            },
            {
                "description": "orchestral music with strings and piano",
                "duration": 15,
                "model_size": "small"
            }
        ],
        "tips": [
            "Be specific in your descriptions for better results",
            "Include genre, instruments, mood, and style",
            "Longer descriptions often produce more accurate results",
            "Consider tempo keywords like 'slow', 'fast', 'upbeat'"
        ]
    }


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_music_generation_job_status(job_id: str):
    """
    Get the status of a music generation job.
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing:
        - job_id: The ID of the job
        - status: Current job status (pending, processing, completed, failed)
        - result: If completed, contains audio_url, duration, model_used, etc.
        - error: If failed, contains error information
    """
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )