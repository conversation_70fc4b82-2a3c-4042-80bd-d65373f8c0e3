"""
Routes for music generation using Meta's MusicGen model.
"""
from fastapi import APIRouter, HTTPException
from app.models import MusicGenerationRequest, JobResponse, JobStatusResponse, JobStatus
from app.services.job_queue import job_queue, JobType
from app.services.audio.music_generation import music_generation_service
import uuid
import logging
from typing import Any

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/music", tags=["audio"])


@router.post("", response_model=JobResponse)
async def create_music_generation_job(request: MusicGenerationRequest):
    """
    Create a job to generate music from a text description using multiple providers.
    
    This endpoint generates high-quality music from natural language descriptions
    using either Meta's MusicGen or Google's Lyria models.
    
    Args:
        request: Music generation request
            - description: Text description of the music (e.g., "lo-fi music with a soothing melody")
            - provider: Provider to use ("musicgen" or "lyria", default: "musicgen")
            - duration: Duration in seconds (MusicGen: 1-30s, Lyria: 1-120s, default: 8)
            - model_size: Model size ("small" for faster generation)
            - output_format: Audio format ("wav" or "mp3")
            - bpm, density, brightness, guidance, scale: Lyria-specific parameters
            
    Returns:
        JobResponse with job_id that can be used to check the status of the job
        
    Examples:
        - "lo-fi music with a soothing melody"
        - "upbeat electronic dance music"
        - "acoustic guitar melody in major key"
        - "orchestral music with strings and piano"
        - "jazz music with saxophone solo"
    """
    try:
        # Validate request
        if not request.description or not request.description.strip():
            raise HTTPException(status_code=400, detail="Description cannot be empty")
        
        if len(request.description) > 500:
            raise HTTPException(status_code=400, detail="Description exceeds maximum length of 500 characters")
        
        # Validate provider
        if request.provider.lower() not in ["musicgen", "lyria"]:
            raise HTTPException(status_code=400, detail="Provider must be 'musicgen' or 'lyria'")
        
        # Validate duration based on provider
        if request.provider.lower() == "musicgen" and request.duration > 30:
            raise HTTPException(status_code=400, detail="MusicGen maximum duration is 30 seconds. For longer music, please use multiple generations or try Lyria provider. Note: Generation time scales with duration (15s takes ~1.5 minutes).")
        elif request.provider.lower() == "lyria" and request.duration > 120:
            raise HTTPException(status_code=400, detail="Lyria maximum duration is 120 seconds (2 minutes)")
        
        # Validate model size
        if request.model_size.lower() not in ["small"]:
            raise HTTPException(status_code=400, detail="Model size must be 'small'")
        
        # Validate output format
        if request.output_format.lower() not in ["wav", "mp3"]:
            raise HTTPException(status_code=400, detail="Output format must be 'wav' or 'mp3'")
        
        # Create job parameters
        job_params = {
            "description": request.description.strip(),
            "provider": request.provider.lower(),
            "duration": request.duration,
            "model_size": request.model_size.lower(),
            "output_format": request.output_format.lower()
        }
        
        # Add Lyria-specific parameters if using Lyria provider
        if request.provider.lower() == "lyria":
            job_params.update({
                "bpm": request.bpm,
                "density": request.density,
                "brightness": request.brightness,
                "guidance": request.guidance,
                "scale": request.scale,
                "mute_bass": request.mute_bass,
                "mute_drums": request.mute_drums,
                "only_bass_and_drums": request.only_bass_and_drums,
                "music_generation_mode": request.music_generation_mode
            })
        
        # Create a new job
        job_id = str(uuid.uuid4())
        
        # Create a wrapper function that matches job queue signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await music_generation_service.process_music_generation(_job_id, data)
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MUSIC_GENERATION,
            process_func=process_wrapper,
            data=job_params
        )
        
        logger.info(f"Created music generation job {job_id} with description: {request.description[:50]}...")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create music generation job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/info")
async def get_music_generation_info():
    """
    Get information about the music generation endpoint and its capabilities.
    
    Returns:
        Dictionary with endpoint information, supported models, formats, and examples
    """
    return {
        "endpoint": "/v1/audio/music",
        "method": "POST",
        "description": "Generate music from text descriptions using multiple providers",
        "providers": {
            "musicgen": {
                "name": "Meta MusicGen Stereo",
                "type": "Text-to-audio generation (short clips)",
                "supported_models": ["small"],
                "max_duration": 30,
                "supported_formats": ["wav", "mp3"],
                "features": ["Fast generation", "Reliable short clips", "High-quality stereo output"],
                "note": "Supports 1-30 second clips. Generation time scales with duration (8s=35s, 15s=90s). For longer music, try Lyria or multiple short generations."
            },
            "lyria": {
                "name": "Google Lyria RealTime",
                "type": "Real-time music generation",
                "supported_models": ["lyria-realtime-exp"],
                "max_duration": 120,
                "supported_formats": ["wav", "mp3"],
                "features": ["BPM control", "Density control", "Brightness control", "Musical scale selection", "Instrument control", "Generation modes"],
                "note": "Real-time streaming generation with advanced parameter controls"
            }
        },
        "parameters": {
            "description": {
                "type": "string",
                "required": True,
                "max_length": 500,
                "description": "Text description of the music to generate"
            },
            "provider": {
                "type": "string",
                "required": False,
                "default": "musicgen",
                "options": ["musicgen", "lyria"],
                "description": "Music generation provider"
            },
            "duration": {
                "type": "integer",
                "required": False,
                "default": 8,
                "min": 1,
                "max": 120,
                "description": "Duration in seconds (MusicGen: 1-30s, Lyria: 1-120s)"
            },
            "model_size": {
                "type": "string",
                "required": False,
                "default": "small",
                "options": ["small"],
                "description": "Model size to use"
            },
            "output_format": {
                "type": "string",
                "required": False,
                "default": "wav",
                "options": ["wav", "mp3"],
                "description": "Output audio format"
            },
            "bpm": {
                "type": "integer",
                "required": False,
                "default": 120,
                "min": 60,
                "max": 200,
                "description": "Beats per minute (Lyria only)"
            },
            "density": {
                "type": "float",
                "required": False,
                "default": 0.5,
                "min": 0.0,
                "max": 1.0,
                "description": "Musical note density (Lyria only)"
            },
            "brightness": {
                "type": "float",
                "required": False,
                "default": 0.5,
                "min": 0.0,
                "max": 1.0,
                "description": "Tonal brightness (Lyria only)"
            },
            "guidance": {
                "type": "float",
                "required": False,
                "default": 3.0,
                "min": 0.0,
                "max": 6.0,
                "description": "Prompt adherence (Lyria only)"
            },
            "scale": {
                "type": "string",
                "required": False,
                "default": "SCALE_UNSPECIFIED",
                "options": [
                    "SCALE_UNSPECIFIED", "C_MAJOR_A_MINOR", "D_FLAT_MAJOR_B_FLAT_MINOR",
                    "D_MAJOR_B_MINOR", "E_FLAT_MAJOR_C_MINOR", "E_MAJOR_D_FLAT_MINOR",
                    "F_MAJOR_D_MINOR", "G_FLAT_MAJOR_E_FLAT_MINOR", "G_MAJOR_E_MINOR",
                    "A_FLAT_MAJOR_F_MINOR", "A_MAJOR_G_FLAT_MINOR", "B_FLAT_MAJOR_G_MINOR",
                    "B_MAJOR_A_FLAT_MINOR"
                ],
                "description": "Musical key/mode (Lyria only)"
            },
            "mute_bass": {
                "type": "boolean",
                "required": False,
                "default": False,
                "description": "Reduces bass output (Lyria only)"
            },
            "mute_drums": {
                "type": "boolean",
                "required": False,
                "default": False,
                "description": "Reduces drums output (Lyria only)"
            },
            "only_bass_and_drums": {
                "type": "boolean",
                "required": False,
                "default": False,
                "description": "Focus on bass and drums only (Lyria only)"
            },
            "music_generation_mode": {
                "type": "string",
                "required": False,
                "default": "QUALITY",
                "options": ["QUALITY", "DIVERSITY", "VOCALIZATION"],
                "description": "Generation focus mode (Lyria only)"
            }
        },
        "examples": [
            {
                "description": "lo-fi music with a soothing melody",
                "duration": 8,
                "model_size": "small"
            },
            {
                "description": "upbeat electronic dance music",
                "duration": 15,
                "model_size": "small"
            },
            {
                "description": "acoustic guitar melody in major key",
                "duration": 10,
                "model_size": "small"
            },
            {
                "description": "orchestral music with strings and piano",
                "duration": 20,
                "model_size": "small"
            }
        ],
        "tips": [
            "Be specific in your descriptions for better results",
            "Include genre, instruments, mood, and style",
            "Longer descriptions often produce more accurate results",
            "Consider tempo keywords like 'slow', 'fast', 'upbeat'"
        ]
    }


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_music_generation_job_status(job_id: str):
    """
    Get the status of a music generation job.
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing:
        - job_id: The ID of the job
        - status: Current job status (pending, processing, completed, failed)
        - result: If completed, contains audio_url, duration, model_used, etc.
        - error: If failed, contains error information
    """
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )