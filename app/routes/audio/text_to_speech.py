"""
Routes for text to speech conversion supporting multiple TTS providers.
"""
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from app.models import TextToSpeechRequest, JobResponse, JobStatusResponse, JobStatus
from app.services.job_queue import job_queue, JobType
from app.services.audio.tts_service import tts_service
import uuid
import logging
import json
import base64
import asyncio

logger = logging.getLogger(__name__)

router = APIRouter(tags=["audio"])


@router.post("/speech")
async def create_speech_job(request: TextToSpeechRequest):
    """
    Create a job to convert text to speech using the specified TTS provider with advanced features.
    
    This endpoint supports multiple TTS providers:
    - Kokoro TTS: High-quality neural voices with advanced features
        * Voice combinations (e.g., "af_heart+af_bella")
        * Text normalization options
        * Pause tags [pause:0.5s]
        * Volume control
        * Word-level timestamps
        * Multiple audio formats
    - Edge TTS: Microsoft Edge TTS with OpenAI-compatible voices and streaming support
    
    Supports both streaming and job-based responses:
    - stream=False: Returns job_id for status polling (default)
    - stream=True: Returns streaming audio data directly
    - stream_format="sse": Returns Server-Sent Events with JSON chunks
    
    Args:
        request: Enhanced text to speech request
            
    Returns:
        JobResponse, StreamingResponse, or SSE response depending on stream settings
    """
    try:
        # Get text content (supports both 'text' and 'input' fields)
        text_content = request.get_text_content()
        
        # Validate request
        if not text_content or not text_content.strip():
            raise HTTPException(status_code=400, detail="Text content cannot be empty")
        
        if len(text_content) > 5000:
            raise HTTPException(status_code=400, detail="Text exceeds maximum length of 5000 characters")
        
        # Validate provider if specified
        if request.provider and request.provider.lower() not in ["kokoro", "edge"]:
            raise HTTPException(status_code=400, detail="Provider must be 'kokoro' or 'edge'")
        
        # Handle streaming requests
        if request.stream:
            return await handle_streaming_speech(request, text_content)
        
        # Handle regular job-based requests
        return await handle_job_based_speech(request, text_content)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create TTS job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


async def handle_streaming_speech(request: TextToSpeechRequest, text_content: str):
    """Handle streaming TTS requests."""
    provider = request.provider or "edge"  # Default to edge for streaming
    
    if provider.lower() not in ["edge"]:
        raise HTTPException(status_code=400, detail="Streaming is only supported with Edge TTS provider")
    
    try:
        if request.stream_format == "sse":
            # Server-Sent Events streaming with JSON
            async def generate_sse_stream():
                try:
                    async for chunk in tts_service.generate_speech_stream(
                        text=text_content,
                        voice=request.voice,
                        provider=provider,
                        speed=request.speed,
                        remove_filter=request.remove_filter
                    ):
                        # Base64 encode the audio chunk
                        encoded_audio = base64.b64encode(chunk).decode('utf-8')
                        
                        # Create SSE event for audio delta
                        event_data = {
                            "type": "speech.audio.delta",
                            "audio": encoded_audio
                        }
                        
                        # Format as SSE event
                        yield f"data: {json.dumps(event_data)}\n\n"
                    
                    # Send completion event
                    completion_event = {
                        "type": "speech.audio.done",
                        "usage": {
                            "input_tokens": len(text_content.split()),
                            "output_tokens": 0,
                            "total_tokens": len(text_content.split())
                        }
                    }
                    yield f"data: {json.dumps(completion_event)}\n\n"
                    
                except Exception as e:
                    logger.error(f"Error during SSE streaming: {e}")
                    error_event = {
                        "type": "error",
                        "error": str(e)
                    }
                    yield f"data: {json.dumps(error_event)}\n\n"
            
            return StreamingResponse(
                generate_sse_stream(),
                media_type='text/event-stream',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'X-Accel-Buffering': 'no'
                }
            )
        
        else:
            # Raw audio streaming (default)
            async def generate_audio_stream():
                async for chunk in tts_service.generate_speech_stream(
                    text=text_content,
                    voice=request.voice,
                    provider=provider,
                    speed=request.speed,
                    remove_filter=request.remove_filter
                ):
                    yield chunk
            
            # Determine MIME type based on response format
            mime_types = {
                "mp3": "audio/mpeg",
                "wav": "audio/wav",
                "opus": "audio/ogg",
                "aac": "audio/mp4",
                "flac": "audio/flac",
                "pcm": "audio/wav"
            }
            mime_type = mime_types.get(request.response_format.lower(), "audio/mpeg")
            
            return StreamingResponse(
                generate_audio_stream(),
                media_type=mime_type,
                headers={
                    'Content-Type': mime_type
                }
            )
    
    except Exception as e:
        logger.error(f"Streaming failed: {e}")
        raise HTTPException(status_code=500, detail=f"Streaming failed: {str(e)}")


async def handle_job_based_speech(request: TextToSpeechRequest, text_content: str):
    """Handle regular job-based TTS requests."""
    # Create job parameters with advanced features
    job_params = {
        "text": text_content,
        "voice": request.voice,
        "provider": request.provider,
        "response_format": request.response_format,
        "speed": request.speed,
        "volume_multiplier": request.volume_multiplier,
        "normalization_options": request.normalization_options.dict() if request.normalization_options else None,
        "return_timestamps": request.return_timestamps,
        "lang_code": request.lang_code,
        "voice_weights": request.voice_weights,
        "remove_filter": request.remove_filter
    }
    
    # Create a new job
    job_id = str(uuid.uuid4())
    
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.TEXT_TO_SPEECH,
        process_func=tts_service.process_text_to_speech,
        data=job_params
    )
    
    logger.info(f"Created TTS job {job_id} with provider: {request.provider or 'default'}")
    
    return JobResponse(job_id=job_id)


@router.get("/speech/{job_id}", response_model=JobStatusResponse)
async def get_speech_job_status(job_id: str):
    """
    Get the status of a text to speech conversion job with enhanced results.
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing:
        - job_id: The ID of the job
        - status: Current job status (pending, processing, completed, failed)
        - result: If completed, contains:
            * audio_url: URL to generated audio file
            * tts_engine: Engine used (kokoro/edge)
            * voice: Voice used for synthesis
            * response_format: Audio format
            * estimated_duration: Audio duration in seconds
            * word_count: Number of words processed
            * For Kokoro: volume_multiplier, lang_code, word_timestamps (if requested)
        - error: If failed, contains error information
    """
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    # Ensure status is a JobStatus enum
    status = job_info.status if isinstance(job_info.status, JobStatus) else JobStatus(job_info.status.lower())
    
    return JobStatusResponse(
        job_id=job_id,
        status=status,
        result=job_info.result,
        error=job_info.error
    )


# Additional endpoints for TTS provider information
@router.get("/voices")
async def get_available_voices(provider: str | None = None, language: str | None = None):
    """
    Get available voices for the specified TTS provider with language filtering.
    
    Args:
        provider: TTS provider ("kokoro", "edge", or None for all)
        language: Language code to filter voices (e.g., 'en-US')
        
    Returns:
        Dictionary mapping provider names to lists of voice information
    """
    try:
        voices = await tts_service.get_available_voices(provider)
        
        # Apply language filtering if specified
        if language:
            filtered_voices = {}
            for prov, voice_list in voices.items():
                filtered_voices[prov] = [
                    voice for voice in voice_list 
                    if voice.get("language", "").lower() == language.lower()
                ]
            return filtered_voices
        
        return voices
    except Exception as e:
        logger.error(f"Failed to get available voices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")


@router.get("/voices/formatted")
async def get_voices_formatted(provider: str | None = None):
    """
    Get formatted list of voices by provider (OpenAI-compatible format).
    
    Args:
        provider: TTS provider ("kokoro", "edge", or None for all)
        
    Returns:
        Dictionary mapping provider names to formatted voice lists
    """
    try:
        voices = tts_service.get_voices_formatted(provider)
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Failed to get formatted voices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")


@router.get("/voices/all")
async def get_all_voices():
    """
    Get all available voices from all providers.
    
    Returns:
        Dictionary with all voices organized by provider
    """
    try:
        voices = await tts_service.get_available_voices()
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Failed to get all voices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")


@router.get("/models")
async def get_available_models(provider: str | None = None):
    """
    Get available TTS models for the specified provider.
    
    Args:
        provider: TTS provider ("kokoro", "edge", or None for all)
        
    Returns:
        Dictionary mapping provider names to model lists
    """
    try:
        models = tts_service.get_models(provider)
        return {"models": models}
    except Exception as e:
        logger.error(f"Failed to get available models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")


@router.get("/models/formatted")
async def get_models_formatted(provider: str | None = None):
    """
    Get formatted list of TTS models (OpenAI-compatible format).
    
    Args:
        provider: TTS provider ("kokoro", "edge", or None for all)
        
    Returns:
        List of model information in OpenAI format
    """
    try:
        models = tts_service.get_models_formatted(provider)
        return {"models": models}
    except Exception as e:
        logger.error(f"Failed to get formatted models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")


@router.get("/providers")
async def get_supported_providers():
    """
    Get list of supported TTS providers and their capabilities.
    
    Returns:
        Dictionary with provider information including supported formats
    """
    try:
        providers = tts_service.get_supported_providers()
        formats = tts_service.get_supported_formats()
        models = tts_service.get_models()
        
        return {
            "providers": providers,
            "formats": formats,
            "models": models,
            "default_provider": tts_service.default_provider
        }
    except Exception as e:
        logger.error(f"Failed to get provider information: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get providers: {str(e)}")


# Frontend compatibility endpoints
@router.get("/tts/providers")
async def get_tts_providers():
    """
    Get TTS providers for frontend compatibility.
    
    Returns:
        Provider information formatted for frontend expectations
    """
    try:
        providers = tts_service.get_supported_providers()
        return {
            "success": True,
            "data": providers
        }
    except Exception as e:
        logger.error(f"Failed to get TTS providers: {e}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/tts/{provider}/voices")
async def get_voices_for_provider(provider: str):
    """
    Get voices for a specific provider for frontend compatibility.
    
    Args:
        provider: TTS provider name
        
    Returns:
        Voice information formatted for frontend expectations
    """
    try:
        voices = await tts_service.get_available_voices(provider)
        provider_voices = voices.get(provider, [])
        
        return {
            "success": True,
            "data": provider_voices
        }
    except Exception as e:
        logger.error(f"Failed to get voices for provider {provider}: {e}")
        return {
            "success": False,
            "error": str(e)
        } 