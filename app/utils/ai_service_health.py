"""
AI Service Health Monitoring Utilities
"""
import os
import time
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class AIServiceHealthChecker:
    """Monitor health status of AI services."""
    
    def __init__(self):
        self.services_status = {}
    
    def check_openai_health(self) -> Dict[str, Any]:
        """Check OpenAI service health."""
        try:
            openai_key = os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_KEY')
            base_url = os.getenv('OPENAI_BASE_URL')
            
            return {
                'service': 'OpenAI',
                'available': bool(openai_key),
                'api_key_configured': bool(openai_key),
                'custom_base_url': bool(base_url),
                'base_url': base_url if base_url else 'https://api.openai.com/v1',
            }
        except Exception as e:
            logger.error(f"Error checking OpenAI health: {e}")
            return {'service': 'OpenAI', 'available': False, 'error': str(e)}
    
    def check_groq_health(self) -> Dict[str, Any]:
        """Check Groq service health."""
        try:
            groq_key = os.getenv('GROQ_API_KEY')
            base_url = os.getenv('GROQ_BASE_URL')
            model = os.getenv('GROQ_MODEL', 'llama3-70b-8192')
            
            return {
                'service': 'Groq',
                'available': bool(groq_key and len(groq_key) > 30),
                'api_key_configured': bool(groq_key),
                'key_length_valid': bool(groq_key and len(groq_key) > 30),
                'model': model,
                'custom_base_url': bool(base_url),
                'base_url': base_url if base_url else 'https://api.groq.com/openai/v1',
            }
        except Exception as e:
            logger.error(f"Error checking Groq health: {e}")
            return {'service': 'Groq', 'available': False, 'error': str(e)}
    
    def check_together_ai_health(self) -> Dict[str, Any]:
        """Check Together AI service health."""
        try:
            together_key = os.getenv('TOGETHER_API_KEY')
            model = os.getenv('TOGETHER_DEFAULT_MODEL', 'black-forest-labs/FLUX.1-schnell')
            
            return {
                'service': 'Together AI',
                'available': bool(together_key),
                'api_key_configured': bool(together_key),
                'default_model': model,
                'max_rps': os.getenv('TOGETHER_MAX_RPS', '2'),
                'max_concurrent': os.getenv('TOGETHER_MAX_CONCURRENT', '3'),
            }
        except Exception as e:
            logger.error(f"Error checking Together AI health: {e}")
            return {'service': 'Together AI', 'available': False, 'error': str(e)}
    
    def get_all_service_status(self) -> Dict[str, Any]:
        """Get health status of all AI services."""
        try:
            return {
                'timestamp': int(time.time()),
                'services': {
                    'openai': self.check_openai_health(),
                    'groq': self.check_groq_health(),
                    'together_ai': self.check_together_ai_health(),
                },
                'summary': {
                    'total_services': 3,
                    'available_services': sum(1 for s in [
                        self.check_openai_health()['available'],
                        self.check_groq_health()['available'], 
                        self.check_together_ai_health()['available']
                    ] if s),
                }
            }
        except Exception as e:
            logger.error(f"Error getting service status: {e}")
            return {'error': str(e), 'timestamp': int(time.time())}


# Global instance
ai_health_checker = AIServiceHealthChecker()
