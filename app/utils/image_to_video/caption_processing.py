"""
Caption processing utilities for the image-to-video pipeline.
"""
import os
import uuid
import logging
from typing import Dict, Any, List, Optional

from app.utils.media import download_subtitle_file
from app.services.media.transcription import get_transcription_service
from app.utils.captions import (
    create_enhanced_ass_from_timestamps,
    create_timestamps_from_text,
    prepare_subtitle_styling
)

logger = logging.getLogger(__name__)

async def process_captions_from_audio(audio_path: str, speech_text: Optional[str] = None, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Process captions from audio file with optional speech text.
    
    Args:
        audio_path: Path to the audio file
        speech_text: Optional text of the speech (for text-to-speech generated audio)
        params: Dictionary containing:
            - caption_properties: Caption styling properties
            - should_add_captions: Whether to add captions
            
    Returns:
        Dictionary containing:
            - srt_path: Path to the generated subtitle file
            - srt_url: URL of the subtitle file (if applicable)
            - caption_style: Style used for the captions
    """
    result = {
        "srt_path": None,
        "srt_url": None,
        "caption_style": None
    }
    
    if not params.get("should_add_captions", False) or not audio_path:
        return result
        
    try:
        # Get caption styling properties
        caption_properties = params.get("caption_properties", {})
        max_words_per_line = caption_properties.get("max_words_per_line", 10)
        style = caption_properties.get("style", "classic")
        language = params.get("caption_language", "auto")
        
        # For advanced styles, we need word-level timestamps (matching /add-captions logic)
        need_word_timestamps = style in ["highlight", "word_by_word", "karaoke", "underline", "bounce", "viral_bounce", "typewriter", "fade_in"] 
        
        # Make a copy of the audio file for transcription to prevent it from being deleted
        temp_dir = "temp"
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir, exist_ok=True)
            
        transcription_audio_path = os.path.join(temp_dir, f"transcribe_{uuid.uuid4()}.mp3")
        try:
            import shutil
            shutil.copy2(audio_path, transcription_audio_path)
            logger.info(f"Created copy of audio for transcription: {transcription_audio_path}")
        except Exception as copy_err:
            logger.error(f"Failed to copy audio for transcription: {str(copy_err)}")
            # Fall back to using the original file if copy fails
            transcription_audio_path = audio_path
        
        # Transcribe audio to get SRT (matching /add-captions parameters)
        logger.info(f"Transcribing audio for captions with style: {style}, language: {language}")
        
        # Prepare transcription parameters
        transcription_params = {
            "include_text": True,
            "include_srt": not need_word_timestamps,
            "word_timestamps": need_word_timestamps,
            "max_words_per_line": max_words_per_line
        }
        
        # Add language parameter if specified and not auto
        if language and language != "auto":
            transcription_params["language"] = language
            logger.info(f"Using language '{language}' for transcription")
        
        transcription_result = await get_transcription_service().transcribe(
            transcription_audio_path,
            **transcription_params
        )
        
        if need_word_timestamps and "words" in transcription_result:
            # Get audio duration (needed for SRT generation)
            from app.utils.image_to_video.audio_processing import get_audio_duration
            audio_duration = await get_audio_duration(audio_path)
            
            # Create custom styled subtitles using word timestamps
            srt_path = await create_enhanced_ass_from_timestamps(
                transcription_result["words"],
                audio_duration,
                max_words_per_line,
                style,
                caption_properties=caption_properties
            )
            result["srt_path"] = srt_path
            result["caption_style"] = style
            logger.info(f"Created {style} style captions using word timestamps from transcription")
            
            # Set srt_url if available in transcription result
            if "srt_url" in transcription_result:
                result["srt_url"] = transcription_result["srt_url"]
        elif "srt_url" in transcription_result:
            # Download the SRT file for other styles
            srt_path = await download_subtitle_file(transcription_result["srt_url"])
            result["srt_path"] = srt_path
            result["srt_url"] = transcription_result["srt_url"]
            result["caption_style"] = style
            logger.info(f"Downloaded SRT file for {style} style captions")
        else:
            # If we don't have word timestamps but need them for advanced styles,
            # use text from transcription to create artificial timestamps
            if need_word_timestamps and "text" in transcription_result:
                text = transcription_result["text"]
                
                # Get audio duration
                from app.utils.image_to_video.audio_processing import get_audio_duration
                audio_duration = await get_audio_duration(audio_path)
                
                words = text.split()
                word_count = len(words)
                
                if word_count > 0:
                    # Create artificial word timestamps by distributing evenly
                    seconds_per_word = audio_duration / word_count
                    
                    # Create word timestamps array
                    word_timestamps = []
                    for i, word in enumerate(words):
                        start_time = i * seconds_per_word
                        end_time = (i + 1) * seconds_per_word
                        word_timestamps.append({
                            "word": word,
                            "start": start_time,
                            "end": end_time
                        })
                    
                    # Use enhanced ASS creation for advanced styles
                    srt_path = await create_enhanced_ass_from_timestamps(
                        word_timestamps,
                        audio_duration,
                        max_words_per_line,
                        style,
                        caption_properties=caption_properties
                    )
                    result["srt_path"] = srt_path
                    result["caption_style"] = style
                    logger.info(f"Created {style} style captions using artificial word timestamps from transcription text")
                else:
                    raise RuntimeError("Transcription text is empty")
            else:
                raise RuntimeError("Transcription failed to generate subtitles")
                
        return result
            
    except Exception as e:
        logger.error(f"Error processing captions from audio: {e}")
        return result

async def process_captions_from_text(text: str, duration: float, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Process captions from text.
    
    Args:
        text: Text to create captions from
        duration: Duration of the audio in seconds
        params: Dictionary containing:
            - caption_properties: Caption styling properties
            
    Returns:
        Dictionary containing:
            - srt_path: Path to the generated subtitle file
            - caption_style: Style used for the captions
    """
    result = {
        "srt_path": None,
        "caption_style": None
    }
    
    if not text or duration <= 0:
        return result
        
    try:
        # Get caption styling properties
        caption_properties = params.get("caption_properties", {}) if params else {}
        max_words_per_line = caption_properties.get("max_words_per_line", 10) or 10
        style = caption_properties.get("style", "highlight") or "highlight"
        
        # Create subtitle file from text
        word_timestamps = create_timestamps_from_text(text, duration)
        srt_path = await create_enhanced_ass_from_timestamps(
            word_timestamps,
            duration,
            max_words_per_line,
            style,
            caption_properties=caption_properties
        )
        
        result["srt_path"] = srt_path
        result["caption_style"] = style
        
        return result
            
    except Exception as e:
        logger.error(f"Error processing captions from text: {e}")
        return result 