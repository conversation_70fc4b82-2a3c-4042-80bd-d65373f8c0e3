"""
Edge TTS service for high-quality text-to-speech using Microsoft Edge's TTS service.
"""
import asyncio
import tempfile
import subprocess
import os
import re

# Optional dependencies - Edge TTS functionality
try:
    import edge_tts
    import emoji
    EDGE_TTS_AVAILABLE = True
except ImportError:
    edge_tts = None  # type: ignore
    emoji = None  # type: ignore
    EDGE_TTS_AVAILABLE = False

import logging
from pathlib import Path
from typing import Optional, List, Dict, Any
from app.utils.text_processing import prepare_tts_input_with_context

# Configure logging
logger = logging.getLogger(__name__)

# OpenAI voice names mapped to edge-tts equivalents (updated with latest voices)
VOICE_MAPPING = {
    'alloy': 'en-US-JennyNeural',      # Updated to match latest Edge TTS project
    'ash': 'en-US-AndrewNeural',       # New voice
    'ballad': 'en-GB-ThomasNeural',    # New voice
    'coral': 'en-AU-NatashaNeural',    # New voice
    'echo': 'en-US-GuyNeural',         # Updated mapping
    'fable': 'en-GB-SoniaNeural',
    'nova': 'en-US-AriaNeural',        # Updated mapping
    'onyx': 'en-US-EricNeural',
    'sage': 'en-US-JennyNeural',       # New voice
    'shimmer': 'en-US-EmmaNeural',
    'verse': 'en-US-BrianNeural'       # New voice
}

# Default configuration
DEFAULT_VOICE = 'en-US-AvaNeural'
DEFAULT_LANGUAGE = 'en-US'

# TTS Models (OpenAI compatible)
TTS_MODELS = [
    {"id": "tts-1", "name": "Text-to-speech v1"},
    {"id": "tts-1-hd", "name": "Text-to-speech v1 HD"},
    {"id": "gpt-4o-mini-tts", "name": "GPT-4o mini TTS"}
]

class EdgeTTSService:
    """Service for Edge TTS text-to-speech generation."""
    
    def __init__(self):
        """Initialize Edge TTS service."""
        logger.info("Initializing Edge TTS service")
        
    @staticmethod
    def is_ffmpeg_available() -> bool:
        """Check if FFmpeg is installed and accessible."""
        try:
            subprocess.run(['ffmpeg', '-version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    @staticmethod
    def prepare_text_for_tts(text: str, remove_filter: bool = False) -> str:
        """
        Prepare text for TTS by cleaning Markdown and adding contextual hints.
        Uses enhanced text processing utilities.
        
        Args:
            text: Raw text containing potential Markdown formatting
            remove_filter: If True, skip text processing
            
        Returns:
            Cleaned text suitable for TTS
        """
        return prepare_tts_input_with_context(text, remove_filter)
    
    @staticmethod
    def speed_to_rate(speed: float) -> str:
        """
        Convert a multiplicative speed value to edge-tts rate format.
        Updated to match latest Edge TTS project validation.
        
        Args:
            speed: Multiplicative speed value (e.g., 1.5 for +50%, 0.5 for -50%)
            
        Returns:
            Formatted rate string (e.g., "+50%" or "-50%")
            
        Raises:
            ValueError: If speed is not between 0 and 2.0
        """
        if speed < 0 or speed > 2.0:
            raise ValueError("Speed must be between 0 and 2.0 (inclusive).")

        # Convert speed to percentage change
        percentage_change = (speed - 1) * 100

        # Format with a leading "+" or "-" as required
        return f"{percentage_change:+.0f}%"
    
    async def generate_speech_stream(
        self,
        text: str,
        voice: str = DEFAULT_VOICE,
        speed: float = 1.0,
        remove_filter: bool = False
    ):
        """
        Generate streaming TTS audio using Edge TTS with async generator.
        
        Args:
            text: Text to convert to speech
            voice: Voice name (OpenAI compatible or direct edge-tts voice)
            speed: Playback speed (0.25 to 4.0)
            remove_filter: Skip text preprocessing if True
            
        Yields:
            Audio chunk bytes
            
        Raises:
            RuntimeError: If TTS generation fails
        """
        if not EDGE_TTS_AVAILABLE:
            raise RuntimeError("Edge TTS functionality is not available. Please install edge-tts and emoji packages.")
            
        try:
            # Prepare text for TTS
            prepared_text = self.prepare_text_for_tts(text, remove_filter)
            
            # Determine the edge-tts voice to use
            edge_tts_voice = VOICE_MAPPING.get(voice, voice)
            
            # Convert speed to SSML rate format
            try:
                speed_rate = self.speed_to_rate(speed)
            except ValueError as e:
                logger.warning(f"Invalid speed value {speed}: {e}. Using default speed.")
                speed_rate = "+0%"
            
            logger.info(f"Streaming speech for text length: {len(prepared_text)} characters")
            logger.info(f"Using voice: {edge_tts_voice}, speed: {speed_rate}")
            
            # Create the communicator for streaming
            assert EDGE_TTS_AVAILABLE and edge_tts is not None, "Edge TTS should be available at this point"
            communicator = edge_tts.Communicate(text=prepared_text, voice=edge_tts_voice, rate=speed_rate)
            
            # Stream the audio data
            async for chunk in communicator.stream():
                if chunk.get("type") == "audio" and "data" in chunk:
                    yield chunk["data"]
                    
        except Exception as e:
            logger.error(f"Failed to generate streaming speech: {e}")
            raise RuntimeError(f"Speech streaming failed: {str(e)}")
    
    def generate_speech_stream_sync(
        self,
        text: str,
        voice: str = DEFAULT_VOICE,
        speed: float = 1.0,
        remove_filter: bool = False
    ):
        """
        Synchronous wrapper for generate_speech_stream.
        
        Args:
            text: Text to convert to speech
            voice: Voice name
            speed: Playback speed
            remove_filter: Skip text preprocessing
            
        Returns:
            Generator yielding audio chunks
        """
        raise NotImplementedError(
            "Synchronous streaming is not supported. Use generate_speech_stream() instead."
        )
    
    async def generate_speech(
        self,
        text: str,
        voice: str = DEFAULT_VOICE,
        response_format: str = "mp3",
        speed: float = 1.0,
        remove_filter: bool = False
    ) -> str:
        """
        Generate TTS audio using Edge TTS.
        
        Args:
            text: Text to convert to speech
            voice: Voice name (OpenAI compatible or direct edge-tts voice)
            response_format: Audio format (mp3, wav, opus, aac, flac, pcm)
            speed: Playback speed (0.25 to 4.0)
            remove_filter: Skip text preprocessing if True
            
        Returns:
            Path to generated audio file
            
        Raises:
            RuntimeError: If TTS generation fails
        """
        if not EDGE_TTS_AVAILABLE:
            raise RuntimeError("Edge TTS functionality is not available. Please install edge-tts and emoji packages.")
            
        try:
            # Prepare text for TTS
            prepared_text = self.prepare_text_for_tts(text, remove_filter)
            
            # Determine the edge-tts voice to use
            edge_tts_voice = VOICE_MAPPING.get(voice, voice)
            
            # Convert speed to SSML rate format
            try:
                speed_rate = self.speed_to_rate(speed)
            except ValueError as e:
                logger.warning(f"Invalid speed value {speed}: {e}. Using default speed.")
                speed_rate = "+0%"
            
            # Create temporary file for MP3 output
            temp_mp3_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
            temp_mp3_path = temp_mp3_file.name
            temp_mp3_file.close()
            
            logger.info(f"Generating speech for text length: {len(prepared_text)} characters")
            logger.info(f"Using voice: {edge_tts_voice}, format: {response_format}, speed: {speed_rate}")
            
            # Generate the MP3 file using Edge TTS
            assert EDGE_TTS_AVAILABLE and edge_tts is not None, "Edge TTS should be available at this point"
            communicator = edge_tts.Communicate(text=prepared_text, voice=edge_tts_voice, rate=speed_rate)
            await communicator.save(temp_mp3_path)
            
            # If MP3 is requested, return the generated file directly
            if response_format.lower() == "mp3":
                logger.info(f"Speech generated successfully: {temp_mp3_path}")
                return temp_mp3_path
            
            # For other formats, check if FFmpeg is available
            if not self.is_ffmpeg_available():
                logger.warning("FFmpeg not available. Returning MP3 format instead of requested format.")
                return temp_mp3_path
            
            # Convert to requested format using FFmpeg
            return await self._convert_audio_format(temp_mp3_path, response_format)
            
        except Exception as e:
            logger.error(f"Failed to generate speech: {e}")
            raise RuntimeError(f"Speech generation failed: {str(e)}")
    
    async def _convert_audio_format(self, input_path: str, output_format: str) -> str:
        """
        Convert audio file to the requested format using FFmpeg.
        
        Args:
            input_path: Path to input MP3 file
            output_format: Target audio format
            
        Returns:
            Path to converted audio file
            
        Raises:
            RuntimeError: If conversion fails
        """
        # Create temporary file for converted output
        converted_file = tempfile.NamedTemporaryFile(delete=False, suffix=f".{output_format}")
        converted_path = converted_file.name
        converted_file.close()
        
        # Build FFmpeg command with enhanced codec/container mappings
        codec_map = {
            "aac": "aac",
            "mp3": "libmp3lame",
            "wav": "pcm_s16le",
            "opus": "libopus",
            "flac": "flac",
            "pcm": "pcm_s16le"
        }
        
        container_map = {
            "aac": "mp4",        # AAC in MP4 container
            "mp3": "mp3", 
            "wav": "wav",
            "opus": "ogg",       # OPUS in OGG container
            "flac": "flac",
            "pcm": "wav"         # PCM in WAV container
        }
        
        codec = codec_map.get(output_format.lower(), "aac")
        container = container_map.get(output_format.lower(), output_format)
        
        ffmpeg_command = [
            "ffmpeg",
            "-i", input_path,
            "-c:a", codec,
        ]
        
        # Add bitrate for compressed formats (improved logic)
        if output_format.lower() not in ["wav", "flac", "pcm"]:
            ffmpeg_command.extend(["-b:a", "192k"])
        
        # Add format-specific options
        ffmpeg_command.extend([
            "-f", container,
            "-y",  # Overwrite without prompt
            converted_path
        ])
        
        try:
            logger.info(f"Converting audio to {output_format} format")
            result = subprocess.run(
                ffmpeg_command, 
                check=True, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                timeout=60  # 60 second timeout
            )
            
            # Clean up original MP3 file
            Path(input_path).unlink(missing_ok=True)
            
            logger.info(f"Audio converted successfully: {converted_path}")
            return converted_path
            
        except subprocess.CalledProcessError as e:
            # Clean up files on error
            Path(converted_path).unlink(missing_ok=True)
            Path(input_path).unlink(missing_ok=True)
            
            error_msg = f"FFmpeg conversion failed: {e.stderr.decode('utf-8', 'ignore') if e.stderr else str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        except subprocess.TimeoutExpired:
            # Clean up files on timeout
            Path(converted_path).unlink(missing_ok=True)
            Path(input_path).unlink(missing_ok=True)
            
            error_msg = "FFmpeg conversion timed out"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    async def get_available_voices(self, language: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of available Edge TTS voices.
        
        Args:
            language: Language code to filter voices (e.g., 'en-US'), or None for all
            
        Returns:
            List of voice information dictionaries
        """
        try:
            if not EDGE_TTS_AVAILABLE or edge_tts is None:
                raise RuntimeError("Edge TTS is not available")
            all_voices = await edge_tts.list_voices()
            
            if language and language.lower() != 'all':
                filtered_voices = [
                    {
                        "name": voice['ShortName'],
                        "gender": voice['Gender'],
                        "language": voice['Locale'],
                        "display_name": voice['FriendlyName']
                    }
                    for voice in all_voices 
                    if voice['Locale'].lower() == language.lower()
                ]
            else:
                filtered_voices = [
                    {
                        "name": voice['ShortName'],
                        "gender": voice['Gender'],
                        "language": voice['Locale'],
                        "display_name": voice['FriendlyName']
                    }
                    for voice in all_voices
                ]
            
            return filtered_voices
            
        except Exception as e:
            logger.error(f"Failed to get available voices: {e}")
            return []
    
    @staticmethod
    def get_supported_models() -> List[Dict[str, str]]:
        """
        Get list of supported TTS models (OpenAI compatible).
        
        Returns:
            List of model information
        """
        return TTS_MODELS
    
    @staticmethod
    def get_models_formatted() -> List[Dict[str, str]]:
        """
        Get formatted list of models (ID only format).
        
        Returns:
            List of model IDs
        """
        return [{"id": model["id"]} for model in TTS_MODELS]
    
    @staticmethod
    def get_voices_formatted() -> List[Dict[str, str]]:
        """
        Get formatted list of OpenAI-compatible voices.
        
        Returns:
            List of voice mappings
        """
        return [{"id": k, "name": v} for k, v in VOICE_MAPPING.items()]

# Global service instance
edge_tts_service = EdgeTTSService()