"""
Internal Kokoro ONNX TTS service for high-quality text-to-speech.
"""
import os
import tempfile
import json
import logging
import asyncio
from typing import Optional, List, Dict, Any
from pathlib import Path
import soundfile as sf
import numpy as np

# Optional dependencies - Kokoro TTS functionality
try:
    from kokoro_onnx import Kokoro
    from kokoro_onnx.tokenizer import Tokenizer
    KOKORO_AVAILABLE = True
except ImportError:
    Kokoro = None  # type: ignore
    Tokenizer = None  # type: ignore
    KOKORO_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)

# Default configuration
DEFAULT_VOICE = "af_heart"
DEFAULT_LANGUAGE = "en-US"
MODEL_PATH = os.environ.get("KOKORO_MODEL_PATH", "/app/models")

class KokoroTTSService:
    """Internal Kokoro ONNX TTS service."""
    
    def __init__(self):
        """Initialize Kokoro TTS service."""
        self.tts_model = None
        self.tokenizer = None
        self.voices_data = None
        self.model_initialized = False
        logger.info("Initializing internal Kokoro TTS service")
        
    def _ensure_model_files(self) -> tuple[str, str]:
        """Download Kokoro model files if they don't exist."""
        import requests
        
        os.makedirs(MODEL_PATH, exist_ok=True)
        
        model_file = os.path.join(MODEL_PATH, "kokoro-v1.0.onnx")
        voices_file = os.path.join(MODEL_PATH, "voices-v1.0.bin")
        
        def download_file(url: str, filepath: str) -> None:
            """Download a file from URL to filepath."""
            logger.info(f"Downloading {os.path.basename(filepath)}...")
            try:
                response = requests.get(url, stream=True)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                logger.info(f"Download progress: {progress:.1f}%")
                
                logger.info(f"Downloaded {os.path.basename(filepath)} successfully")
                
            except Exception as e:
                logger.error(f"Failed to download {os.path.basename(filepath)}: {e}")
                if os.path.exists(filepath):
                    os.remove(filepath)
                raise RuntimeError(f"Failed to download {os.path.basename(filepath)}: {e}")
        
        if not os.path.exists(model_file):
            download_file(
                "https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/kokoro-v1.0.onnx",
                model_file
            )
                
        if not os.path.exists(voices_file):
            download_file(
                "https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/voices-v1.0.bin",
                voices_file
            )
                
        return model_file, voices_file
    
    def _load_voices_data(self) -> List[Dict[str, Any]]:
        """Load voice information from JSON file."""
        voices_json_path = os.path.join(os.path.dirname(__file__), "kokoro_voices.json")
        
        if os.path.exists(voices_json_path):
            try:
                with open(voices_json_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load voices JSON: {e}")
        
        # Fallback to basic voice list if JSON not available
        logger.warning("kokoro_voices.json not found, using basic voice list")
        return [
            {"name": "af_heart", "gender": "female", "locale": "en-US", "engine": "kokoro", "description": "American Female - Heart", "grade": "A"},
            {"name": "af_alloy", "gender": "female", "locale": "en-US", "engine": "kokoro", "description": "American Female - Alloy", "grade": "C"},
            {"name": "af_bella", "gender": "female", "locale": "en-US", "engine": "kokoro", "description": "American Female - Bella", "grade": "A-"},
            {"name": "am_michael", "gender": "male", "locale": "en-US", "engine": "kokoro", "description": "American Male - Michael", "grade": "C+"},
            {"name": "bf_emma", "gender": "female", "locale": "en-GB", "engine": "kokoro", "description": "British Female - Emma", "grade": "B-"},
            {"name": "bm_george", "gender": "male", "locale": "en-GB", "engine": "kokoro", "description": "British Male - George", "grade": "C"},
        ]
    
    async def initialize_model(self) -> bool:
        """Initialize the Kokoro TTS model and tokenizer."""
        if not KOKORO_AVAILABLE:
            logger.error("Kokoro ONNX not available. Please install with: pip install kokoro-onnx")
            return False
            
        if self.model_initialized:
            return True
            
        try:
            logger.info("Initializing Kokoro TTS model...")
            
            # Ensure model files exist
            model_file, voices_file = self._ensure_model_files()
            
            # Initialize model and tokenizer
            if not KOKORO_AVAILABLE or Kokoro is None or Tokenizer is None:
                raise RuntimeError("Kokoro TTS dependencies are not available")
            self.tts_model = Kokoro(model_file, voices_file)
            self.tokenizer = Tokenizer()
            self.voices_data = self._load_voices_data()
            self.model_initialized = True
            
            logger.info("Kokoro TTS model and tokenizer initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Kokoro TTS model: {e}")
            self.tts_model = None
            self.tokenizer = None
            self.model_initialized = False
            return False
    
    def is_available(self) -> bool:
        """Check if Kokoro TTS is available and initialized."""
        return KOKORO_AVAILABLE and self.model_initialized
    
    def _process_voice_combination(self, voice: str) -> tuple[str, str]:
        """
        Process voice combination string and return voice name and path.
        Supports formats like: af_heart+af_bella or af_heart(0.7)+af_bella(0.3)
        """
        if '+' not in voice:
            # Single voice
            return voice, voice
        
        import re
        import tempfile
        import torch
        
        # Parse voice combination
        voices = voice.split('+')
        voice_tensors = []
        total_weight = 0
        
        for voice_spec in voices:
            # Check for weight specification: voice_name(weight)
            weight_match = re.match(r'([^(]+)\(([0-9.]+)\)', voice_spec.strip())
            if weight_match:
                voice_name, weight_str = weight_match.groups()
                weight = float(weight_str)
            else:
                voice_name = voice_spec.strip()
                weight = 1.0
            
            total_weight += weight
            voice_tensors.append((voice_name, weight))
        
        # Load and combine voice tensors
        combined_tensor = None
        for voice_name, weight in voice_tensors:
            # For now, return the first voice - in a full implementation,
            # we would load and combine the actual voice tensors
            if combined_tensor is None:
                return voice_name, voice_name
        
        return voice, voice  # Fallback to original
    
    def _process_pause_tags(self, text: str) -> str:
        """
        Process pause tags in text like [pause:0.5s] and convert to appropriate format.
        For now, we'll replace with silence periods.
        """
        import re
        
        # Pattern to match [pause:Xs] or [pause:X.Ys]
        pause_pattern = re.compile(r'\[pause:(\d+(?:\.\d+)?)s\]', re.IGNORECASE)
        
        def replace_pause(match):
            duration = float(match.group(1))
            # Convert to approximate silence (could be improved with actual silence generation)
            silence_chars = "..." * max(1, int(duration))
            return f" {silence_chars} "
        
        return pause_pattern.sub(replace_pause, text)
    
    def _normalize_text(self, text: str, normalization_options: Optional[Dict] = None) -> str:
        """
        Apply text normalization based on options.
        This is a basic implementation - could be enhanced with proper normalization library.
        """
        if not normalization_options:
            return text
        
        import re
        
        # URL normalization
        if normalization_options.get('url_normalization', True):
            text = re.sub(r'https?://[^\s]+', 'URL', text)
        
        # Email normalization  
        if normalization_options.get('email_normalization', True):
            text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', 'email address', text)
        
        # Phone normalization
        if normalization_options.get('phone_normalization', True):
            text = re.sub(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', 'phone number', text)
        
        # Unit normalization
        if normalization_options.get('unit_normalization', False):
            text = re.sub(r'(\d+)\s*(KB|MB|GB|TB)', r'\1 \2', text)
            text = text.replace('KB', 'kilobytes').replace('MB', 'megabytes').replace('GB', 'gigabytes').replace('TB', 'terabytes')
        
        # Replace remaining symbols
        if normalization_options.get('replace_remaining_symbols', True):
            symbol_replacements = {
                '&': 'and',
                '@': 'at',
                '%': 'percent',
                '#': 'hashtag',
                '$': 'dollar',
                '+': 'plus',
                '=': 'equals',
                '<': 'less than',
                '>': 'greater than',
            }
            for symbol, replacement in symbol_replacements.items():
                text = text.replace(symbol, f' {replacement} ')
        
        return text

    async def generate_speech(
        self,
        text: str,
        voice: str = DEFAULT_VOICE,
        language: str = DEFAULT_LANGUAGE,
        speed: float = 1.0,
        response_format: str = "wav",
        volume_multiplier: float = 1.0,
        normalization_options: Optional[Dict] = None,
        return_timestamps: bool = False
    ) -> str:
        """
        Generate TTS audio using internal Kokoro ONNX with advanced features.
        
        Args:
            text: Text to convert to speech
            voice: Kokoro voice name or voice combination (e.g., "af_heart+af_bella")
            language: Language code (used for phonemization)
            speed: Playback speed 
            response_format: Audio format (wav, mp3, etc.)
            volume_multiplier: Volume adjustment factor
            normalization_options: Text processing options
            return_timestamps: Whether to return word-level timestamps
            
        Returns:
            Path to generated audio file
            
        Raises:
            RuntimeError: If TTS generation fails
        """
        if not self.is_available():
            if not await self.initialize_model():
                raise RuntimeError("Kokoro TTS is not available or failed to initialize")
        
        try:
            # Process text with normalization options
            processed_text = text
            if normalization_options:
                processed_text = self._normalize_text(processed_text, normalization_options)
            
            # Process pause tags
            processed_text = self._process_pause_tags(processed_text)
            
            # Process voice combination
            actual_voice, voice_path = self._process_voice_combination(voice)
            
            # Convert language code to Kokoro format
            kokoro_lang = self._map_language_code(language)
            
            logger.info(f"Generating speech for text length: {len(processed_text)} characters")
            logger.info(f"Using voice: {actual_voice}, language: {kokoro_lang}, speed: {speed}, volume: {volume_multiplier}")
            
            # Phonemize the processed text
            if self.tokenizer is None:
                raise RuntimeError("Kokoro tokenizer not initialized")
            phonemes = self.tokenizer.phonemize(processed_text, lang=kokoro_lang)
            
            # Generate speech
            if self.tts_model is None:
                raise RuntimeError("Kokoro TTS model not initialized")
            samples, sample_rate = self.tts_model.create(
                phonemes,
                voice=actual_voice,
                speed=speed,
                is_phonemes=True
            )
            
            # Apply volume multiplier
            if volume_multiplier != 1.0:
                samples = samples * volume_multiplier
                # Ensure we don't clip
                samples = np.clip(samples, -1.0, 1.0)
            
            # Create temporary file for output
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f".{response_format}")
            temp_path = temp_file.name
            temp_file.close()
            
            # Save audio data in requested format
            if response_format.lower() in ["wav", "mp3", "flac", "ogg"]:
                sf.write(temp_path, samples, sample_rate, format=response_format.upper())
            else:
                # Fallback to WAV for unsupported formats
                sf.write(temp_path, samples, sample_rate, format='WAV')
                logger.warning(f"Requested format {response_format} not directly supported, generated WAV")
            
            logger.info(f"Speech generated successfully: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Failed to generate speech with Kokoro: {e}")
            raise RuntimeError(f"Kokoro speech generation failed: {str(e)}")
    
    def _map_language_code(self, language: str) -> str:
        """Map standard language codes to Kokoro tokenizer format."""
        language_mapping = {
            "en-US": "en-us",
            "en-GB": "en-us",  # Kokoro uses en-us for English variants
            "en": "en-us",
            "ja-JP": "ja",
            "ja": "ja",
            "zh-CN": "zh",
            "zh": "zh",
            "es-ES": "es",
            "es": "es",
            "fr-FR": "fr",
            "fr": "fr",
            "it-IT": "it",
            "it": "it",
            "pt-BR": "pt",
            "pt": "pt",
            "hi-IN": "hi",
            "hi": "hi"
        }
        
        return language_mapping.get(language, "en-us")
    
    async def get_available_voices(self, language: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of available Kokoro voices.
        
        Args:
            language: Language code to filter voices (e.g., 'en-US'), or None for all
            
        Returns:
            List of voice information dictionaries
        """
        if not self.is_available():
            if not await self.initialize_model():
                return []
        
        try:
            if not self.voices_data:
                self.voices_data = self._load_voices_data()
            
            voices = []
            for voice_data in self.voices_data:
                # Filter by language if specified
                if language and language.lower() != 'all':
                    if voice_data["locale"].lower() != language.lower():
                        continue
                
                voices.append({
                    "name": voice_data["name"],
                    "gender": voice_data["gender"],
                    "language": voice_data["locale"],
                    "description": voice_data["description"],
                    "grade": voice_data.get("grade", ""),
                    "engine": "kokoro"
                })
            
            logger.info(f"Retrieved {len(voices)} Kokoro voices" + (f" for language {language}" if language else ""))
            return voices
            
        except Exception as e:
            logger.error(f"Failed to get available voices: {e}")
            return []
    
    def get_supported_formats(self) -> List[str]:
        """
        Get supported audio formats.
        
        Returns:
            List of supported format strings
        """
        return ["wav"]  # Currently only WAV is directly supported
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "name": "Kokoro ONNX",
            "version": "1.0",
            "engine": "kokoro",
            "available": self.is_available(),
            "model_path": MODEL_PATH,
            "supported_formats": self.get_supported_formats()
        }

# Global service instance
kokoro_tts_service = KokoroTTSService()