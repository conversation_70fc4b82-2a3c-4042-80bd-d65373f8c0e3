"""
Music generation service using multiple providers.
"""
import os
import tempfile
import logging
import uuid
import asyncio
import json
import aiohttp
from typing import Any, Optional, TYPE_CHECKING, Union, Tuple

# Type checking imports
if TYPE_CHECKING:
    import numpy as np
    import scipy.io.wavfile
    from transformers.pipelines import pipeline
    from google import genai
    from google.genai import types
    
    NumpyArray = np.ndarray
else:
    # Create type aliases for runtime
    NumpyArray = Any

# Runtime imports with graceful fallback
try:
    import numpy as np
    import scipy.io.wavfile
    from transformers.pipelines import pipeline
    MUSIC_GENERATION_AVAILABLE = True
except ImportError as e:
    MUSIC_GENERATION_AVAILABLE = False
    missing_deps = str(e)
    # Create stub objects for runtime when dependencies missing
    np = None
    scipy = None
    pipeline = None

try:
    from google import genai
    from google.genai import types
    LYRIA_AVAILABLE = True
except ImportError:
    LYRIA_AVAILABLE = False
    genai = None
    types = None

from app.services.s3 import s3_service

logger = logging.getLogger(__name__)


class MusicGenerationService:
    """Service for generating music using multiple providers"""
    
    def __init__(self):
        self.model_cache = {}
        self.model_loading_lock = asyncio.Lock()  # Prevent concurrent model loading
        self.temp_dir = os.environ.get('LOCAL_STORAGE_PATH', '/tmp')
        self.gemini_api_key = os.environ.get('GEMINI_API_KEY')
        self._warmup_started = False
        
        if not MUSIC_GENERATION_AVAILABLE:
            logger.warning(f"MusicGen dependencies not available: {missing_deps}. MusicGen features will be disabled.")
        
        if not LYRIA_AVAILABLE:
            logger.warning("Lyria dependencies not available. Please install: google-generativeai")
        
        if not self.gemini_api_key and LYRIA_AVAILABLE:
            logger.warning("GEMINI_API_KEY not configured. Lyria provider will not be available and will fallback to MusicGen.")
        
        # Initialize Gemini client if available
        if LYRIA_AVAILABLE and self.gemini_api_key and genai is not None:
            try:
                self.genai_client = genai.Client(
                    api_key=self.gemini_api_key,
                    http_options={'api_version': 'v1alpha'}
                )
                logger.info("Lyria provider initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Lyria provider: {e}")
                self.genai_client = None
        else:
            self.genai_client = None
        
        # Start background model warmup if enabled
        if os.environ.get('ENABLE_MODEL_WARMUP', 'true').lower() == 'true':
            asyncio.create_task(self._background_warmup())
        
    async def _get_model(self, model_size: str = "small"):
        """Get or load the MusicGen model with caching and optimized loading"""
        if not MUSIC_GENERATION_AVAILABLE:
            raise Exception("Music generation dependencies not available. Please install: transformers, torch, scipy, numpy")
        
        model_name = f"facebook/musicgen-stereo-{model_size}"
        
        # Use lock to prevent concurrent loading of the same model
        async with self.model_loading_lock:
            # Double-check pattern - model might have been loaded while waiting for lock
            if model_name in self.model_cache:
                logger.info(f"Using already loaded model: {model_name}")
                return self.model_cache[model_name]
            
            # Use environment cache directory for consistent caching
            cache_dir = os.environ.get('TRANSFORMERS_CACHE', '/root/.cache/huggingface')
            
            # Check if this is likely a pre-loaded scenario (faster timeout)
            is_preloaded = os.environ.get('ENABLE_MODEL_PRELOAD', 'false').lower() == 'true'
            # More generous timeouts for production stability
            timeout = 300 if is_preloaded else 1800  # 5 min if pre-loaded, 30 min otherwise
            
            try:
                logger.info(f"Loading MusicGen model: {model_name}")
                logger.info(f"Using cache directory: {cache_dir}")
                logger.info(f"Model timeout: {timeout} seconds ({'pre-loaded' if is_preloaded else 'first-time'} scenario)")
                
                # Set the cache directory as environment variable (transformers uses this)
                original_cache = os.environ.get('TRANSFORMERS_CACHE')
                os.environ['TRANSFORMERS_CACHE'] = cache_dir
                
                try:
                    # Load model with specified cache directory
                    if pipeline is None:
                        raise Exception("Pipeline not available - transformers not properly imported")
                    
                    # Create local variable to help type checker understand pipeline is not None
                    model_pipeline = pipeline
                    
                    self.model_cache[model_name] = await asyncio.wait_for(
                        asyncio.to_thread(lambda: model_pipeline(
                            "text-to-audio", 
                            model_name, 
                            device=-1  # CPU usage
                        )),
                        timeout=timeout
                    )
                finally:
                    # Restore original cache setting
                    if original_cache is not None:
                        os.environ['TRANSFORMERS_CACHE'] = original_cache
                    elif 'TRANSFORMERS_CACHE' in os.environ:
                        del os.environ['TRANSFORMERS_CACHE']
                        
                logger.info(f"Model {model_name} loaded successfully from cache: {cache_dir}")
                
            except asyncio.TimeoutError:
                timeout_msg = f"Model loading timed out after {timeout} seconds"
                logger.error(f"{timeout_msg}: {model_name}")
                
                if is_preloaded:
                    raise Exception(f"{timeout_msg}. Model may not be properly pre-loaded. Check model pre-loading configuration.")
                else:
                    raise Exception(f"{timeout_msg}. The model download may be slow or the service may be overloaded. Please try again later.")
                    
            except Exception as e:
                logger.error(f"Failed to load model {model_name}: {str(e)}")
                raise Exception(f"Failed to load MusicGen model: {str(e)}")
                
            logger.info(f"Model {model_name} loaded successfully from cache: {cache_dir}")
        
        return self.model_cache[model_name]
    
    async def _background_warmup(self):
        """Background task to warm up models during startup"""
        if self._warmup_started or not MUSIC_GENERATION_AVAILABLE:
            return
        
        self._warmup_started = True
        logger.info("🔥 Starting background model warmup")
        
        try:
            # Wait a bit to let the service start up
            await asyncio.sleep(30)
            
            # Warm up the default model
            model_size = os.environ.get('MUSICGEN_MODEL_SIZE', 'small')
            logger.info(f"🔥 Warming up {model_size} model in background")
            
            async with self.model_loading_lock:
                if f"facebook/musicgen-stereo-{model_size}" not in self.model_cache:
                    try:
                        await self._get_model(model_size)
                        logger.info(f"✅ Background warmup completed for {model_size} model")
                    except Exception as e:
                        logger.warning(f"⚠️ Background warmup failed for {model_size} model: {e}")
        except Exception as e:
            logger.error(f"❌ Background warmup task failed: {e}")
    
    async def _preload_model_if_configured(self, model_size: str) -> bool:
        """Check if model should be preloaded based on configuration"""
        model_name = f"facebook/musicgen-stereo-{model_size}"
        
        # Check environment configuration
        preload_enabled = False
        if model_size == 'small':
            preload_enabled = True  # Always preload small model
        elif model_size == 'medium':
            preload_enabled = os.environ.get('PRELOAD_MUSICGEN_MEDIUM', 'false').lower() == 'true'
        elif model_size == 'large':
            preload_enabled = os.environ.get('PRELOAD_MUSICGEN_LARGE', 'false').lower() == 'true'
        
        if preload_enabled and model_name not in self.model_cache:
            logger.info(f"🚀 Preloading {model_size} model as configured")
            try:
                await self._get_model(model_size)
                return True
            except Exception as e:
                logger.warning(f"⚠️ Failed to preload {model_size} model: {e}")
                return False
        
        return model_name in self.model_cache
    
    async def process_music_generation(self, job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """
        Process music generation job using the specified provider.
        
        Args:
            job_id: The job ID (unused but required for job queue signature)
            data: Dictionary containing music generation parameters
            
        Returns:
            Dictionary with music generation results
        """
        try:
            provider = data.get('provider', 'musicgen')
            description = data.get('description') or "instrumental music"  # Provide default if None
            duration = data.get('duration', 8)
            
            # Ensure duration is a valid number
            if duration is None or not isinstance(duration, (int, float)) or duration <= 0:
                duration = 8  # Default to 8 seconds
            
            if not description:
                raise ValueError("Description is required for music generation")
            
            logger.info(f"Generating music using {provider} provider for description: '{description}' (duration: {duration}s)")
            
            if provider == 'lyria':
                # Check if Lyria is actually available
                if not self.genai_client:
                    logger.warning(f"Lyria provider requested but not available (API key: {'configured' if self.gemini_api_key else 'missing'}, dependencies: {'available' if LYRIA_AVAILABLE else 'missing'}). Falling back to MusicGen.")
                    # Immediate fallback to MusicGen
                    data_copy = data.copy()
                    data_copy['provider'] = 'musicgen'
                    result = await self._generate_with_musicgen(job_id, data_copy)
                    result['provider'] = 'musicgen_fallback'
                    result['fallback_reason'] = "Lyria not available - missing API key or dependencies"
                    return result
                
                try:
                    # Try Lyria with a shorter timeout
                    return await asyncio.wait_for(
                        self._generate_with_lyria(job_id, data),
                        timeout=max(duration + 60, 120)  # Maximum 2 minutes for any Lyria request
                    )
                except (asyncio.TimeoutError, Exception) as e:
                    logger.warning(f"Lyria generation failed: {str(e)}. Falling back to MusicGen.")
                    # Fallback to MusicGen
                    data_copy = data.copy()
                    data_copy['provider'] = 'musicgen'
                    result = await self._generate_with_musicgen(job_id, data_copy)
                    result['provider'] = 'musicgen_fallback'
                    result['fallback_reason'] = f"Lyria failed: {str(e)}"
                    return result
            elif provider == 'musicgen':
                # Add overall timeout for MusicGen (much more generous for production)
                # Scale with model loading considerations
                base_timeout = duration * 45 + 900  # 45x duration + 15min model load
                musicgen_timeout = min(base_timeout, 3600)  # Max 60 minutes for any request
                try:
                    return await asyncio.wait_for(
                        self._generate_with_musicgen(job_id, data),
                        timeout=musicgen_timeout
                    )
                except asyncio.TimeoutError:
                    logger.error(f"MusicGen generation timed out after {musicgen_timeout}s")
                    raise Exception(f"MusicGen generation timed out after {musicgen_timeout} seconds. The service may be overloaded.")
            else:
                raise ValueError(f"Unsupported provider: {provider}")
                
        except Exception as e:
            logger.error(f"Music generation failed: {str(e)}", exc_info=True)
            raise Exception(f"Music generation failed: {str(e)}")

    async def _generate_with_musicgen(self, job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Generate music using Meta's MusicGen model with sliding window for longer durations."""
        if not MUSIC_GENERATION_AVAILABLE:
            raise Exception("MusicGen dependencies not available. Please install: transformers, torch, scipy, numpy")
        
        description = data.get('description') or "instrumental music"  # Provide default if None
        duration = data.get('duration', 8)
        model_size = data.get('model_size', 'small')
        output_format = data.get('output_format', 'wav')
        
        # Get the model (this is the slowest part on first load)
        logger.info("Loading MusicGen model - this may take up to 2-3 minutes on first request")
        synthesizer = await self._get_model(model_size)
        logger.info("Model loaded, starting music generation")
        
        # Generate unique filename
        filename = f"musicgen_{job_id or uuid.uuid4().hex}"
        temp_wav_path = os.path.join(self.temp_dir, f"{filename}.wav")
        
        # For reliability, limit MusicGen to reasonable durations
        if duration > 30:
            logger.error(f"MusicGen duration {duration}s exceeds reliable limit of 30s")
            raise Exception(f"MusicGen supports maximum 30 seconds. Requested: {duration}s. Note: Generation time scales significantly with duration (15s takes ~1.5 minutes).")
        elif duration > 15:
            logger.warning(f"MusicGen duration {duration}s may take several minutes to generate")
        
        # Single generation for short durations only
        audio_data, sampling_rate = await self._generate_single_segment(
            synthesizer, description, duration
        )
        
        # Ensure audio data is in the correct format
        if np is not None and isinstance(audio_data, np.ndarray):
            # Handle stereo audio (2D array with shape [samples, channels])
            if audio_data.ndim > 1:
                # For stereo, keep the 2D shape [samples, channels]
                pass
            else:
                # If mono, convert to stereo by duplicating the channel
                audio_data = np.column_stack((audio_data, audio_data))
            
            # Normalize audio if needed
            if audio_data.max() > 1.0 or audio_data.min() < -1.0:
                audio_data = audio_data / np.max(np.abs(audio_data))
            
            # Convert to 16-bit PCM
            audio_data = (audio_data * 32767).astype(np.int16)
        
        # Save WAV file
        if scipy is not None:
            scipy.io.wavfile.write(temp_wav_path, rate=sampling_rate, data=audio_data)
        
        # For now, only support WAV output (MP3 conversion would require ffmpeg)
        if output_format.lower() == "mp3":
            logger.warning("MP3 output not yet supported, defaulting to WAV")
            output_format = "wav"
        
        output_path = temp_wav_path
        
        # Upload to S3
        file_size = os.path.getsize(output_path)
        object_name = f"audio/music/{filename}.{output_format}"
        
        audio_url = await s3_service.upload_file(
            file_path=output_path,
            object_name=object_name
        )
        
        # Clean up temp file
        if os.path.exists(output_path):
            os.remove(output_path)
        
        # Calculate actual duration from generated audio
        if audio_data.ndim > 1:
            actual_duration = audio_data.shape[0] / sampling_rate
        else:
            actual_duration = len(audio_data) / sampling_rate
        
        result = {
            "audio_url": audio_url,
            "duration": round(actual_duration, 2),
            "model_used": f"facebook/musicgen-stereo-{model_size}",
            "file_size": file_size,
            "sampling_rate": sampling_rate,
            "provider": "musicgen"
        }
        
        logger.info(f"MusicGen generation completed: {result}")
        return result

    async def _generate_single_segment(self, synthesizer, description: str, duration: int) -> Tuple[NumpyArray, int]:
        """Generate a single segment of music (≤30 seconds) with timeout."""
        # Optimized token calculation for better generation speed vs quality
        if duration <= 5:
            max_tokens = min(duration * 32, 160)  # Efficient rate for very short clips
        elif duration <= 10:
            max_tokens = min(duration * 25, 250)  # Balanced rate for short clips
        elif duration <= 20:
            max_tokens = min(duration * 18, 360)  # Conservative rate for medium clips
        else:
            max_tokens = min(duration * 12, 400)  # Very conservative for long clips
        
        # Add timeout for generation (much more generous for production environments)
        # Scale timeout more aggressively for longer durations
        if duration <= 5:
            generation_timeout = max(duration * 60, 300)  # 60x for short clips, min 5 minutes
        elif duration <= 10:
            generation_timeout = max(duration * 90, 600)  # 90x for medium clips, min 10 minutes
        else:
            generation_timeout = max(duration * 120, 1200)  # 120x for long clips, min 20 minutes
        
        logger.info(f"Generating single segment: {duration}s with {max_tokens} tokens (timeout: {generation_timeout}s)")
        
        try:
            music = await asyncio.wait_for(
                asyncio.to_thread(
                    lambda: synthesizer(description, forward_params={"max_new_tokens": max_tokens})
                ),
                timeout=generation_timeout
            )
            return music["audio"], music["sampling_rate"]
        except asyncio.TimeoutError:
            logger.error(f"Music generation timed out after {generation_timeout}s for {duration}s segment")
            raise Exception(f"Music generation timed out. Duration {duration}s took longer than {generation_timeout}s to generate.")
        except Exception as e:
            logger.error(f"Error during music generation: {str(e)}")
            raise Exception(f"Music generation failed: {str(e)}")

    async def _generate_with_sliding_window(self, synthesizer, description: str, target_duration: int) -> Tuple[NumpyArray, int]:
        """Generate longer music using sliding window approach (30s chunks with 20s overlap)."""
        logger.info(f"Starting sliding window generation for {target_duration}s")
        
        # Parameters for sliding window - using shorter segments for reliability
        segment_duration = 10  # Each segment is 10 seconds (more reliable)
        overlap_duration = 5   # Keep last 5 seconds as context
        step_duration = 5      # Move forward by 5 seconds each time
        
        # Generate first segment (using the smaller segment duration)
        logger.info(f"Generating initial {segment_duration}-second segment")
        first_audio, sampling_rate = await self._generate_single_segment(
            synthesizer, description, segment_duration
        )
        
        # Convert to float for processing
        if np is not None and first_audio.dtype != np.float32:
            first_audio = first_audio.astype(np.float32) / 32767.0
        
        # Initialize with first segment
        final_audio = first_audio.copy()
        current_duration = segment_duration
        
        # Generate subsequent segments using sliding window
        segment_count = 1
        while current_duration < target_duration:
            remaining_duration = target_duration - current_duration
            next_segment_duration = min(segment_duration, remaining_duration + overlap_duration)
            
            logger.info(f"Generating segment {segment_count + 1}: {next_segment_duration}s with context")
            
            # Use the last 20 seconds as context for next generation
            overlap_samples = int(overlap_duration * sampling_rate)
            context_audio = final_audio[-overlap_samples:]  # Reserved for future context-aware generation
            
            # Generate next segment with context
            # Note: In a real implementation, you'd pass the context to the model
            # For now, we'll generate independently and blend
            # TODO: Use context_audio for context-aware generation
            next_audio, _ = await self._generate_single_segment(
                synthesizer, description, next_segment_duration
            )
            
            # Convert to float for processing
            if np is not None and next_audio.dtype != np.float32:
                next_audio = next_audio.astype(np.float32) / 32767.0
            
            # Take only the new part (skip the overlapping portion)
            step_samples = int(step_duration * sampling_rate)
            new_audio = next_audio[overlap_samples:overlap_samples + step_samples]
            
            # Blend the transition to avoid clicks (shorter blend for shorter segments)
            blend_samples = int(0.2 * sampling_rate)  # 0.2 second blend
            if len(new_audio) > blend_samples and len(final_audio) > blend_samples:
                # Create a smooth transition
                if np is not None:
                    fade_out = np.linspace(1.0, 0.0, blend_samples)
                    fade_in = np.linspace(0.0, 1.0, blend_samples)
                else:
                    raise Exception("NumPy not available for audio processing")
                
                # Apply fade to overlapping region
                final_audio[-blend_samples:] *= fade_out.reshape(-1, 1) if final_audio.ndim > 1 else fade_out
                new_audio[:blend_samples] *= fade_in.reshape(-1, 1) if new_audio.ndim > 1 else fade_in
                
                # Mix the overlapping part
                final_audio[-blend_samples:] += new_audio[:blend_samples]
                new_audio = new_audio[blend_samples:]
            
            # Append the new audio
            if np is not None:
                final_audio = np.concatenate([final_audio, new_audio], axis=0)
            else:
                raise Exception("NumPy not available for audio processing")
            current_duration += step_duration
            segment_count += 1
            
            logger.info(f"Total duration now: {current_duration}s / {target_duration}s")
            
            # Safety check to prevent infinite loops
            if segment_count > 20:  # Max 20 segments = ~3.5 minutes
                logger.warning(f"Reached maximum segments ({segment_count}), stopping generation")
                break
        
        # Trim to exact target duration
        target_samples = int(target_duration * sampling_rate)
        if len(final_audio) > target_samples:
            final_audio = final_audio[:target_samples]
        
        logger.info(f"Sliding window generation complete: {len(final_audio) / sampling_rate:.2f}s")
        return final_audio, sampling_rate

    async def _generate_with_lyria(self, job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Generate music using Google's Lyria model."""
        if not LYRIA_AVAILABLE:
            raise Exception("Lyria dependencies not available. Please install: google-generativeai")
        
        if not self.genai_client:
            raise Exception("GEMINI_API_KEY not configured or Lyria client not initialized")
        
        description = data.get('description') or "instrumental music"  # Provide default if None
        duration = data.get('duration', 8)
        output_format = data.get('output_format', 'wav')
        bpm = data.get('bpm', 120)
        density = data.get('density', 0.5)
        brightness = data.get('brightness', 0.5)
        guidance = data.get('guidance', 3.0)
        scale = data.get('scale', 'SCALE_UNSPECIFIED')
        mute_bass = data.get('mute_bass', False)
        mute_drums = data.get('mute_drums', False)
        only_bass_and_drums = data.get('only_bass_and_drums', False)
        music_generation_mode = data.get('music_generation_mode', 'QUALITY')
        
        # Add timeout for the entire operation (shorter to prevent hanging)
        timeout_duration = min(duration + 45, 90)  # Maximum 90 seconds, or duration + 45s buffer
        
        try:
            logger.info(f"Starting Lyria music generation for {duration}s")
            
            async def generate_with_timeout():
                # Collect audio data
                audio_chunks = []
                
                async def receive_audio(session):
                    """Background task to collect audio chunks."""
                    nonlocal audio_chunks
                    try:
                        async for message in session.receive():
                            logger.debug(f"Received message from Lyria session")
                            if hasattr(message, 'server_content') and hasattr(message.server_content, 'audio_chunks'):
                                for chunk in message.server_content.audio_chunks:
                                    if hasattr(chunk, 'data'):
                                        audio_chunks.append(chunk.data)
                                        logger.debug(f"Collected audio chunk, total chunks: {len(audio_chunks)}")
                    except Exception as e:
                        logger.error(f"Error in receive_audio: {e}")
                
                # Use the correct async context manager pattern
                logger.info("Connecting to Lyria real-time music generation...")
                if self.genai_client is None:
                    raise Exception("Lyria client not available")
                async with (
                    self.genai_client.aio.live.music.connect(model='models/lyria-realtime-exp') as session,
                    asyncio.TaskGroup() as tg,
                ):
                    logger.info("Connected to Lyria session successfully")
                    
                    # Set up task to receive server messages
                    tg.create_task(receive_audio(session))
                    
                    # Configure music generation parameters
                    logger.info("Setting weighted prompts...")
                    if types is None:
                        raise Exception("Google AI types not available")
                    await session.set_weighted_prompts(
                        prompts=[
                            types.WeightedPrompt(text=description, weight=1.0),
                        ]
                    )
                    
                    # Create configuration object
                    logger.info(f"Setting music generation config (BPM: {bpm}, temperature: {guidance / 6.0})...")
                    if types is None:
                        raise Exception("Google AI types not available")
                    config = types.LiveMusicGenerationConfig(
                        bpm=bpm,
                        temperature=guidance / 6.0,  # Convert guidance (0-6) to temperature (0-1)
                    )
                    
                    # Add other parameters if the API supports them
                    # Note: Some parameters might not be available in the current API
                    await session.set_music_generation_config(config=config)
                    
                    # Start music generation
                    logger.info("Starting music playback...")
                    await session.play()
                    
                    # Wait for the specified duration with periodic logging
                    for i in range(duration):
                        await asyncio.sleep(1)
                        if i % 5 == 0:  # Log every 5 seconds
                            logger.info(f"Music generation progress: {i+1}/{duration}s, chunks collected: {len(audio_chunks)}")
                    
                    # Stop generation
                    logger.info("Stopping music generation...")
                    await session.stop()
                    
                return audio_chunks
            
            # Run with timeout
            logger.info(f"Starting generation with {timeout_duration}s timeout...")
            audio_chunks = await asyncio.wait_for(generate_with_timeout(), timeout=timeout_duration)
            
            if not audio_chunks:
                raise Exception("No audio data received from Lyria")
            
            logger.info(f"Received {len(audio_chunks)} audio chunks from Lyria")
            
            # Combine audio chunks
            # Lyria outputs 16-bit PCM at 48kHz stereo
            sampling_rate = 48000
            
            # Convert chunks to numpy arrays and concatenate
            if np is None:
                raise Exception("NumPy not available for audio processing")
            audio_arrays = []
            for chunk in audio_chunks:
                if isinstance(chunk, bytes):
                    # Convert bytes to numpy array (assuming 16-bit PCM stereo)
                    audio_array = np.frombuffer(chunk, dtype=np.int16)
                    audio_arrays.append(audio_array)
            
            if not audio_arrays:
                raise Exception("No valid audio data could be processed from chunks")
            
            audio_data = np.concatenate(audio_arrays)
            
            # Ensure stereo format
            if len(audio_data) % 2 == 0:
                audio_data = audio_data.reshape(-1, 2)
            else:
                # If odd number of samples, duplicate for stereo
                if np is not None:
                    audio_data = np.column_stack((audio_data, audio_data))
                else:
                    raise Exception("NumPy not available for audio processing")
            
            # Trim to exact duration
            target_samples = int(duration * sampling_rate)
            if len(audio_data) > target_samples:
                audio_data = audio_data[:target_samples]
            
            # Generate unique filename
            filename = f"lyria_{job_id or uuid.uuid4().hex}"
            temp_wav_path = os.path.join(self.temp_dir, f"{filename}.wav")
            
            # Save WAV file
            if scipy is not None:
                scipy.io.wavfile.write(temp_wav_path, rate=sampling_rate, data=audio_data)
            else:
                raise Exception("SciPy not available for audio file writing")
            
            # For now, only support WAV output (MP3 conversion would require ffmpeg)
            if output_format.lower() == "mp3":
                logger.warning("MP3 output not yet supported, defaulting to WAV")
                output_format = "wav"
            
            output_path = temp_wav_path
            
            # Upload to S3
            file_size = os.path.getsize(output_path)
            object_name = f"audio/music/{filename}.{output_format}"
            
            audio_url = await s3_service.upload_file(
                file_path=output_path,
                object_name=object_name
            )
            
            # Clean up temp file
            if os.path.exists(output_path):
                os.remove(output_path)
            
            # Calculate actual duration from generated audio
            actual_duration = len(audio_data) / sampling_rate
            
            return {
                "audio_url": audio_url,
                "duration": round(actual_duration, 2),
                "model_used": "models/lyria-realtime-exp",
                "file_size": file_size,
                "sampling_rate": sampling_rate,
                "provider": "lyria",
                "parameters": {
                    "bpm": bpm,
                    "density": density,
                    "brightness": brightness,
                    "guidance": guidance,
                    "scale": scale,
                    "mute_bass": mute_bass,
                    "mute_drums": mute_drums,
                    "only_bass_and_drums": only_bass_and_drums,
                    "music_generation_mode": music_generation_mode
                }
            }
            
        except asyncio.TimeoutError:
            logger.error(f"Lyria music generation timed out after {timeout_duration}s")
            raise Exception(f"Lyria music generation timed out after {timeout_duration} seconds. The service may be overloaded or experiencing issues.")
        except Exception as e:
            logger.error(f"Lyria music generation failed: {str(e)}", exc_info=True)
            # Provide a more helpful error message
            if "Client" in str(e) or "connect" in str(e).lower():
                raise Exception(f"Failed to connect to Lyria service. Please check your GEMINI_API_KEY or try again later. Error: {str(e)}")
            elif "timeout" in str(e).lower():
                raise Exception(f"Lyria service timeout. The service may be busy. Please try with a shorter duration or try again later.")
            else:
                raise Exception(f"Lyria music generation failed: {str(e)}")
    
    def clear_cache(self):
        """Clear model cache to free memory"""
        self.model_cache.clear()
        logger.info("MusicGen model cache cleared")
    
    async def health_check(self) -> dict[str, Any]:
        """Health check for music generation service"""
        status = {
            "service": "music_generation",
            "status": "healthy",
            "dependencies": {
                "transformers": MUSIC_GENERATION_AVAILABLE,
                "lyria": LYRIA_AVAILABLE,
                "gemini_api_key": bool(self.gemini_api_key)
            },
            "cached_models": list(self.model_cache.keys()),
            "cache_count": len(self.model_cache),
            "warmup_started": self._warmup_started
        }
        
        # Check if models are ready
        default_model_size = os.environ.get('MUSICGEN_MODEL_SIZE', 'small')
        default_model_name = f"facebook/musicgen-stereo-{default_model_size}"
        status["default_model_ready"] = default_model_name in self.model_cache
        
        return status
    
    async def warm_up_model(self, model_size: Optional[str] = None) -> dict[str, Any]:
        """Manually warm up a specific model"""
        if not model_size:
            model_size = os.environ.get('MUSICGEN_MODEL_SIZE', 'small')
        
        try:
            start_time = asyncio.get_event_loop().time()
            await self._get_model(model_size)
            end_time = asyncio.get_event_loop().time()
            
            return {
                "success": True,
                "model_size": model_size,
                "load_time": round(end_time - start_time, 2),
                "cached": True
            }
        except Exception as e:
            return {
                "success": False,
                "model_size": model_size,
                "error": str(e)
            }


# Create a singleton instance
music_generation_service = MusicGenerationService()