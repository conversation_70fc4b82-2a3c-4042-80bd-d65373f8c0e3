"""
Simplified music generation service using Meta's MusicGen model.
Based on the Musicgen-Text-to-Music implementation.
"""
import os
import tempfile
import logging
import uuid
import asyncio
from typing import Any, Optional, Tuple

# Runtime imports with graceful fallback
try:
    import torchaudio
    from audiocraft.models import MusicGen
    from audiocraft.data.audio import audio_write
    import torch
    MUSIC_GENERATION_AVAILABLE = True
except ImportError as e:
    MUSIC_GENERATION_AVAILABLE = False
    missing_deps = str(e)
    # Create stub objects for runtime when dependencies missing
    torchaudio = None
    MusicGen = None
    audio_write = None
    torch = None

logger = logging.getLogger(__name__)


class MusicGenerationService:
    """Simplified service for generating music using Meta's MusicGen model"""
    
    def __init__(self):
        self.model_cache = {}
        self.model_loading_lock = asyncio.Lock()  # Prevent concurrent model loading
        self.temp_dir = os.environ.get('LOCAL_STORAGE_PATH', '/tmp')
        
        if not MUSIC_GENERATION_AVAILABLE:
            logger.warning(f"MusicGen dependencies not available: {missing_deps}. Please install: audiocraft, torch, torchaudio")
        else:
            logger.info("MusicGen dependencies available")
        
        # Start background model warmup if enabled (only if event loop is running)
        if os.environ.get('ENABLE_MODEL_WARMUP', 'true').lower() == 'true':
            try:
                asyncio.create_task(self._background_warmup())
            except RuntimeError:
                # No event loop running, warmup will be skipped
                logger.info("No event loop running, skipping background warmup")

    async def _get_model(self, model_size: str = "small"):
        """Get or load the MusicGen model with caching"""
        if not MUSIC_GENERATION_AVAILABLE:
            raise Exception("Music generation dependencies not available. Please install: audiocraft, torch, torchaudio")
        
        model_name = f"facebook/musicgen-{model_size}"
        
        # Use lock to prevent concurrent loading of the same model
        async with self.model_loading_lock:
            # Double-check pattern - model might have been loaded while waiting for lock
            if model_name in self.model_cache:
                logger.info(f"Using already loaded model: {model_name}")
                return self.model_cache[model_name]
            
            try:
                logger.info(f"Loading MusicGen model: {model_name}")
                
                # Load model using audiocraft
                if MusicGen is None:
                    raise Exception("MusicGen not available - audiocraft not properly imported")

                # Load model in a separate thread to avoid blocking
                # Type checker: MusicGen is guaranteed to not be None here due to the check above
                musicgen_class = MusicGen  # Create local variable to help type checker
                model = await asyncio.to_thread(
                    lambda: musicgen_class.get_pretrained(model_name)
                )
                
                self.model_cache[model_name] = model
                logger.info(f"Model {model_name} loaded successfully")
                
            except Exception as e:
                logger.error(f"Failed to load model {model_name}: {str(e)}")
                raise Exception(f"Failed to load MusicGen model: {str(e)}")
        
        return self.model_cache[model_name]
    
    async def _background_warmup(self):
        """Background task to warm up models during startup"""
        if not MUSIC_GENERATION_AVAILABLE:
            return
        
        logger.info("🔥 Starting background model warmup")
        
        try:
            # Wait a bit to let the service start up
            await asyncio.sleep(30)
            
            # Warm up the default model
            model_size = os.environ.get('MUSICGEN_MODEL_SIZE', 'small')
            logger.info(f"🔥 Warming up {model_size} model in background")
            
            async with self.model_loading_lock:
                if f"facebook/musicgen-{model_size}" not in self.model_cache:
                    try:
                        await self._get_model(model_size)
                        logger.info(f"✅ Background warmup completed for {model_size} model")
                    except Exception as e:
                        logger.warning(f"⚠️ Background warmup failed for {model_size} model: {e}")
        except Exception as e:
            logger.error(f"❌ Background warmup task failed: {e}")

    async def generate_music_tensors(self, description: str, duration: int):
        """Generate music tensors using MusicGen model"""
        logger.info(f"Description: {description}")
        logger.info(f"Duration: {duration}")
        
        model = await self._get_model('small')
        model.set_generation_params(duration=duration)
        wav = model.generate([description])
        
        return wav, model.sample_rate
    
    async def process_music_generation(self, job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """
        Process music generation job using MusicGen.
        
        Args:
            job_id: The job ID
            data: Dictionary containing music generation parameters
            
        Returns:
            Dictionary with music generation results
        """
        try:
            description = data.get('description') or "instrumental music"
            duration = data.get('duration', 8)
            # Note: output_format is always 'wav' in simplified implementation
            
            # Ensure duration is a valid number
            if duration is None or not isinstance(duration, (int, float)) or duration <= 0:
                duration = 8  # Default to 8 seconds
            
            if not description:
                raise ValueError("Description is required for music generation")
            
            logger.info(f"Generating music for description: '{description}' (duration: {duration}s)")
            
            # Generate music using simplified method
            wav, sample_rate = await self.generate_music_tensors(description, int(duration))
            
            # Generate unique filename
            filename = f"musicgen_{job_id or uuid.uuid4().hex}"
            temp_wav_path = os.path.join(self.temp_dir, f"{filename}.wav")
            
            # Save the audio file
            if audio_write is None:
                raise Exception("audio_write not available - audiocraft not properly imported")
            
            # Save audio using audiocraft's audio_write
            for idx, one_wav in enumerate(wav):
                audio_write(
                    temp_wav_path.replace('.wav', ''),  # audio_write adds extension
                    one_wav.cpu(), 
                    sample_rate, 
                    strategy="loudness", 
                    loudness_compressor=True
                )
                break  # Only process first audio sample
            
            # Upload to S3
            from app.services.s3 import s3_service
            s3_key = f"audio/music/{filename}.wav"
            audio_url = await s3_service.upload_file(temp_wav_path, s3_key)
            
            # Clean up temp file
            if os.path.exists(temp_wav_path):
                os.remove(temp_wav_path)
            
            result = {
                "audio_url": audio_url,
                "duration": float(duration),
                "model_used": "facebook/musicgen-small",
                "file_size": 0,  # Could calculate if needed
                "sampling_rate": sample_rate,
                "provider": "musicgen"
            }
            
            logger.info(f"MusicGen generation completed: {result}")
            return result
                
        except Exception as e:
            logger.error(f"Music generation failed: {str(e)}")
            raise


# Create a singleton instance
music_generation_service = MusicGenerationService()
