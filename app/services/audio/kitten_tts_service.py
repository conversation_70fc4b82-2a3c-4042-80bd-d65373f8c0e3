"""
KittenTTS service for ultra-lightweight text-to-speech synthesis.
Real implementation using Hugging Face Hub and ONNX Runtime.
"""
import os
import tempfile
import logging
import asyncio
import uuid
import json
from typing import Optional, List, Dict, Any, Protocol, TYPE_CHECKING

if TYPE_CHECKING:
    import onnxruntime as ort
    import numpy as np
    from huggingface_hub import hf_hub_download
    import soundfile as sf

try:
    import onnxruntime as ort
    import numpy as np
    from huggingface_hub import hf_hub_download
    import soundfile as sf
    import re
    ONNX_AVAILABLE = True
except ImportError as e:
    ort = None  # type: ignore
    np = None  # type: ignore
    hf_hub_download = None  # type: ignore
    sf = None  # type: ignore
    re = None  # type: ignore
    ONNX_AVAILABLE = False
    logging.warning(f"ONNX dependencies not available: {e}")

# Protocol for KittenTTS interface
class KittenTTSProtocol(Protocol):
    def __init__(self, model_name: str, cache_dir: Optional[str] = None) -> None: ...
    def generate_to_file(self, text: str, output_path: str, voice: str, speed: float, sample_rate: int) -> None: ...
    @property
    def available_voices(self) -> List[str]: ...

# Basic text processing functions (simplified versions)
def basic_english_tokenize(text: str) -> List[str]:
    """Basic English tokenizer that splits on whitespace and punctuation."""
    if re is None:
        return text.split()
    tokens = re.findall(r"\w+|[^\w\s]", text)
    return tokens

class SimpleTextCleaner:
    """Simplified text cleaner for KittenTTS."""
    def __init__(self):
        _pad = "$"
        _punctuation = ';:,.!?¡¿—…"«»"" '
        _letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
        _letters_ipa = "ɑɐɒæɓʙβɔɕçɗɖðʤəɘɚɛɜɝɞɟʄɡɠɢʛɦɧħɥʜɨɪʝɭɬɫɮʟɱɯɰŋɳɲɴøɵɸθœɶʘɹɺɾɻʀʁɽʂʃʈʧʉʊʋⱱʌɣɤʍχʎʏʑʐʒʔʡʕʢǀǁǂǃˈˌːˑʼʴʰʱʲʷˠˤ˞↓↑→↗↘'̩'ᵻ"

        symbols = [_pad] + list(_punctuation) + list(_letters) + list(_letters_ipa)
        
        self.word_index_dictionary = {symbol: i for i, symbol in enumerate(symbols)}

    def __call__(self, text: str) -> List[int]:
        indexes = []
        for char in text:
            if char in self.word_index_dictionary:
                indexes.append(self.word_index_dictionary[char])
        return indexes

class RealKittenTTS:
    """Real KittenTTS implementation using ONNX Runtime and Hugging Face Hub."""
    
    def __init__(self, model_name: str = "KittenML/kitten-tts-nano-0.1", cache_dir: Optional[str] = None):
        if not ONNX_AVAILABLE:
            raise RuntimeError("ONNX dependencies not available. Install: pip install onnxruntime numpy huggingface_hub soundfile")
        
        self.model_name = model_name
        self.cache_dir = cache_dir or os.path.join(tempfile.gettempdir(), "kitten_tts_cache")
        self.session = None
        self.voices = None
        self.text_cleaner = SimpleTextCleaner()
        
        # Available voices with better mapping
        self._available_voices = [
            'expr-voice-2-m', 'expr-voice-2-f', 'expr-voice-3-m', 'expr-voice-3-f', 
            'expr-voice-4-m', 'expr-voice-4-f', 'expr-voice-5-m', 'expr-voice-5-f'
        ]
        
        # Create cache directory
        os.makedirs(self.cache_dir, exist_ok=True)
        logging.info(f"KittenTTS cache directory: {self.cache_dir}")
        
    def _ensure_model_loaded(self):
        """Download and load the model if not already loaded."""
        if self.session is not None:
            return
            
        try:
            logging.info(f"Loading KittenTTS model from {self.model_name}")
            
            # Download config first
            if hf_hub_download is None:
                raise RuntimeError("huggingface_hub not available")
                
            config_path = hf_hub_download(
                repo_id=self.model_name,
                filename="config.json",
                cache_dir=self.cache_dir,
                local_files_only=False
            )
            logging.info(f"Downloaded config: {config_path}")
            
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            logging.info(f"Model config: {config}")
            
            if config.get("type") != "ONNX1":
                raise ValueError(f"Unsupported model type: {config.get('type')}")
            
            # Download model file
            model_file = config.get("model_file", "model.onnx")
            model_path = hf_hub_download(
                repo_id=self.model_name,
                filename=model_file,
                cache_dir=self.cache_dir,
                local_files_only=False
            )
            logging.info(f"Downloaded model: {model_path}")
            
            # Download voices file
            voices_file = config.get("voices", "voices.npy")
            voices_path = hf_hub_download(
                repo_id=self.model_name,
                filename=voices_file,
                cache_dir=self.cache_dir,
                local_files_only=False
            )
            logging.info(f"Downloaded voices: {voices_path}")
            
            # Load model and voices
            if ort is None or np is None:
                raise RuntimeError("onnxruntime or numpy not available")
            
            # Set up ONNX session options for better performance
            sess_options = ort.SessionOptions()
            sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
            sess_options.execution_mode = ort.ExecutionMode.ORT_SEQUENTIAL
            
            self.session = ort.InferenceSession(model_path, sess_options)
            
            # Load voices - handle different formats
            try:
                voices_data = np.load(voices_path, allow_pickle=True)
                if hasattr(voices_data, 'item'):
                    self.voices = voices_data.item()
                elif isinstance(voices_data, np.ndarray):
                    # If it's just an array, create a mapping
                    self.voices = {voice: voices_data[i] for i, voice in enumerate(self._available_voices[:len(voices_data)])}
                else:
                    # If it's an NPZ file, try to get the data
                    if hasattr(voices_data, 'files'):
                        # It's an NPZ file with multiple arrays
                        voice_arrays = {}
                        for file_key in voices_data.files:
                            voice_arrays[file_key] = voices_data[file_key]
                        self.voices = voice_arrays
                    else:
                        self.voices = dict(voices_data)
                
                logging.info(f"KittenTTS model loaded successfully")
                logging.info(f"Voices loaded: {type(self.voices)}")
                if isinstance(self.voices, dict):
                    logging.info(f"Available voice keys: {list(self.voices.keys())}")
                else:
                    logging.info(f"Voices shape: {getattr(self.voices, 'shape', 'no shape')}")
            except Exception as voice_error:
                logging.error(f"Failed to load voices file: {voice_error}")
                # Create fallback voice embeddings
                logging.warning("Creating fallback voice embeddings")
                embedding_size = 256  # Common embedding size
                self.voices = {}
                for voice in self._available_voices:
                    # Create random but consistent embeddings based on voice name
                    np.random.seed(hash(voice) % 2**32)
                    self.voices[voice] = np.random.randn(embedding_size).astype(np.float32)
                logging.info(f"Created {len(self.voices)} fallback voice embeddings")
            
        except Exception as e:
            logging.error(f"Failed to load KittenTTS model: {e}")
            logging.error(f"Model name: {self.model_name}, Cache dir: {self.cache_dir}")
            raise
    
    def _prepare_inputs(self, text: str, voice: str, speed: float = 1.0) -> Dict[str, Any]:
        """Prepare ONNX model inputs from text and voice parameters."""
        if voice not in self._available_voices:
            logging.warning(f"Voice '{voice}' not in available voices, using default")
            voice = self._available_voices[0]  # Use first available voice
        
        # Clean and prepare text
        text = text.strip()
        if not text:
            text = "Hello world"  # Fallback text
        
        # Simple tokenization - convert text to character indices
        tokens = self.text_cleaner(text)
        
        # Ensure we have valid tokens
        if not tokens:
            tokens = [1, 2, 3]  # Fallback tokens
        
        # Add padding tokens
        tokens.insert(0, 0)  # Start token
        tokens.append(0)     # End token
        
        if np is None:
            raise RuntimeError("numpy not available")
            
        # Create input tensor
        input_ids = np.array([tokens], dtype=np.int64)
        
        if self.voices is None:
            raise RuntimeError("voices not loaded")
        
        # Get voice embedding
        if isinstance(self.voices, dict):
            if voice in self.voices:
                ref_s = self.voices[voice]
            else:
                # Try to find a similar voice or use the first one
                available_keys = list(self.voices.keys())
                logging.warning(f"Voice {voice} not found, available: {available_keys}")
                ref_s = self.voices[available_keys[0]]
        else:
            # If voices is an array, use index-based access
            voice_index = min(len(self._available_voices) - 1, self._available_voices.index(voice) if voice in self._available_voices else 0)
            ref_s = self.voices[voice_index] if hasattr(self.voices, '__getitem__') else self.voices
        
        # Ensure ref_s is a numpy array
        if not isinstance(ref_s, np.ndarray):
            ref_s = np.array(ref_s, dtype=np.float32)
        
        return {
            "input_ids": input_ids,
            "style": ref_s,
            "speed": np.array([speed], dtype=np.float32),
        }
    
    def generate_to_file(self, text: str, output_path: str, voice: str = "expr-voice-5-m", speed: float = 1.0, sample_rate: int = 24000) -> None:
        """Generate speech and save to file."""
        logging.info(f"Generating speech: text='{text[:50]}...', voice={voice}, speed={speed}")
        
        self._ensure_model_loaded()
        
        # Prepare inputs
        onnx_inputs = self._prepare_inputs(text, voice, speed)
        logging.info(f"ONNX inputs prepared: {list(onnx_inputs.keys())}")
        
        # Run inference
        if self.session is None:
            raise RuntimeError("session not initialized")
        
        try:
            outputs = self.session.run(None, onnx_inputs)
            logging.info(f"ONNX inference completed, got {len(outputs)} outputs")
        except Exception as e:
            logging.error(f"ONNX inference failed: {e}")
            raise
        
        # Process output
        audio = outputs[0]
        logging.info(f"Raw audio output shape: {getattr(audio, 'shape', 'no shape')}, type: {type(audio)}")
        
        # Convert to numpy array if needed
        if np is None:
            raise RuntimeError("numpy not available for audio processing")
            
        if not isinstance(audio, np.ndarray):
            if hasattr(audio, 'numpy') and callable(getattr(audio, 'numpy', None)):
                # Handle tensor types that have .numpy() method
                numpy_method = getattr(audio, 'numpy')
                audio = numpy_method()
            elif hasattr(audio, '__array__'):
                audio = np.asarray(audio)
            else:
                audio = np.array(audio)
        
        # Ensure audio is 1D
        if len(audio.shape) > 1:
            audio = audio.flatten()
        
        # Convert to float32 if needed
        if audio.dtype != np.float32:
            audio = audio.astype(np.float32)
        
        # Normalize audio to [-1, 1] range
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))
        
        # Trim silence (more conservative)
        try:
            if len(audio) > 10000:  # Only trim if we have enough samples
                # Find non-silent regions
                threshold = 0.01  # Silence threshold
                non_silent = np.abs(audio) > threshold
                if np.any(non_silent):
                    start = np.argmax(non_silent)
                    end = len(audio) - np.argmax(non_silent[::-1])
                    audio = audio[start:end]
                    logging.info(f"Trimmed audio from {start} to {end}")
        except Exception as e:
            logging.warning(f"Could not trim silence: {e}")
        
        # Ensure minimum length
        if len(audio) < 1000:
            logging.warning(f"Audio too short ({len(audio)} samples), padding")
            audio = np.pad(audio, (0, 1000 - len(audio)), 'constant')
        
        logging.info(f"Final audio: {len(audio)} samples, range: [{np.min(audio):.3f}, {np.max(audio):.3f}]")
        
        # Save to file
        if sf is None:
            raise RuntimeError("soundfile not available")
        
        try:
            sf.write(output_path, audio, sample_rate)
            file_size = os.path.getsize(output_path)
            logging.info(f"KittenTTS generated audio saved to: {output_path} ({file_size} bytes)")
        except Exception as e:
            logging.error(f"Failed to save audio file: {e}")
            raise
    
    @property
    def available_voices(self) -> List[str]:
        return self._available_voices.copy()

# Mock implementation for fallback
class MockKittenTTS:
    def __init__(self, model_name: str, cache_dir: Optional[str] = None) -> None:
        self.model_name = model_name
        self.cache_dir = cache_dir
        logging.warning("Using MockKittenTTS - install onnxruntime, numpy, huggingface_hub, soundfile for real TTS")
        
    def generate_to_file(self, text: str, output_path: str, voice: str, speed: float, sample_rate: int) -> None:
        """Generate a simple tone instead of silence for testing."""
        _ = voice, speed  # Mark parameters as used to avoid warnings
        logging.warning(f"MockKittenTTS generating test tone for: '{text[:50]}...'")
        
        import wave
        import numpy as np
        import math
        
        # Generate a simple tone instead of silence
        duration = max(1.0, len(text.split()) * 0.6)  # Estimate duration based on text length
        samples_count = int(sample_rate * duration)
        
        # Create a simple sine wave tone (440 Hz for testing)
        frequency = 440  # A4 note
        samples = []
        for i in range(samples_count):
            # Create a sine wave with envelope to avoid clicks
            t = i / sample_rate
            envelope = 0.5 * (1 - math.cos(2 * math.pi * t / duration))  # Smooth envelope
            sample = envelope * 0.1 * math.sin(2 * math.pi * frequency * t)  # Low volume
            samples.append(int(sample * 32767))  # Convert to 16-bit integer
        
        samples_array = np.array(samples, dtype=np.int16)
        
        try:
            with wave.open(output_path, 'w') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(samples_array.tobytes())
            
            file_size = os.path.getsize(output_path)
            logging.info(f"MockKittenTTS generated test tone: {output_path} ({file_size} bytes)")
        except Exception as e:
            logging.error(f"Failed to create mock audio file: {e}")
            raise
    
    @property
    def available_voices(self) -> List[str]:
        return [
            'expr-voice-2-m', 'expr-voice-2-f', 'expr-voice-3-m', 'expr-voice-3-f', 
            'expr-voice-4-m', 'expr-voice-4-f', 'expr-voice-5-m', 'expr-voice-5-f'
        ]

# Use real implementation if dependencies are available, otherwise fall back to mock
if ONNX_AVAILABLE:
    KittenTTSClass = RealKittenTTS
    KITTEN_AVAILABLE = True
    logging.info("Using real KittenTTS implementation")
else:
    KittenTTSClass = MockKittenTTS  # type: ignore
    KITTEN_AVAILABLE = True
    logging.warning("Using mock KittenTTS implementation - install onnxruntime, numpy, huggingface_hub, soundfile for real TTS")

# Configure logging
logger = logging.getLogger(__name__)

# Default configuration
DEFAULT_VOICE = "expr-voice-5-m"
MODEL_NAME = "KittenML/kitten-tts-nano-0.1"
CACHE_DIR = os.environ.get("KITTEN_CACHE_DIR", os.path.join(tempfile.gettempdir(), "kitten_tts_cache"))

class KittenTTSService:
    """KittenTTS service for ultra-lightweight TTS."""
    
    def __init__(self):
        """Initialize KittenTTS service."""
        self.tts_model: Optional[KittenTTSProtocol] = None
        self.model_initialized = False
        self.available_voices = [
            'expr-voice-2-m', 'expr-voice-2-f', 'expr-voice-3-m', 'expr-voice-3-f', 
            'expr-voice-4-m', 'expr-voice-4-f', 'expr-voice-5-m', 'expr-voice-5-f'
        ]
        logger.info("Initializing KittenTTS service")
        
    def is_available(self) -> bool:
        """Check if KittenTTS is available and initialized."""
        return KITTEN_AVAILABLE and self.model_initialized
    
    async def initialize_model(self) -> bool:
        """Initialize the KittenTTS model."""
        if not KITTEN_AVAILABLE:
            logger.warning("KittenTTS not available - missing dependencies")
            return False
            
        if self.model_initialized:
            return True
            
        try:
            logger.info(f"Initializing KittenTTS model... (ONNX available: {ONNX_AVAILABLE})")
            
            # Create cache directory
            os.makedirs(CACHE_DIR, exist_ok=True)
            logger.info(f"Using cache directory: {CACHE_DIR}")
            
            # Initialize model in thread to avoid blocking
            loop = asyncio.get_event_loop()
            self.tts_model = await loop.run_in_executor(
                None, 
                lambda: KittenTTSClass(MODEL_NAME, cache_dir=CACHE_DIR)
            )
            
            # Test the model with a simple phrase
            if ONNX_AVAILABLE:
                logger.info("Testing KittenTTS model with sample text...")
                test_path = os.path.join(tempfile.gettempdir(), "kitten_test.wav")
                try:
                    # Ensure model is available before calling generate_to_file
                    if self.tts_model is not None:
                        await loop.run_in_executor(
                            None,
                            lambda: self.tts_model.generate_to_file("Hello", test_path, DEFAULT_VOICE, 1.0, 24000)  # type: ignore
                        )
                    if os.path.exists(test_path):
                        file_size = os.path.getsize(test_path)
                        logger.info(f"KittenTTS test successful: {file_size} bytes generated")
                        os.remove(test_path)  # Clean up
                    else:
                        logger.warning("KittenTTS test file not created")
                except Exception as test_error:
                    logger.warning(f"KittenTTS test failed: {test_error}")
            
            self.model_initialized = True
            logger.info(f"KittenTTS model initialized successfully (using {'real' if ONNX_AVAILABLE else 'mock'} implementation)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize KittenTTS model: {e}")
            logger.error(f"Model name: {MODEL_NAME}, Cache: {CACHE_DIR}")
            return False
    
    async def generate_speech(
        self,
        text: str,
        voice: str = DEFAULT_VOICE,
        speed: float = 1.0,
        response_format: str = "wav"
    ) -> str:
        """
        Generate speech from text using KittenTTS.
        
        Args:
            text: Text to convert to speech
            voice: Voice name from available voices
            speed: Speech speed (1.0 = normal)
            response_format: Output format (wav only for KittenTTS)
            
        Returns:
            Path to the generated audio file
            
        Raises:
            ValueError: If generation fails
        """
        if not self.is_available():
            success = await self.initialize_model()
            if not success:
                raise ValueError("KittenTTS service not available")
        
        if voice not in self.available_voices:
            logger.warning(f"Voice '{voice}' not available, using default '{DEFAULT_VOICE}'")
            voice = DEFAULT_VOICE
        
        # Force WAV format for KittenTTS
        if response_format.lower() != "wav":
            logger.info(f"KittenTTS only supports WAV format, converting from {response_format}")
        
        try:
            # Generate unique filename
            output_filename = f"kitten_tts_{uuid.uuid4().hex}.wav"
            output_path = os.path.join(tempfile.gettempdir(), output_filename)
            
            logger.info(f"Generating speech with KittenTTS: voice={voice}, speed={speed}")
            
            # Generate audio in executor to avoid blocking
            if self.tts_model is None:
                raise ValueError("TTS model not initialized")
                
            # Capture model reference for lambda
            model = self.tts_model
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: model.generate_to_file(
                    text=text,
                    output_path=output_path,
                    voice=voice,
                    speed=speed,
                    sample_rate=24000
                )
            )
            
            # Verify file was created
            if not os.path.exists(output_path):
                raise ValueError("KittenTTS failed to generate audio file")
            
            file_size = os.path.getsize(output_path)
            if file_size < 100:
                raise ValueError(f"Generated audio file too small: {file_size} bytes")
            
            logger.info(f"KittenTTS generated audio: {output_path} ({file_size} bytes)")
            return output_path
            
        except Exception as e:
            logger.error(f"KittenTTS generation failed: {e}")
            raise ValueError(f"Failed to generate speech with KittenTTS: {e}")
    
    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """
        Get list of available KittenTTS voices.
        
        Returns:
            List of voice information dictionaries
        """
        voices = []
        
        # KittenTTS voice metadata
        voice_info = {
            'expr-voice-2-m': {'gender': 'male', 'description': 'Expressive Male Voice 2'},
            'expr-voice-2-f': {'gender': 'female', 'description': 'Expressive Female Voice 2'},
            'expr-voice-3-m': {'gender': 'male', 'description': 'Expressive Male Voice 3'},
            'expr-voice-3-f': {'gender': 'female', 'description': 'Expressive Female Voice 3'},
            'expr-voice-4-m': {'gender': 'male', 'description': 'Expressive Male Voice 4'},
            'expr-voice-4-f': {'gender': 'female', 'description': 'Expressive Female Voice 4'},
            'expr-voice-5-m': {'gender': 'male', 'description': 'Expressive Male Voice 5'},
            'expr-voice-5-f': {'gender': 'female', 'description': 'Expressive Female Voice 5'},
        }
        
        for voice_name in self.available_voices:
            info = voice_info.get(voice_name, {'gender': 'unknown', 'description': f'Voice {voice_name}'})
            voices.append({
                "name": voice_name,
                "language": "en-US",
                "description": info['description'],
                "gender": info['gender'],
                "provider": "kitten"
            })
        
        return voices
    
    def get_supported_formats(self) -> List[str]:
        """Get supported audio formats."""
        return ["wav"]
    
    def get_supported_models(self) -> List[Dict[str, str]]:
        """Get supported KittenTTS models."""
        return [
            {
                "id": "kitten-tts-nano-0.1",
                "name": "KittenTTS Nano v0.1",
                "description": "Ultra-lightweight 15M parameter TTS model"
            }
        ]
    
    def get_voices_formatted(self) -> List[Dict[str, str]]:
        """Get formatted list of voices for API responses."""
        return [
            {"id": voice, "name": f"KittenTTS {voice}"}
            for voice in self.available_voices
        ]

# Global service instance
kitten_tts_service = KittenTTSService()