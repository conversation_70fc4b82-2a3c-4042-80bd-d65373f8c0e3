"""
Service for handling code execution with return value capture (matching dahopevi functionality).
"""
from typing import Any
from app.services.job_queue import job_queue, JobType
import logging
import subprocess
import tempfile
import json
import textwrap
import os

logger = logging.getLogger(__name__)

class CodeExecutionService:
    """
    Service for handling code execution with full dahopevi capabilities.
    """

    async def execute_python(self, job_id: str, code: str, timeout: int = 30) -> dict[str, Any]:
        """
        Execute Python code with return value capture.
        """
        try:
            params = {"code": code, "timeout": timeout}

            # Create a wrapper function that matches the expected signature
            async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
                return await self.process_python_execution(data)
            
            await job_queue.add_job(
                job_id=job_id,
                job_type=JobType.CODE_EXECUTION,
                process_func=process_wrapper,
                data=params,
            )

            return {"job_id": job_id}
        except Exception as e:
            logger.error(f"Error creating code execution job: {e}")
            raise

    async def process_python_execution(self, params: dict[str, Any]) -> dict[str, Any]:
        """
        Process Python code execution with return value capture (dahopevi-compatible).
        """
        temp_file_path = None
        try:
            code = params["code"]
            timeout = params.get("timeout", 30)
            
            # Indent user code
            indented_code = textwrap.indent(code, '    ')
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
                temp_file_path = temp_file.name
                template = '''import sys
import json
from io import StringIO
import contextlib

@contextlib.contextmanager
def capture_output():
    stdout, stderr = StringIO(), StringIO()
    old_out, old_err = sys.stdout, sys.stderr
    try:
        sys.stdout, sys.stderr = stdout, stderr
        yield stdout, stderr
    finally:
        sys.stdout, sys.stderr = old_out, old_err

def execute_code():
{}

with capture_output() as (stdout, stderr):
    try:
        result_value = execute_code()
    except Exception as e:
        print(f"Error: {{str(e)}}", file=sys.stderr)
        result_value = None

result = {{
    'stdout': stdout.getvalue(),
    'stderr': stderr.getvalue(),
    'return_value': result_value
}}
print(json.dumps(result))
'''
                
                final_code = template.format(indented_code)
                temp_file.write(final_code)
                temp_file.flush()
                
                # Log the generated code for debugging
                logger.debug(f"Generated code:\n{final_code}")
                
                try:
                    result = subprocess.run(
                        ['python3', temp_file_path],
                        capture_output=True,
                        text=True,
                        timeout=timeout
                    )
                    
                    try:
                        output = json.loads(result.stdout)
                        if result.returncode != 0 or output['stderr']:
                            return {
                                'error': output['stderr'] or 'Execution failed',
                                'stdout': output['stdout'],
                                'exit_code': result.returncode
                            }
                        
                        return {
                            'result': output['return_value'],
                            'stdout': output['stdout'],
                            'stderr': output['stderr'],
                            'exit_code': result.returncode
                        }
                        
                    except json.JSONDecodeError:
                        return {
                            'error': 'Failed to parse execution result',
                            'stdout': result.stdout,
                            'stderr': result.stderr,
                            'exit_code': result.returncode
                        }
                        
                except subprocess.TimeoutExpired:
                    return {"error": f"Execution timed out after {timeout} seconds"}
                except subprocess.SubprocessError as e:
                    return {"error": f"Execution failed: {str(e)}"}
                    
        except Exception as e:
            logger.error(f"Error processing code execution: {e}")
            raise
        finally:
            # Clean up temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except FileNotFoundError:
                    pass  # File already deleted

code_execution_service = CodeExecutionService()
