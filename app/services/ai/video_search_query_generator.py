import os
import json
import re
import logging
import asyncio
import time
from typing import Dict, Any, <PERSON>, Tuple, Union
from openai import OpenAI
from app.utils.ai_context import get_current_context

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False
    Groq = None

logger = logging.getLogger(__name__)


class VideoSearchQueryGenerator:
    """Service for generating video search queries from script content using AI."""
    
    def __init__(self):
        self.openai_client = None
        self.groq_client = None
        self._setup_clients()
    
    def _setup_clients(self):
        """Initialize AI clients based on available API keys."""
        # Setup OpenAI client
        openai_key = os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_KEY')
        if openai_key:
            # Get base URL from environment (for OpenAI-compatible LLMs)
            openai_base_url = os.getenv('OPENAI_BASE_URL')
            if openai_base_url:
                self.openai_client = OpenAI(api_key=openai_key, base_url=openai_base_url)
            else:
                self.openai_client = OpenAI(api_key=openai_key)
        
        # Setup Groq client if available
        groq_key = os.getenv('GROQ_API_KEY')
        if groq_key and GROQ_AVAILABLE and Groq is not None and len(groq_key) > 30:
            try:
                groq_base_url = os.getenv('GROQ_BASE_URL')
                if groq_base_url:
                    self.groq_client = Groq(api_key=groq_key, base_url=groq_base_url)
                else:
                    self.groq_client = Groq(api_key=groq_key)
            except Exception as e:
                logger.warning(f"Failed to initialize Groq client: {e}")
                self.groq_client = None
    
    def _get_provider_and_model(self, provider: str) -> Tuple[Any, str, str]:
        """Get the appropriate client and model based on provider preference."""
        # Get configurable model names from environment
        openai_model = os.getenv('OPENAI_MODEL', 'gpt-4o')
        groq_model = os.getenv('GROQ_MODEL', 'llama3-70b-8192')
        
        if provider == "groq" and self.groq_client:
            return self.groq_client, groq_model, "groq"
        elif provider == "openai" and self.openai_client:
            return self.openai_client, openai_model, "openai"
        elif provider == "auto":
            # Auto-select based on availability (prefer OpenAI for stability)
            if self.openai_client:
                return self.openai_client, openai_model, "openai"
            elif self.groq_client:
                return self.groq_client, groq_model, "groq"
        else:
            # Fallback to any available client
            if self.openai_client:
                return self.openai_client, openai_model, "openai"
            elif self.groq_client:
                return self.groq_client, groq_model, "groq"
        
        raise ValueError("No AI provider available. Please set OPENAI_API_KEY or GROQ_API_KEY environment variable.")
    
    def _get_video_search_prompt(self, segment_duration: float) -> str:
        """Generate the prompt for video search query generation."""
        return f"""# Video Search Query Generation for Stock Footage

{get_current_context()}

Generate search queries optimized for finding excellent stock videos on Pexels. Each query should target approximately {segment_duration} seconds of footage and use proven search patterns.

## CRITICAL REQUIREMENTS

1. **Use PROVEN search patterns that work on Pexels:**
   - PERSON + ACTION + LOCATION: "businessman typing laptop"
   - PROFESSION + ACTIVITY: "doctor examining patient" 
   - WORKPLACE SCENES: "team meeting office"
   - CLOSE-UP ACTIONS: "hands typing keyboard"

2. **GUARANTEED success patterns:**
   ✅ "businessman typing laptop"
   ✅ "scientist laboratory microscope"  
   ✅ "teacher explaining classroom"
   ✅ "chef cooking kitchen"
   ✅ "engineer blueprints office"
   ✅ "student reading library"
   ✅ "hands typing keyboard"
   ✅ "city skyline sunset"

3. **AVOID these (poor results on Pexels):**
   ❌ Abstract concepts: "innovation", "success", "growth"
   ❌ Emotional states: "happiness", "excitement"  
   ❌ Technical jargon: "algorithm", "framework"
   ❌ Generic terms: "technology", "business"

## FORMULA FOR SUCCESS
Each query should follow: **[Person/Setting] + [Action] + [Context]**

Examples:
- Script mentions research → "scientist laboratory research"
- Script mentions business → "businessman meeting office" 
- Script mentions technology → "hands typing keyboard"
- Script mentions education → "teacher explaining classroom"

## RESPONSE FORMAT - CRITICAL
You MUST return ONLY a valid JSON array. No explanations, no markdown, no extra text.

EXACT FORMAT:
[
  {{"start_time": 0, "end_time": {segment_duration}, "query": "businessman typing laptop", "visual_concept": "Professional working"}},
  {{"start_time": {segment_duration}, "end_time": {segment_duration * 2}, "query": "team meeting office", "visual_concept": "Business collaboration"}}
]

**CRITICAL RULES:**
- Return ONLY the JSON array, nothing else
- NEVER return empty content or just whitespace
- Use proven query terms that work on Pexels
- Cover the full script duration with consecutive segments
- No gaps between segments
- Focus on visual concepts that exist as stock footage
- If unsure, use these reliable queries: "businessman typing laptop", "team meeting office", "hands typing keyboard"
- ALWAYS return at least one query segment"""
    
    def _estimate_script_duration(self, script: str, speaking_rate: float = 2.8) -> float:
        """Estimate script duration based on word count and speaking rate."""
        word_count = len(script.split())
        return word_count / speaking_rate
    
    def _fix_json_formatting(self, json_str: str) -> str:
        """Fix common JSON formatting issues in AI responses."""
        # Replace typographical quotes with standard quotes
        json_str = json_str.replace("'", "'").replace(""", '"').replace(""", '"')
        json_str = json_str.replace("'", '"').replace("'", '"')
        
        # Remove markdown code blocks
        json_str = re.sub(r'```json\s*', '', json_str)
        json_str = re.sub(r'```\s*', '', json_str)
        
        # Remove extra whitespace
        json_str = re.sub(r'\s+', ' ', json_str).strip()
        
        return json_str
    
    def _parse_ai_response(self, content: str, script: str = "") -> List[Dict]:
        """Parse AI response and extract video search queries."""
        logger.info(f"Attempting to parse AI response: {content[:200]}...")
        
        # Handle empty or very short responses
        if not content or len(content.strip()) < 10:
            logger.error(f"AI returned empty or very short response: '{content}'")
            return []
        
        try:
            # Try direct JSON parsing first
            parsed = json.loads(content)
            logger.info(f"Successfully parsed JSON directly: {len(parsed)} queries")
            return parsed
        except json.JSONDecodeError as e:
            logger.debug(f"Direct JSON parsing failed: {e}")
        
        try:
            # Try fixing JSON formatting
            fixed_content = self._fix_json_formatting(content)
            parsed = json.loads(fixed_content)
            logger.info(f"Successfully parsed after JSON fixes: {len(parsed)} queries")
            return parsed
        except json.JSONDecodeError as e:
            logger.debug(f"Fixed JSON parsing failed: {e}")
        
        try:
            # Try extracting JSON from response with multiple patterns
            patterns = [
                r'\[\s*\{.*?\}\s*\]',  # Standard JSON array
                r'```json\s*(\[.*?\])\s*```',  # Markdown code block
                r'```\s*(\[.*?\])\s*```',  # Generic code block
                r'\[.*?\]',  # Any content in square brackets
            ]
            
            for pattern in patterns:
                json_match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
                if json_match:
                    json_content = json_match.group(1) if json_match.groups() else json_match.group(0)
                    try:
                        fixed_content = self._fix_json_formatting(json_content)
                        parsed = json.loads(fixed_content)
                        logger.info(f"Successfully parsed with pattern {pattern}: {len(parsed)} queries")
                        return parsed
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            logger.debug(f"Pattern matching failed: {e}")
        
        # Try to extract queries from natural language response
        try:
            extracted_queries = self._extract_queries_from_text(content, script)
            if extracted_queries:
                logger.info(f"Successfully extracted queries from text: {len(extracted_queries)} queries")
                return extracted_queries
        except Exception as e:
            logger.debug(f"Text extraction failed: {e}")
        
        # If all parsing fails, log the full response for debugging
        logger.error(f"Failed to parse AI response. Full response: '{content[:500]}{'...' if len(content) > 500 else ''}'")
        logger.error(f"Response type: {type(content)}, Length: {len(content) if content else 0}")
        logger.warning("Will use intelligent fallback queries based on script content")
        return []
    
    def _extract_queries_from_text(self, content: str, script: str) -> List[Dict]:
        """Extract search queries from natural language AI response."""
        queries = []
        
        # Look for time-based segments and queries
        time_patterns = [
            r'(\d+(?:\.\d+)?)\s*-?\s*(\d+(?:\.\d+)?)\s*(?:seconds?|s)\s*:?\s*["\']?([^"\n\r]+)["\']?',
            r'(?:start|from)\s*(\d+(?:\.\d+)?)\s*(?:to|end|-)\s*(\d+(?:\.\d+)?)\s*:?\s*["\']?([^"\n\r]+)["\']?',
            r'query\s*:?\s*["\']([^"\n\r]+)["\']',
            r'search\s*:?\s*["\']([^"\n\r]+)["\']',
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if len(match) == 3:  # Has time range
                    start_time, end_time, query = match
                    try:
                        queries.append({
                            "start_time": float(start_time),
                            "end_time": float(end_time),
                            "query": query.strip(),
                            "visual_concept": f"Visual: {query.strip()}"
                        })
                    except ValueError:
                        continue
                elif len(match) == 1:  # Just query
                    query = match[0] if isinstance(match, tuple) else match
                    queries.append({
                        "query": query.strip(),
                        "visual_concept": f"Visual: {query.strip()}"
                    })
        
        # If we found some queries without timing, add timing
        if queries and not all('start_time' in q for q in queries):
            estimated_duration = self._estimate_script_duration(script)
            segment_duration = estimated_duration / len(queries) if queries else 3.0
            
            for i, query in enumerate(queries):
                if 'start_time' not in query:
                    query['start_time'] = i * segment_duration
                    query['end_time'] = min((i + 1) * segment_duration, estimated_duration)
        
        return queries
    
    def _extract_phrases_and_contexts(self, text: str) -> List[str]:
        """Extract meaningful phrases and contexts from the script."""
        import re
        
        # Clean text but preserve sentence structure
        text = re.sub(r'[^\w\s\.]', ' ', text.lower())
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        
        phrases = []
        
        # Look for noun phrases with prepositions (common in visual descriptions)
        phrase_patterns = [
            r'\b(person|people|man|woman|team|group)\s+(working|using|holding|standing|sitting)\s+(\w+)',
            r'\b(in|at|on|near)\s+the\s+(\w+)',
            r'\b(\w+)\s+(room|office|building|space|area)',
            r'\b(close\s+up|wide\s+shot|view)\s+of\s+(\w+)',
            r'\b(\w+)\s+(screen|display|monitor|device)',
            r'\b(hands|fingers)\s+(typing|holding|touching|pointing)',
        ]
        
        for sentence in sentences[:3]:  # Focus on first 3 sentences
            for pattern in phrase_patterns:
                matches = re.findall(pattern, sentence)
                for match in matches:
                    if isinstance(match, tuple):
                        phrase = ' '.join(match).strip()
                    else:
                        phrase = match.strip()
                    if len(phrase) > 5:  # Meaningful phrases
                        phrases.append(phrase)
        
        return phrases[:10]  # Top 10 phrases
    
    def _extract_nouns_and_key_terms(self, text: str) -> List[str]:
        """Extract nouns and key visual terms from text using enhanced NLP."""
        import re
        
        # First, extract meaningful phrases
        phrases = self._extract_phrases_and_contexts(text)
        
        # Clean the text for word extraction
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # Enhanced visual nouns categorized by relevance for stock footage
        high_priority_terms = {
            # People (work very well)
            'person', 'people', 'man', 'woman', 'team', 'group', 'worker', 'professional',
            'student', 'teacher', 'doctor', 'scientist', 'engineer', 'chef', 'artist',
            'professionals', 'students', 'teachers', 'doctors', 'scientists', 'engineers',
            
            # Technology (high success rate)
            'computer', 'laptop', 'phone', 'smartphone', 'screen', 'keyboard', 'data',
            'technology', 'digital', 'software', 'app', 'website', 'internet',
            'computers', 'laptops', 'phones', 'smartphones', 'screens',
            
            # Workspaces (very common in stock footage)
            'office', 'workplace', 'desk', 'meeting', 'conference', 'presentation',
            'business', 'work', 'job', 'project', 'task', 'discussion',
            'offices', 'workplaces', 'meetings', 'presentations', 'projects',
        }
        
        medium_priority_terms = {
            # Places
            'building', 'room', 'space', 'city', 'street', 'home', 'kitchen',
            'school', 'university', 'hospital', 'laboratory', 'restaurant',
            'nature', 'forest', 'ocean', 'mountain', 'park', 'garden',
            'classroom', 'studio', 'lab', 'buildings', 'rooms', 'cities',
            'schools', 'universities', 'hospitals', 'laboratories', 'classrooms',
            
            # Objects
            'book', 'paper', 'document', 'chart', 'graph', 'equipment', 'tool',
            'camera', 'car', 'vehicle', 'food', 'meal', 'water', 'light',
            'books', 'papers', 'documents', 'charts', 'graphs', 'tools',
            
            # Activities & Concepts
            'research', 'study', 'analysis', 'experiment', 'training', 'exercise',
            'cooking', 'travel', 'music', 'art', 'design', 'creation', 'learning',
            'education', 'medical', 'healthcare', 'innovation', 'development',
            'collaboration', 'communication', 'creative', 'solution', 'solutions',
        }
        
        # Extract terms with priority scoring
        high_terms = [word for word in words if word in high_priority_terms]
        medium_terms = [word for word in words if word in medium_priority_terms]
        
        # Combine with phrases, prioritizing high-value terms
        all_terms = phrases + high_terms + medium_terms
        
        # Remove duplicates while preserving order
        seen = set()
        unique_terms = []
        for term in all_terms:
            if term not in seen and len(term) > 2:
                seen.add(term)
                unique_terms.append(term)
        
        return unique_terms[:25]  # Increased to 25 for better coverage
    
    def _create_script_based_queries(self, script: str, extracted_terms: List[str], segment_duration: float) -> List[str]:
        """Create search queries based on extracted script terms with smart combinations."""
        queries = []
        
        # If we have phrases (multi-word terms), use them directly as they're already contextual
        phrases = [term for term in extracted_terms if ' ' in term]
        single_terms = [term for term in extracted_terms if ' ' not in term]
        
        # Use phrases directly - they're already well-formed for search
        for phrase in phrases:
            # Clean up phrases to be more search-friendly
            clean_phrase = phrase.replace(' and ', ' ').replace(' the ', ' ')
            if len(clean_phrase.split()) <= 3:  # Keep phrases concise
                queries.append(clean_phrase)
        
        # Create contextual queries from single terms
        for i, term in enumerate(single_terms):
            # People-focused queries
            if term in ['person', 'people', 'man', 'woman', 'team', 'group', 'professional', 'worker']:
                context_terms = [t for t in single_terms[i+1:i+3] if t not in ['person', 'people', 'man', 'woman']]
                if context_terms:
                    if any(ct in ['office', 'work', 'business', 'meeting'] for ct in context_terms):
                        queries.append(f"{term} business office")
                    elif any(ct in ['computer', 'laptop', 'screen', 'keyboard'] for ct in context_terms):
                        queries.append(f"{term} working computer")
                    elif any(ct in ['research', 'study', 'data', 'analysis'] for ct in context_terms):
                        queries.append(f"{term} research work")
                    else:
                        queries.append(f"{term} {context_terms[0]}")
                else:
                    queries.append(f"{term} working")
            
            # Technology-focused queries
            elif term in ['computer', 'laptop', 'phone', 'smartphone', 'screen', 'technology', 'digital']:
                queries.append("hands typing keyboard")
                if 'data' in single_terms or 'analysis' in single_terms:
                    queries.append("computer screen data")
                else:
                    queries.append(f"{term} close up")
            
            # Workplace queries
            elif term in ['office', 'workplace', 'business', 'work', 'meeting', 'presentation']:
                if 'team' in single_terms or 'group' in single_terms:
                    queries.append("team meeting office")
                else:
                    queries.append("business office workspace")
            
            # Science/Research queries
            elif term in ['research', 'science', 'study', 'data', 'analysis', 'experiment']:
                if any(t in ['laboratory', 'lab', 'scientist'] for t in single_terms):
                    queries.append("scientist laboratory research")
                else:
                    queries.append("research data analysis")
            
            # Medical/Health queries
            elif term in ['health', 'medical', 'doctor', 'hospital', 'patient']:
                queries.append("doctor medical consultation")
            
            # Education queries
            elif term in ['education', 'school', 'student', 'teacher', 'learning']:
                queries.append("student studying library")
            
            # Food/Cooking queries
            elif term in ['food', 'cooking', 'kitchen', 'chef', 'restaurant']:
                queries.append("chef cooking kitchen")
            
            # Nature/Environment queries
            elif term in ['nature', 'environment', 'forest', 'tree', 'ocean', 'mountain']:
                queries.append(f"{term} landscape")
            
            # City/Urban queries
            elif term in ['city', 'urban', 'building', 'street']:
                queries.append("city skyline urban")
            
            # For other specific terms, create contextual searches
            elif len(term) > 4 and term.isalpha():
                # Try to find a related term to pair with
                related_terms = single_terms[max(0, i-2):i] + single_terms[i+1:i+3]
                if related_terms:
                    queries.append(f"{term} {related_terms[0]}")
                else:
                    queries.append(f"{term} professional")
        
        # If we still don't have enough queries, extract key words from script
        if len(queries) < 3:
            # Extract the most important nouns from the script directly
            script_words = script.lower().split()
            important_words = []
            
            for word in script_words:
                if (len(word) > 5 and word.isalpha() and 
                    word not in ['the', 'and', 'for', 'with', 'that', 'this', 'from', 'they', 'have', 'were', 'been']):
                    important_words.append(word)
                if len(important_words) >= 10:
                    break
            
            for word in important_words[:5]:
                queries.append(f"{word} concept")
        
        # Remove duplicates and limit results
        unique_queries = []
        seen = set()
        for query in queries:
            if query not in seen and len(query.split()) <= 4:  # Keep queries concise
                seen.add(query)
                unique_queries.append(query)
        
        return unique_queries[:20]  # Increased limit for better coverage
    
    def _create_fallback_queries(self, script: str, segment_duration: float) -> List[Dict]:
        """Create fallback queries based on actual script content analysis."""
        estimated_duration = self._estimate_script_duration(script)
        num_segments = max(1, int(estimated_duration / segment_duration))
        
        logger.info(f"Creating script-based fallback queries for {num_segments} segments")
        
        # Extract relevant terms from the script
        extracted_terms = self._extract_nouns_and_key_terms(script)
        logger.info(f"Extracted terms from script: {extracted_terms[:10]}")  # Log first 10 terms
        
        # Create search queries based on extracted terms
        script_queries = self._create_script_based_queries(script, extracted_terms, segment_duration)
        logger.info(f"Generated {len(script_queries)} script-based queries: {script_queries[:5]}")
        
        # If we don't have enough queries, add some generic but safe ones
        if len(script_queries) < num_segments:
            safe_fallbacks = [
                "person working computer",
                "hands typing keyboard", 
                "office workspace",
                "business meeting",
                "professional presentation",
                "data analysis screen",
                "close up hands",
                "modern workplace",
                "team collaboration",
                "creative workspace"
            ]
            script_queries.extend(safe_fallbacks)
        
        # Create the final query objects
        queries = []
        for i in range(num_segments):
            start_time = i * segment_duration
            end_time = min((i + 1) * segment_duration, estimated_duration)
            
            # Use script-based queries, cycling through them
            query_term = script_queries[i % len(script_queries)]
            
            queries.append({
                "start_time": start_time,
                "end_time": end_time,
                "query": query_term,
                "visual_concept": f"Script-based: {query_term}"
            })
        
        logger.info(f"Created {len(queries)} script-based fallback queries")
        return queries
    
    def _validate_and_fix_queries(self, queries: List[Dict], 
                                 script_duration: float, segment_duration: float) -> List[Dict]:
        """Validate and fix timing issues in generated queries."""
        if not queries:
            return []
        
        # Sort by start time
        queries.sort(key=lambda x: x.get('start_time', 0))
        
        # Fix timing gaps and overlaps
        fixed_queries = []
        current_time = 0
        
        for i, query in enumerate(queries):
            start_time = current_time
            
            # Determine end time
            if i < len(queries) - 1:
                end_time = min(start_time + segment_duration, queries[i + 1].get('start_time', script_duration))
            else:
                end_time = script_duration
            
            # Skip if segment is too short
            if end_time - start_time < 1.0:
                continue
            
            fixed_query = {
                "start_time": start_time,
                "end_time": end_time,
                "duration": end_time - start_time,
                "query": query.get('query', 'nature scene'),
                "visual_concept": query.get('visual_concept', 'Visual content')
            }
            
            fixed_queries.append(fixed_query)
            current_time = end_time
            
            # Stop if we've covered the script duration
            if current_time >= script_duration:
                break
        
        return fixed_queries
    
    async def generate_video_search_queries(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate video search queries from script content.
        
        Args:
            params: Dictionary containing:
                - script: The script text to analyze
                - segment_duration: Target duration for each video segment
                - provider: AI provider preference ('auto', 'openai', 'groq')
                - language: Language code (currently only 'en' supported)
        
        Returns:
            Dictionary containing generated queries and metadata
        """
        script = params.get('script')
        segment_duration = params.get('segment_duration', 3.0)
        provider = params.get('provider', 'auto')
        language = params.get('language', 'en')
        
        if not script:
            raise ValueError("Script is required for video search query generation")
        
        if language != 'en':
            logger.warning(f"Language '{language}' not fully supported, using English")
        
        # Get the appropriate client and model
        try:
            client, model, actual_provider = self._get_provider_and_model(provider)
            logger.info(f"Using AI provider: {actual_provider} with model: {model}")
        except ValueError as e:
            logger.error(f"No AI provider available: {e}")
            # Directly create fallback queries since no AI is available
            fallback_queries = self._create_fallback_queries(script, segment_duration)
            fixed_queries = self._validate_and_fix_queries(
                fallback_queries, self._estimate_script_duration(script), segment_duration
            )
            
            query_objects = []
            for query_data in fixed_queries:
                query_objects.append({
                    "query": query_data["query"],
                    "start_time": query_data["start_time"],
                    "end_time": query_data["end_time"],
                    "duration": query_data["duration"],
                    "visual_concept": query_data["visual_concept"]
                })
            
            return {
                "queries": query_objects,
                "total_duration": self._estimate_script_duration(script),
                "total_segments": len(query_objects),
                "provider_used": "fallback-no-ai"
            }
        
        # Estimate script duration
        script_duration = self._estimate_script_duration(script)
        
        # Generate the prompt
        prompt = self._get_video_search_prompt(segment_duration)
        
        # Create user content
        user_content = f"""Script: {script}

Estimated Duration: {script_duration:.1f} seconds
Target Segment Duration: {segment_duration} seconds

Please generate video search queries for this script."""
        
        try:
            # Retry logic with exponential backoff for rate limiting
            max_retries = 3
            base_delay = 1.0
            response = None
            
            for attempt in range(max_retries):
                try:
                    logger.info(f"Making AI API call (attempt {attempt + 1}/{max_retries}) with model {model}")
                    
                    # Make the API call with specific parameters for better JSON output
                    response = client.chat.completions.create(
                        model=model,
                        temperature=0.5,  # Increased temperature for better response generation
                        max_tokens=2000,  # Increased token limit for complex responses
                        messages=[
                            {"role": "system", "content": prompt},
                            {"role": "user", "content": user_content}
                        ]
                    )
                    break  # Success, exit retry loop
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    logger.warning(f"API call failed on attempt {attempt + 1}: {e}")
                    if "rate limit" in error_msg or "too many requests" in error_msg:
                        if attempt < max_retries - 1:  # Don't sleep on last attempt
                            delay = base_delay * (2 ** attempt)  # Exponential backoff
                            logger.warning(f"Rate limit hit, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                            time.sleep(delay)
                            continue
                    raise  # Re-raise if not rate limit or max retries reached
            
            if not response:
                raise Exception("Failed to get response after all retries")
                
            # Parse the response
            content = response.choices[0].message.content
            if content is None:
                content = ""
            logger.info(f"Received AI response length: {len(content)} characters")
            
            # Check for empty or very short responses
            if not content or len(content.strip()) < 10:
                logger.warning(f"AI returned empty or very short response: '{content}' - using intelligent fallback queries")
                parsed_queries = self._create_fallback_queries(script, segment_duration)
            else:
                # Try multiple parsing attempts with different strategies
                parsed_queries = None
                parsing_attempts = [
                    lambda: self._parse_ai_response(content, script),
                    lambda: self._parse_ai_response(content.strip(), script),
                    lambda: self._parse_ai_response(content.replace('\n', ' '), script)
                ]
                
                for i, parse_func in enumerate(parsing_attempts):
                    try:
                        parsed_queries = parse_func()
                        if parsed_queries:
                            logger.info(f"Successfully parsed on attempt {i + 1}")
                            break
                    except Exception as e:
                        logger.debug(f"Parse attempt {i + 1} failed: {e}")
                        continue
                
                # If parsing failed, create intelligent fallback queries based on script content
                if not parsed_queries:
                    logger.warning(f"All parsing attempts failed for AI response. Creating intelligent fallback queries.")
                    logger.debug(f"Failed AI response was: {content[:500]}...")
                    parsed_queries = self._create_fallback_queries(script, segment_duration)
                    logger.info(f"Created {len(parsed_queries)} intelligent fallback queries based on script content")
            
            # Validate and fix timing
            final_queries = self._validate_and_fix_queries(
                parsed_queries, script_duration, segment_duration
            )
            
            # Convert to response format
            query_objects = []
            for query_data in final_queries:
                query_objects.append({
                    "query": query_data["query"],
                    "start_time": query_data["start_time"],
                    "end_time": query_data["end_time"],
                    "duration": query_data["duration"],
                    "visual_concept": query_data["visual_concept"]
                })
            
            return {
                "queries": query_objects,
                "total_duration": script_duration,
                "total_segments": len(query_objects),
                "provider_used": actual_provider
            }
            
        except Exception as e:
            logger.error(f"Failed to generate video search queries: {e}")
            # Return fallback result
            fallback_queries = self._create_fallback_queries(script, segment_duration)
            fixed_queries = self._validate_and_fix_queries(
                fallback_queries, script_duration, segment_duration
            )
            
            query_objects = []
            for query_data in fixed_queries:
                query_objects.append({
                    "query": query_data["query"],
                    "start_time": query_data["start_time"],
                    "end_time": query_data["end_time"],
                    "duration": query_data["duration"],
                    "visual_concept": query_data["visual_concept"]
                })
            
            return {
                "queries": query_objects,
                "total_duration": script_duration,
                "total_segments": len(query_objects),
                "provider_used": "fallback"
            }


# Create a singleton instance
video_search_query_generator = VideoSearchQueryGenerator()