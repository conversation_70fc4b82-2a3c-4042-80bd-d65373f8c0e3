import os
import logging
from typing import Dict, Any, List
from openai import OpenAI
from app.utils.ai_context import get_current_context

logger = logging.getLogger(__name__)


class ImagePromptGenerator:
    """Service for generating image prompts using AI models."""
    
    def __init__(self):
        self.openai_client = None
        self._setup_client()
    
    def _setup_client(self):
        """Initialize OpenAI client."""
        openai_key = os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_KEY')
        if openai_key:
            openai_base_url = os.getenv('OPENAI_BASE_URL')
            if openai_base_url:
                self.openai_client = OpenAI(api_key=openai_key, base_url=openai_base_url)
            else:
                self.openai_client = OpenAI(api_key=openai_key)
        else:
            logger.warning("OpenAI API key not found. Image prompt generation will not be available.")
    
    def is_available(self) -> bool:
        """Check if the service is available."""
        return self.openai_client is not None
    
    async def generate_image_prompt_for_segment(
        self,
        script_text: str,
        full_script: str,
        segment_position: int,
        total_segments: int,
        model: str = "gpt-4o-mini"
    ) -> str:
        """
        Generate an image prompt for a specific script segment.
        
        Args:
            script_text: The text content of this specific segment
            full_script: The complete script for context
            segment_position: Position of this segment (1-based)
            total_segments: Total number of segments
            model: OpenAI model to use
        
        Returns:
            Generated image prompt suitable for FLUX model
        """
        if not self.openai_client:
            raise ValueError("OpenAI client not available")
        
        system_prompt = f"""You are an expert at creating image prompts for AI image generation models like FLUX.

Your task is to generate compelling, visually striking image prompts that will create engaging visuals for video content.

{get_current_context()}

Guidelines:
1. Create vivid, descriptive prompts that capture the essence of the script segment
2. Include relevant style keywords (e.g., "cinematic", "photorealistic", "dramatic lighting")
3. Be specific about composition, colors, and visual elements
4. Keep prompts under 200 characters for optimal generation
5. Avoid text, words, or letters in the image
6. Focus on visual storytelling that complements the narration
7. Consider the segment's position in the overall story arc

Examples of good prompts:
- "Majestic underwater coral reef teeming with colorful fish, cinematic lighting, crystal clear water, National Geographic style"
- "Ancient stone temple in misty jungle, dramatic golden hour lighting, mysterious atmosphere, photorealistic"
- "Vast galaxy with swirling nebulae and bright stars, deep space photography, cosmic wonder, ultra detailed"

Generate a single image prompt that visually represents the content of this script segment."""

        user_prompt = f"""Script segment to visualize:
"{script_text}"

Context - Full script:
"{full_script}"

Segment position: {segment_position} of {total_segments}

Generate a compelling image prompt for this segment that will create a visually engaging image to accompany the narration."""

        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=150,
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            if not content:
                raise Exception("Empty response from OpenAI")
            prompt = content.strip()
            
            # Clean up the prompt (remove quotes, etc.)
            prompt = prompt.strip('"\'')
            
            logger.info(f"Generated image prompt for segment {segment_position}: {prompt[:50]}...")
            
            return prompt
            
        except Exception as e:
            logger.error(f"Error generating image prompt: {str(e)}")
            # Fallback to a simple prompt based on the text
            fallback_prompt = f"Visual representation of: {script_text[:100]}, cinematic style"
            logger.info(f"Using fallback prompt: {fallback_prompt}")
            return fallback_prompt
    
    async def generate_music_prompt(
        self,
        script_text: str,
        script_type: str = "facts",
        video_duration: float = 60.0,
        model: str = "gpt-4o-mini"
    ) -> str:
        """
        Generate a music prompt based on the script content and type.
        
        Args:
            script_text: The complete script text
            script_type: Type of script (facts, story, educational, etc.)
            video_duration: Duration of the video in seconds
            model: OpenAI model to use
        
        Returns:
            Generated music prompt for background music generation
        """
        if not self.openai_client:
            raise ValueError("OpenAI client not available")
        
        system_prompt = f"""You are an expert at creating music prompts for AI music generation.

Your task is to generate compelling prompts that will create appropriate background music for video content.

{get_current_context()}

Guidelines:
1. Consider the mood, tone, and energy of the script
2. Match the music style to the content type (educational, dramatic, uplifting, etc.)
3. Specify tempo, instruments, and musical style
4. Keep prompts concise but descriptive (under 100 words)
5. Avoid lyrics or vocals unless specifically needed
6. Consider the video duration for pacing

Examples of good music prompts:
- "Uplifting ambient electronic music with soft synth pads, gentle percussion, inspiring and educational tone"
- "Mysterious cinematic orchestral music with strings and subtle percussion, building tension, documentary style"
- "Calming nature-inspired ambient music with piano and soft strings, peaceful and meditative"

Generate a music prompt that complements the script content and type."""

        user_prompt = f"""Script content:
"{script_text[:500]}..."

Script type: {script_type}
Video duration: {video_duration} seconds

Generate an appropriate background music prompt for this content."""

        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=100,
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            if not content:
                raise Exception("Empty response from OpenAI")
            prompt = content.strip()
            
            logger.info(f"Generated music prompt: {prompt[:50]}...")
            
            return prompt
            
        except Exception as e:
            logger.error(f"Error generating music prompt: {str(e)}")
            # Fallback to a simple prompt based on script type
            fallback_prompts = {
                "facts": "Uplifting ambient music with soft synths, educational and inspiring tone",
                "story": "Cinematic orchestral music with emotional depth, storytelling atmosphere",
                "educational": "Calm ambient electronic music, focus-enhancing, learning-friendly",
                "motivation": "Energetic uplifting music with driving beat, inspirational and powerful",
                "conspiracy": "Dark mysterious ambient music with tension, investigative atmosphere"
            }
            fallback_prompt = fallback_prompts.get(script_type, "Calm ambient music with gentle melody")
            logger.info(f"Using fallback music prompt: {fallback_prompt}")
            return fallback_prompt
    
    async def generate_multiple_image_prompts(
        self,
        segments: List[Dict[str, Any]],
        full_script: str,
        model: str = "gpt-4o-mini"
    ) -> List[str]:
        """
        Generate image prompts for multiple script segments.
        
        Args:
            segments: List of segment dictionaries with 'text' key
            full_script: The complete script for context
            model: OpenAI model to use
        
        Returns:
            List of generated image prompts
        """
        prompts = []
        total_segments = len(segments)
        
        for i, segment in enumerate(segments, 1):
            try:
                prompt = await self.generate_image_prompt_for_segment(
                    script_text=segment.get('text', ''),
                    full_script=full_script,
                    segment_position=i,
                    total_segments=total_segments,
                    model=model
                )
                prompts.append(prompt)
            except Exception as e:
                logger.error(f"Failed to generate prompt for segment {i}: {str(e)}")
                # Add a fallback prompt
                fallback = f"Visual representation of script segment {i}, cinematic style"
                prompts.append(fallback)
        
        logger.info(f"Generated {len(prompts)} image prompts for {total_segments} segments")
        
        return prompts


# Global instance
image_prompt_generator = ImagePromptGenerator()