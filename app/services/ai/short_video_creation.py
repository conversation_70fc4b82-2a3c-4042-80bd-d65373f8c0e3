"""
Short video creation service that adapts footage-to-video functionality for MCP and frontend use.
"""
import uuid
from typing import Dict, List, Any, Optional
from pydantic import BaseModel

from app.services.ai.footage_to_video_pipeline import FootageToVideoPipeline
from app.models import FootageToVideoRequest, VideoCaptionProperties
from app.utils.logging import get_logger

logger = get_logger()


class ShortVideoScene(BaseModel):
    """Individual scene for short video creation."""
    text: str
    duration: float
    searchTerms: List[str]


class ShortVideoRequest(BaseModel):
    """Request model for short video creation compatible with MCP."""
    scenes: List[ShortVideoScene]
    voice_provider: str = "kokoro"
    voice_name: str = "af_bella"
    language: str = "en"
    background_music: Optional[str] = None
    resolution: str = "1080x1920"
    fps: int = 30
    caption_style: Optional[str] = "viral_bounce"
    caption_color: Optional[str] = None
    caption_position: Optional[str] = "center"


class ShortVideoCreationService:
    """Service for creating short videos compatible with MCP and frontend."""
    
    def __init__(self):
        self.topic_pipeline = FootageToVideoPipeline()
    
    async def create_short_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a short video from scenes using the existing footage-to-video pipeline.
        
        Args:
            params: Dictionary containing short video creation parameters
            
        Returns:
            Dictionary containing the result with video URLs and metadata
        """
        try:
            # Extract parameters
            scenes = params.get("scenes", [])
            voice_provider = params.get("voice_provider", "kokoro")
            voice_name = params.get("voice_name", "af_bella")
            language = params.get("language", "en")
            background_music = params.get("background_music")
            resolution = params.get("resolution", "1080x1920")
            fps = params.get("fps", 30)
            max_duration_param = params.get("max_duration", 60)
            caption_style = params.get("caption_style", "viral_bounce")
            caption_color = params.get("caption_color")
            caption_position = params.get("caption_position", "center")
            
            logger.info(f"Creating short video with {len(scenes)} scenes")
            
            # Convert scenes to script format
            script_parts = []
            total_duration = 0.0
            
            for i, scene in enumerate(scenes):
                text = scene.get("text", "")
                duration = scene.get("duration", 3.0)
                search_terms = scene.get("searchTerms", [])
                
                script_parts.append(text)
                total_duration += duration
                
                logger.debug(f"Scene {i+1}: {duration}s - {text[:50]}...")
            
            # Combine script parts
            full_script = " ".join(script_parts)
            
            # Determine video orientation and dimensions from resolution
            if "x" in resolution:
                width_str, height_str = resolution.split("x")
                output_width = int(width_str)
                output_height = int(height_str)
                
                if output_height > output_width:
                    video_orientation = "portrait"
                elif output_width > output_height:
                    video_orientation = "landscape"
                else:
                    video_orientation = "square"
            else:
                # Default to portrait for short videos
                video_orientation = "portrait"
                output_width = 1080
                output_height = 1920
            
            # Set up caption properties for short videos (TikTok style)
            # Use custom color for viral styles if provided, otherwise use defaults based on style
            if caption_color:
                # User has explicitly selected a color - use it regardless of style
                glow_color = caption_color
                highlight_color = caption_color
            else:
                # No color specified - use style-based defaults
                if caption_style == "viral_cyan" or caption_style == "viral_bounce":
                    glow_color = "#00FFFF"  # Cyan
                    highlight_color = "#00FFFF"
                elif caption_style == "viral_yellow":
                    glow_color = "#FFFF00"  # Yellow  
                    highlight_color = "#FFFF00"
                elif caption_style == "viral_green":
                    glow_color = "#00FF00"  # Green
                    highlight_color = "#00FF00"
                else:
                    # Default fallback color
                    glow_color = "#00FFFF"  # Cyan
                    highlight_color = "#00FFFF"
            
            caption_properties = VideoCaptionProperties(
                style=caption_style,
                font_size=48,
                line_color=glow_color if glow_color else "white",  # Use user's color for line color
                outline_color="black",
                outline_width=3,
                position=caption_position,
                all_caps=True,
                glow_effect=True,
                glow_color=glow_color,
                glow_intensity=0.8,
                bounce_intensity=1.2,
                animation_speed=1.0,
                auto_emoji=True,
                confidence_styling=True,
                highlight_color=highlight_color,
                word_color=highlight_color,  # Also set word_color for highlighting
                caption_position=caption_position
            )
            
            # Create FootageToVideoRequest
            topic_request = FootageToVideoRequest(
                topic=full_script[:100] + "..." if len(full_script) > 100 else full_script,
                language=language,
                script_provider="auto",
                script_type="facts",  # Default type for short videos
                max_duration=max(20, max_duration_param, int(total_duration) + 10),  # Use provided max_duration, with 20s minimum
                voice=voice_name,
                tts_provider=voice_provider,
                tts_speed=1.0,
                video_orientation=video_orientation,
                segment_duration=3.0,  # Standard segment duration
                add_captions=True,
                caption_style=caption_style,
                caption_color=caption_color,
                caption_position=caption_position,
                caption_properties=caption_properties,
                output_width=output_width,
                output_height=output_height,
                frame_rate=fps,
                # Add background music parameters
                background_music=background_music,
                background_music_volume=0.3
            )
            
            # Override the script generation step by providing the script directly
            # This is a bit of a hack, but allows us to use existing infrastructure
            logger.info("Processing short video through footage-to-video pipeline")
            
            # Use the existing pipeline but with our custom script
            # The pipeline expects a single params dict, so we'll merge all parameters
            pipeline_params = topic_request.model_dump().copy()
            pipeline_params['custom_script'] = full_script
            pipeline_params['scenes_data'] = scenes
            
            result = await self.topic_pipeline.process_footage_to_video(pipeline_params)
            
            # Format result for MCP/frontend compatibility
            formatted_result = {
                "video_url": result.get("final_video_url"),
                "video_with_audio_url": result.get("video_with_audio_url"),
                "audio_url": result.get("audio_url"),
                "srt_url": result.get("srt_url"),
                "duration": result.get("video_duration", total_duration),
                "script_used": full_script,
                "scenes_processed": len(scenes),
                "resolution": f"{output_width}x{output_height}",
                "voice_used": f"{voice_provider}:{voice_name}",
                "language": language,
                "processing_time": result.get("processing_time", 0.0),
                "background_videos_used": result.get("background_videos_used", [])
            }
            
            # Add background music info if specified
            if background_music:
                formatted_result["background_music"] = background_music
                
            # Add background music URL if available
            if result.get("background_music_url"):
                formatted_result["background_music_url"] = result.get("background_music_url")
            
            logger.info(f"Short video created successfully: {formatted_result['duration']:.2f}s")
            
            return formatted_result
            
        except Exception as e:
            logger.error(f"Error creating short video: {e}")
            raise Exception(f"Failed to create short video: {str(e)}")
    
    async def process_topic_to_short_video(self, topic: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a short video from a topic using AI script generation.
        
        Args:
            topic: Topic to generate script from
            params: Additional parameters for video creation
            
        Returns:
            Dictionary containing the result with video URLs and metadata
        """
        try:
            # Generate script from topic first
            from app.services.ai.script_generator import AIScriptGenerator
            
            script_service = AIScriptGenerator()
            
            # Generate script
            script_params = {
                "topic": topic,
                "script_type": params.get("script_type", "facts"),
                "max_duration": params.get("max_duration", 50),
                "language": params.get("language", "english"),
                "provider": params.get("script_provider", "auto")
            }
            
            script_result = await script_service.generate_script(script_params)
            generated_script = script_result["script"]
            
            logger.info(f"Generated script from topic '{topic}': {len(generated_script)} chars")
            
            # Generate video search queries for the script
            from app.services.ai.video_search_query_generator import VideoSearchQueryGenerator
            
            video_search_service = VideoSearchQueryGenerator()
            
            search_params = {
                "script": generated_script,
                "segment_duration": params.get("segment_duration", 3.0),
                "provider": "auto",
                "language": params.get("language", "en")
            }
            
            search_result = await video_search_service.generate_video_search_queries(search_params)
            queries = search_result["queries"]
            
            # Convert queries to scenes format
            scenes = []
            current_text = ""
            words = generated_script.split()
            words_per_query = len(words) // len(queries) if queries else len(words)
            
            for i, query in enumerate(queries):
                # Estimate text for this segment
                start_word = i * words_per_query
                end_word = min((i + 1) * words_per_query, len(words))
                segment_text = " ".join(words[start_word:end_word])
                
                scenes.append({
                    "text": segment_text,
                    "duration": query.get("duration", 3.0),
                    "searchTerms": [query.get("query", topic)]
                })
            
            # If no queries generated, create a single scene
            if not scenes:
                scenes = [{
                    "text": generated_script,
                    "duration": params.get("max_duration", 50),
                    "searchTerms": [topic]
                }]
            
            # Use the scenes to create the video
            video_params = {
                "scenes": scenes,
                "voice_provider": params.get("voice_provider", "kokoro"),
                "voice_name": params.get("voice_name", "af_bella"),
                "language": params.get("language", "en"),
                "background_music": params.get("background_music"),
                "resolution": params.get("resolution", "1080x1920"),
                "fps": params.get("fps", 30)
            }
            
            result = await self.create_short_video(video_params)
            
            # Add topic and script generation info
            result.update({
                "topic_used": topic,
                "script_generated": generated_script,
                "word_count": len(generated_script.split()),
                "auto_generated": True
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Error creating short video from topic: {e}")
            raise Exception(f"Failed to create short video from topic: {str(e)}")


# Create singleton instance
short_video_service = ShortVideoCreationService()