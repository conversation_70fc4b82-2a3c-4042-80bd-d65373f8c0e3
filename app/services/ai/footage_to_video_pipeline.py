import os
import time
import logging
import uuid
from typing import Dict, Any, List, Optional, Union
from app.services.ai.script_generator import script_generator
from app.services.ai.video_search_query_generator import video_search_query_generator
from app.services.ai.pexels_service import pexels_service
from app.services.ai.pixabay_service import pixabay_service
from app.services.ai.pexels_image_service import pexels_image_service
from app.services.ai.pixabay_image_service import pixabay_image_service
from app.services.video.ltx_video_service import ltx_video_service
from app.services.video.wavespeed_service import wavespeed_service
from app.services.ai.news_research_service import news_research_service
from app.services.audio.tts_service import tts_service as text_to_speech_service
from app.services.video.add_captions import add_captions_service
from app.services.video.concatenate import concatenation_service
from app.services.video.add_audio import add_audio_service
from app.services.image.image_to_video import image_to_video_service
from app.services.s3 import s3_service
from app.config import get_caption_style, apply_caption_style_preset, get_available_caption_styles
from app.services.media.metadata import metadata_service
from app.utils.video.background_video_composer import BackgroundVideoComposer
from app.services.music_service import music_service
from app.utils.video.moviepy_video_composer import MoviePyVideoComposer
from app.services.ai.topic_discovery_service import topic_discovery_service

logger = logging.getLogger(__name__)


class FootageToVideoPipeline:
    """
    Complete end-to-end pipeline for generating videos from topics.
    
    This service orchestrates the entire process:
    1. Generate script from topic using AI
    2. Create TTS audio from script
    3. Generate video search queries from script
    4. Find and download background videos from Pexels
    5. Compose videos with timing
    6. Add captions if requested
    7. Final rendering and upload to S3
    """
    
    def __init__(self):
        self.video_composer = BackgroundVideoComposer()  # FFmpeg fallback composer
        self.moviepy_composer = MoviePyVideoComposer()   # Primary MoviePy composer
    
    def _map_language_code_to_name(self, language_code: str) -> str:
        """Map language codes to full language names for script generation."""
        language_mapping = {
            'en': 'english',
            'fr': 'french', 
            'es': 'spanish',
            'de': 'german',
            'it': 'italian',
            'pt': 'portuguese',
            'ru': 'russian',
            'zh': 'chinese',
            'ja': 'japanese',
            'ko': 'korean',
            'ar': 'arabic',
            'hi': 'hindi',
            'th': 'thai',
            'vi': 'vietnamese',
            'pl': 'polish',
            'nl': 'dutch',
            'sv': 'swedish',
            'no': 'norwegian',
            'da': 'danish',
            'fi': 'finnish',
            'tr': 'turkish',
            'he': 'hebrew',
            'cs': 'czech',
            'hu': 'hungarian',
            'ro': 'romanian',
            'sk': 'slovak',
            'sl': 'slovenian',
            'hr': 'croatian',
            'bg': 'bulgarian',
            'et': 'estonian',
            'lv': 'latvian',
            'lt': 'lithuanian',
            'mt': 'maltese',
            'ga': 'irish',
            'cy': 'welsh',
        }
        return language_mapping.get(language_code.lower(), language_code)
    
    async def process_footage_to_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process complete footage-to-video pipeline.
        
        Args:
            params: Pipeline parameters including topic, voice settings, caption options, etc.
        
        Returns:
            Dictionary containing final video URL and processing metadata
        """
        start_time = time.time()
        
        # Log the media type and provider being used
        media_type = params.get('media_type', 'video')
        footage_provider = params.get('footage_provider', 'pexels')
        logger.info(f"Processing footage-to-video with media_type: '{media_type}', footage_provider: '{footage_provider}'")
        
        try:
            # Handle video orientation first to ensure dimensions are set
            orientation = params.get('video_orientation', 'landscape')
            
            if orientation == 'portrait':
                output_width = params.get('output_width') or 1080
                output_height = params.get('output_height') or 1920
            elif orientation == 'square':
                output_width = params.get('output_width') or 1080
                output_height = params.get('output_height') or 1080
            else:  # landscape
                output_width = params.get('output_width') or 1920
                output_height = params.get('output_height') or 1080
            
            
            # Step 1: Generate script from topic or use provided custom script
            if params.get('custom_script'):
                logger.info("Step 1: Using provided custom script")
                script_text = params['custom_script']
                # Estimate duration from script length (assuming ~2.5 words per second)
                word_count = len(script_text.split())
                estimated_duration = word_count / 2.5
                script_result = {
                    'script': script_text,
                    'estimated_duration': estimated_duration,
                    'word_count': word_count,
                    'title': 'Custom Video',
                    'description': 'Video from custom script'
                }
            else:
                # Handle auto topic discovery
                topic = params.get('topic')
                if params.get('auto_topic', False) and not topic:
                    logger.info("Step 1: Auto topic discovery enabled, discovering topic")
                    language_code = params.get('language', 'en')
                    logger.info(f"Using language code '{language_code}' for topic discovery")
                    discovered_topic = await topic_discovery_service.discover_topic(
                        script_type=params.get('script_type', 'facts'),
                        use_trending=True,
                        language=language_code
                    )
                    topic = discovered_topic['topic']
                    logger.info(f"Discovered topic: {topic}")
                
                if not topic:
                    raise ValueError("Topic is required for script generation")
                
                logger.info("Step 1: Generating script from topic")
                
                # Get language code and map it to language name for script generation
                language_code = params.get('language', 'en')
                script_language = self._map_language_code_to_name(language_code)
                logger.info(f"Using language code '{language_code}' -> mapped to '{script_language}' for script generation")
                
                script_params = {
                    'topic': topic,
                    'provider': params.get('script_provider', 'auto'),
                    'script_type': params.get('script_type', 'facts'),
                    'max_duration': params.get('max_duration', 120),
                    'target_words': self._calculate_target_words(params.get('max_duration', 120)),
                    'language': script_language  # Use mapped language name
                }
                
                script_result = await script_generator.generate_script(script_params)
                script_text = script_result['script']
                estimated_duration = script_result['estimated_duration']
            
            
            # Step 2: Generate TTS audio from script
            logger.info("Step 2: Generating TTS audio")
            tts_params = {
                'text': script_text,
                'voice': params.get('voice', 'af_alloy'),
                'provider': params.get('tts_provider'),
                'speed': params.get('tts_speed', 1.0),
                'response_format': 'wav',  # Use WAV for better quality
                'language': params.get('language', 'en')  # Match script language
            }
            
            tts_result = await text_to_speech_service.process_text_to_speech(f"pipeline_tts_{uuid.uuid4().hex[:8]}", tts_params)
            audio_url = tts_result['audio_url']
            
            logger.info(f"Generated TTS audio: {audio_url}")
            
            # Step 2.5: Get actual audio duration instead of estimation
            logger.info("Getting actual audio duration from metadata")
            try:
                audio_metadata = await metadata_service.get_metadata(audio_url, f"pipeline_duration_{uuid.uuid4().hex[:8]}")
                actual_duration = audio_metadata.get('duration', estimated_duration)
                
                # Validate the duration makes sense
                if actual_duration <= 0.001:
                    logger.error(f"Invalid audio duration: {actual_duration:.6f}s - falling back to estimated: {estimated_duration:.1f}s")
                    actual_duration = estimated_duration
                
                # Log significant differences for monitoring
                duration_diff = abs(actual_duration - estimated_duration)
                if duration_diff > 2.0:
                    logger.warning(f"Duration difference: actual={actual_duration:.1f}s vs estimated={estimated_duration:.1f}s")
                    
            except Exception as e:
                logger.warning(f"Failed to get audio duration, using estimated: {e}")
                actual_duration = estimated_duration
            
            # Step 3: Generate video search queries
            query_params = {
                'script': script_text,
                'segment_duration': params.get('segment_duration', 3.0),
                'provider': params.get('script_provider', 'auto'),
                'language': 'en'
            }
            
            query_result = await video_search_query_generator.generate_video_search_queries(query_params)
            video_queries = query_result['queries']
            
            
            # Adjust video queries timing based on actual audio duration
            if abs(actual_duration - estimated_duration) > 0.5:  # Only adjust if difference > 0.5s
                video_queries = self._adjust_video_queries_timing(video_queries, actual_duration, estimated_duration)
            
            # Step 4: Find background videos for each query
            background_videos = await self._find_background_videos(
                video_queries,
                params.get('video_orientation', 'landscape'),
                params.get('script_type', 'facts'),
                params.get('segment_duration', 3.0),
                params.get('footage_provider', 'pexels'),  # Support multiple footage providers
                params.get('footage_quality', 'high'),     # Quality setting for AI generation
                params.get('search_safety', 'moderate'),    # Safety setting
                params.get('ai_video_provider', 'ltx_video'),  # AI video provider (ltx_video or wavespeed)
                params.get('media_type', 'video'),  # Media type: 'video' or 'image'
                params.get('image_provider', 'together'),  # Image provider for AI-generated images
                # Pass motion parameters for image-to-video conversion
                params
            )
            
            
            # Step 5: Compose background video with timing
            
            # Use MoviePy composer by default for better timing precision
            use_moviepy = params.get('use_moviepy', True)  # Default to MoviePy
            if use_moviepy:
                composed_video_url = await self._compose_background_video_moviepy(
                    background_videos,
                    actual_duration,  # Use actual duration instead of estimated
                    output_width,
                    output_height,
                    params.get('frame_rate', 30)
                )
            else:
                composed_video_url = await self._compose_background_video(
                    background_videos,
                    actual_duration,  # Use actual duration instead of estimated
                    output_width,
                    output_height,
                    params.get('frame_rate', 30)
                )
            
            
            # Step 6: Process background music (if requested)
            final_audio_url = audio_url
            background_music_url = None
            background_music = params.get('background_music')
            
            # Check if we should add background music
            should_add_music = (
                (params.get('background_music') and params.get('background_music') != 'none') or
                (background_music and background_music != '' and background_music != 'none')
            )
            
            if should_add_music:
                logger.info("Step 6: Processing background music")
                
                # Determine background music type
                if background_music == 'ai_generate':
                    background_music_type = 'generate'
                elif background_music and background_music != 'ai_generate':
                    background_music_type = background_music  # Use mood directly
                else:
                    background_music_type = params.get('background_music', 'generate')
                
                logger.info(f"Calling _add_background_music with: type='{background_music_type}', mood='{background_music if background_music != 'ai_generate' else 'chill'}', volume={params.get('background_music_volume', 0.3)}")
                try:
                    music_result = await self._add_background_music(
                        audio_url,
                        background_music_type,
                        background_music if background_music and background_music != 'ai_generate' else 'chill',
                        params.get('background_music_volume', 0.3),
                        actual_duration
                    )
                    final_audio_url = music_result.get("mixed_audio_url", audio_url) if music_result else audio_url
                    background_music_url = music_result.get("background_music_url") if music_result else None
                except Exception as e:
                    logger.error(f"Failed to add background music, using original audio: {e}")
                    # Continue with original audio if background music fails
                    final_audio_url = audio_url
                    background_music_url = None
            
            # Step 7: Add audio to video
            if final_audio_url is None:
                raise ValueError("Final audio URL is None, cannot add audio to video")
            video_with_audio_url = await self._add_audio_to_video(
                composed_video_url,
                final_audio_url
            )
            
            
            # Step 8: Add captions if requested
            final_video_url = video_with_audio_url
            srt_url = None
            
            if params.get('add_captions', True):
                logger.info("Step 8: Adding captions")
                caption_result = await self._add_captions_to_video(
                    video_with_audio_url,
                    script_text,
                    params.get('caption_style', 'viral_bounce'),
                    output_width,
                    output_height,
                    actual_duration,  # Use actual duration instead of estimated
                    params.get('caption_properties')
                )
                final_video_url = caption_result['video_url']
                srt_url = caption_result.get('srt_url')
                
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Prepare result
            result = {
                "final_video_url": final_video_url,
                "video_with_audio_url": video_with_audio_url,  # Non-captioned version
                "title": script_result.get('title', 'Generated Video'),
                "description": script_result.get('description', 'AI-generated content'),
                "script_generated": script_text,
                "audio_url": audio_url,
                "background_videos_used": [video['download_url'] for video in background_videos if video],
                "srt_url": srt_url,
                "video_duration": actual_duration,  # Use actual duration instead of estimated
                "processing_time": processing_time,
                "word_count": script_result['word_count'],
                "segments_count": len(video_queries),
                "background_music_url": background_music_url  # Add background music URL
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Topic-to-video pipeline failed: {e}")
            raise ValueError(f"Pipeline failed: {str(e)}")
    
    def _calculate_target_words(self, max_duration: int) -> int:
        """Calculate target word count based on max duration."""
        # Average speaking rate: ~2.8 words per second
        return int(max_duration * 2.8)
    
    def _adjust_video_queries_timing(self, video_queries: List[Dict], actual_duration: float, estimated_duration: float) -> List[Dict]:
        """Adjust video query timing based on actual audio duration."""
        if estimated_duration <= 0:
            return video_queries
            
        # Calculate the scaling factor
        scale_factor = actual_duration / estimated_duration
        
        # Validate scale factor to prevent extreme values
        if scale_factor <= 0.001:
            logger.warning(f"Scale factor too small ({scale_factor}), using minimum 0.001")
            scale_factor = 0.001
        elif scale_factor > 100:
            logger.warning(f"Scale factor too large ({scale_factor}), capping to 100")
            scale_factor = 100
        
        adjusted_queries = []
        for query in video_queries:
            adjusted_query = query.copy()
            
            # Scale the timing information
            adjusted_query['start_time'] = query['start_time'] * scale_factor
            adjusted_query['end_time'] = query['end_time'] * scale_factor
            adjusted_query['duration'] = query['duration'] * scale_factor
            
            # Ensure minimum duration for each segment
            if adjusted_query['duration'] < 0.1:
                logger.warning(f"Adjusted duration too small ({adjusted_query['duration']:.6f}), setting to 0.1s")
                adjusted_query['duration'] = 0.1
                # Adjust end_time accordingly
                adjusted_query['end_time'] = adjusted_query['start_time'] + 0.1
            
            adjusted_queries.append(adjusted_query)
            
        return adjusted_queries
    
    def _enhance_video_query_for_news(self, query: str, script_type: str) -> str:
        """Enhance video search query for news content."""
        if script_type == "daily_news":
            # Add news-specific keywords to improve video search
            news_keywords = news_research_service.get_news_keywords(query)
            # Use the first few keywords to enhance the query
            enhanced_query = f"{query} {' '.join(news_keywords[:3])}"
            return enhanced_query
        return query
    
    async def _find_background_videos(
        self,
        video_queries: List[Dict],
        orientation: str,
        script_type: str = "facts",
        segment_duration: float = 3.0,
        footage_provider: str = "pexels",
        footage_quality: str = "high",
        search_safety: str = "moderate",
        ai_video_provider: str = "ltx_video",
        media_type: str = "video",
        image_provider: str = "together",
        motion_params: Optional[Dict[str, Any]] = None
    ) -> List[Optional[Dict[str, Any]]]:
        """Find background videos/images for each search query using specified footage provider and media type."""
        background_videos: List[Optional[Dict[str, Any]]] = []
        used_urls: List[str] = []
        
        logger.info(f"Finding background {media_type}s using provider: {footage_provider}")
        
        for i, query_data in enumerate(video_queries):
            query = query_data['query']
            
            # Enhance query for news content
            enhanced_query = self._enhance_video_query_for_news(query, script_type)
            
            try:
                video_url = None
                
                if footage_provider == "ai_generated":
                    if media_type == "image":
                        # Generate AI images and convert to video
                        video_url = await self._generate_ai_image_to_video(
                            enhanced_query,
                            orientation,
                            query_data['duration'],
                            footage_quality,
                            i,  # Use scene index as seed variation
                            image_provider,
                            # Pass motion parameters from frontend
                            motion_params.get('effect_type', 'ken_burns') if motion_params else 'ken_burns',
                            motion_params.get('zoom_speed', 10.0) if motion_params else 10.0,
                            motion_params.get('pan_direction', 'right') if motion_params else 'right',
                            motion_params.get('ken_burns_keypoints') if motion_params else None
                        )
                    else:
                        # Use specified AI video provider for AI video generation
                        video_url = await self._generate_ai_background_video(
                            enhanced_query,
                            orientation,
                            query_data['duration'],
                            footage_quality,
                            i,  # Use scene index as seed variation
                            ai_video_provider
                        )
                elif footage_provider == "pixabay":
                    if media_type == "image":
                        # Use Pixabay image service and convert to video
                        video_url = await self._get_pixabay_image_to_video(
                            enhanced_query,
                            orientation,
                            query_data['duration'],
                            footage_quality,
                            used_urls,
                            # Pass motion parameters from frontend
                            motion_params.get('effect_type', 'ken_burns') if motion_params else 'ken_burns',
                            motion_params.get('zoom_speed', 10.0) if motion_params else 10.0,
                            motion_params.get('pan_direction', 'right') if motion_params else 'right',
                            motion_params.get('ken_burns_keypoints') if motion_params else None
                        )
                    else:
                        # Use Pixabay video service
                        video_url = await self._get_pixabay_video(
                            enhanced_query,
                            orientation,
                            used_urls
                        )
                else:  # Default to pexels
                    if media_type == "image":
                        # Use Pexels image service and convert to video
                        video_url = await self._get_pexels_image_to_video(
                            enhanced_query,
                            orientation,
                            query_data['duration'],
                            footage_quality,
                            used_urls,
                            # Pass motion parameters from frontend
                            motion_params.get('effect_type', 'ken_burns') if motion_params else 'ken_burns',
                            motion_params.get('zoom_speed', 10.0) if motion_params else 10.0,
                            motion_params.get('pan_direction', 'right') if motion_params else 'right',
                            motion_params.get('ken_burns_keypoints') if motion_params else None
                        )
                    else:
                        # Use Pexels video service
                        video_url = await pexels_service.get_best_video(
                            query=enhanced_query,
                            orientation=orientation,
                            used_urls=used_urls
                        )
                
                if video_url:
                    background_videos.append({
                        'download_url': video_url,
                        'start_time': query_data['start_time'],
                        'end_time': query_data['end_time'],
                        'duration': query_data['duration'],
                        'query': query,
                        'provider': footage_provider
                    })
                    used_urls.append(video_url)
                else:
                    # Try fallback search with different provider
                    logger.warning(f"No video found for query: {query}, trying fallback")
                    fallback_video = await self._get_fallback_video(
                        enhanced_query,
                        orientation,
                        used_urls,
                        footage_provider,
                        query_data,
                        media_type,
                        motion_params.get('use_ai_image_fallback', True) if motion_params else True
                    )
                    if fallback_video:
                        background_videos.append(fallback_video)
                        used_urls.append(fallback_video['download_url'])
                    else:
                        logger.warning(f"No fallback video found for query: {query}")
                        background_videos.append(None)
                    
            except Exception as e:
                logger.error(f"Error finding video for query '{query}': {e}")
                # Try fallback search even on exceptions
                try:
                    fallback_video = await self._get_fallback_video(
                        enhanced_query,
                        orientation,
                        used_urls,
                        footage_provider,
                        query_data,
                        media_type,
                        motion_params.get('use_ai_image_fallback', True) if motion_params else True
                    )
                    if fallback_video:
                        background_videos.append(fallback_video)
                        used_urls.append(fallback_video['download_url'])
                    else:
                        background_videos.append(None)
                except Exception as fallback_error:
                    logger.error(f"Fallback search also failed for query '{query}': {fallback_error}")
                    background_videos.append(None)
        
        return background_videos
    
    async def _generate_ai_background_video(
        self,
        query: str,
        orientation: str,
        duration: float,
        quality: str = "high",
        seed_variation: int = 0,
        ai_video_provider: str = "ltx_video"
    ) -> Optional[str]:
        """Generate AI background video using specified AI video provider (LTX-Video or WaveSpeed)."""
        try:
            if ai_video_provider == "wavespeed":
                return await self._generate_wavespeed_video(query, orientation, duration, quality, seed_variation)
            else:  # Default to LTX-Video
                return await self._generate_ltx_video(query, orientation, duration, quality, seed_variation)
                    
        except Exception as e:
            logger.error(f"Failed to generate AI video for query '{query}' using {ai_video_provider}: {e}")
            return None
    
    async def _generate_ltx_video(
        self,
        query: str,
        orientation: str,
        duration: float,
        quality: str = "high",
        seed_variation: int = 0
    ) -> Optional[str]:
        """Generate AI background video using LTX-Video service."""
        try:
            if not ltx_video_service.is_available():
                logger.warning("LTX-Video service not available, cannot generate AI video")
                return None
            
            # Convert orientation to dimensions (divisible by 32 for LTX-Video)
            if orientation == 'portrait':
                width, height = 704, 960  # 9:16 aspect ratio, divisible by 32
            elif orientation == 'square':
                width, height = 704, 704  # 1:1 aspect ratio
            else:  # landscape
                width, height = 960, 704  # 16:9 aspect ratio
            
            # Convert duration to frame count (assuming 15 fps for LTX-Video)
            fps = 15
            num_frames = min(int(duration * fps), 257)  # LTX-Video max is 257 frames
            
            # Quality settings
            if quality == "ultra":
                num_inference_steps = 300
                guidance_scale = 5.0
            elif quality == "high":
                num_inference_steps = 200
                guidance_scale = 4.5
            else:  # standard
                num_inference_steps = 150
                guidance_scale = 4.0
            
            # Generate video with LTX-Video
            logger.info(f"Generating LTX video for query: '{query}' ({width}x{height}, {num_frames} frames)")
            
            video_data = await ltx_video_service.generate_video(
                prompt=f"A cinematic video of {query}, high quality, professional cinematography",
                negative_prompt="blurry, low quality, text, watermark, logo",
                width=width,
                height=height,
                num_frames=num_frames,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                seed=hash(query + str(seed_variation)) % **********  # Generate seed from query
            )
            
            # Save video to S3
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as temp_file:
                temp_file.write(video_data)
                temp_file_path = temp_file.name
            
            try:
                # Upload to S3
                job_id = str(uuid.uuid4())
                s3_path = f"ai-generated-videos/ltx_{job_id}.mp4"
                video_url = await s3_service.upload_file(
                    file_path=temp_file_path,
                    object_name=s3_path,
                    content_type="video/mp4"
                )
                
                logger.info(f"LTX video generated and uploaded: {video_url}")
                return video_url
                
            finally:
                # Clean up temp file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Failed to generate LTX video for query '{query}': {e}")
            return None
    
    async def _generate_wavespeed_video(
        self,
        query: str,
        orientation: str,
        duration: float,
        quality: str = "high",
        seed_variation: int = 0
    ) -> Optional[str]:
        """Generate AI background video using WaveSpeed service."""
        try:
            if not wavespeed_service.is_available():
                logger.warning("WaveSpeed service not available, cannot generate AI video")
                return None
            
            # Convert orientation to WaveSpeed size format
            if orientation == 'portrait':
                size = "480*832"  # 9:16 aspect ratio
            elif orientation == 'square':
                size = "832*832"  # 1:1 aspect ratio
            else:  # landscape
                size = "832*480"  # 16:9 aspect ratio
            
            # WaveSpeed duration limits
            duration_seconds = min(int(duration), 8)  # WaveSpeed max is 8 seconds
            
            # Quality/model mapping
            if quality == "ultra":
                model = "minimax-video-02"  # High quality model
            elif quality == "high":
                model = "wan-2.2"  # Default fast model
            else:  # standard
                model = "wan-2.2"  # Use default for standard quality
            
            # Generate seed from query and variation
            seed = (hash(query + str(seed_variation)) % **********) if seed_variation != -1 else -1
            
            # Generate video with WaveSpeed
            logger.info(f"Generating WaveSpeed video for query: '{query}' ({size}, {duration_seconds}s, model: {model})")
            
            video_bytes = await wavespeed_service.text_to_video(
                prompt=f"A cinematic video of {query}, high quality, professional cinematography",
                model=model,
                size=size,
                duration=duration_seconds,
                seed=seed
            )
            
            # Save video to S3
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as temp_file:
                temp_file.write(video_bytes)
                temp_file_path = temp_file.name
            
            try:
                # Upload to S3
                job_id = str(uuid.uuid4())
                s3_path = f"ai-generated-videos/wavespeed_{job_id}.mp4"
                video_url = await s3_service.upload_file(
                    file_path=temp_file_path,
                    object_name=s3_path,
                    content_type="video/mp4"
                )
                
                logger.info(f"WaveSpeed video generated and uploaded: {video_url}")
                return video_url
                
            finally:
                # Clean up temp file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Failed to generate WaveSpeed video for query '{query}': {e}")
            return None
    
    async def _get_pixabay_video(
        self,
        query: str,
        orientation: str,
        used_urls: List[str]
    ) -> Optional[str]:
        """Get video from Pixabay service."""
        try:
            if not pixabay_service.is_available():
                logger.warning("Pixabay service not available")
                return None
                
            # Search for videos
            videos = await pixabay_service.search_by_orientation(
                query=query,
                target_orientation=orientation,
                max_results=5
            )
            
            # Find first unused video
            for video in videos:
                video_url = video.get('url')
                if video_url and video_url not in used_urls:
                    logger.info(f"Found Pixabay video for query: '{query}'")
                    return video_url
            
            logger.warning(f"No unused Pixabay videos found for query: '{query}'")
            return None
            
        except Exception as e:
            logger.error(f"Error searching Pixabay for query '{query}': {e}")
            return None
    
    async def _get_fallback_video(
        self,
        query: str,
        orientation: str,
        used_urls: List[str],
        original_provider: str,
        query_data: Dict[str, Any],
        media_type: str = "video",
        use_ai_fallback: bool = True
    ) -> Optional[Dict[str, Any]]:
        """Get fallback video when primary provider fails."""
        # Try different fallback strategies based on original provider
        fallback_providers = []
        
        if original_provider == "ai_generated":
            fallback_providers = ["pexels", "pixabay"]
        elif original_provider == "pixabay":
            fallback_providers = ["pexels"]
        else:  # pexels
            fallback_providers = ["pixabay"]
        
        # First try other stock providers
        for provider in fallback_providers:
            try:
                fallback_url = None
                fallback_query = "nature landscape abstract"  # Generic fallback query
                
                if provider == "pexels":
                    if media_type == "video":
                        fallback_url = await pexels_service.get_best_video(
                            query=fallback_query,
                            orientation=orientation,
                            used_urls=used_urls
                        )
                    else:  # image
                        fallback_url = await self._get_pexels_image_to_video(
                            fallback_query,
                            orientation,
                            query_data['duration'],
                            "high",
                            used_urls
                        )
                elif provider == "pixabay":
                    if media_type == "video":
                        fallback_url = await self._get_pixabay_video(
                            fallback_query,
                            orientation,
                            used_urls
                        )
                    else:  # image
                        fallback_url = await self._get_pixabay_image_to_video(
                            fallback_query,
                            orientation,
                            query_data['duration'],
                            "high",
                            used_urls
                        )
                
                if fallback_url:
                    logger.info(f"Found fallback {media_type} using {provider} for query: '{query}'")
                    return {
                        'download_url': fallback_url,
                        'start_time': query_data['start_time'],
                        'end_time': query_data['end_time'],
                        'duration': query_data['duration'],
                        'query': f"{query} (fallback: {provider})",
                        'provider': f"{original_provider}_fallback_{provider}"
                    }
                    
            except Exception as e:
                logger.error(f"Fallback provider {provider} failed: {e}")
                continue
        
        # If all stock providers failed and we're looking for images, try AI generation as final fallback
        if media_type == "image" and use_ai_fallback and original_provider != "ai_generated":
            logger.info(f"Stock image providers failed for '{query}', trying AI image generation as final fallback")
            try:
                ai_image_url = await self._generate_ai_image_to_video(
                    query,
                    orientation,
                    query_data['duration'],
                    "high",
                    0,  # seed variation
                    "together"  # default AI image provider
                )
                
                if ai_image_url:
                    logger.info(f"Successfully generated AI fallback image for query: '{query}'")
                    return {
                        'download_url': ai_image_url,
                        'start_time': query_data['start_time'],
                        'end_time': query_data['end_time'],
                        'duration': query_data['duration'],
                        'query': f"{query} (AI fallback)",
                        'provider': f"{original_provider}_ai_fallback"
                    }
            except Exception as e:
                logger.error(f"AI image fallback also failed for query '{query}': {e}")
        
        return None
    
    async def _create_fallback_video(self, duration: float, width: int, height: int, frame_rate: int) -> str:
        """Create a fallback video with solid color when no background videos are available."""
        import tempfile
        import asyncio
        import ffmpeg
        
        output_path = None
        try:
            # Create temporary output file
            temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
            output_path = temp_file.name
            temp_file.close()
            
            
            # Create a solid color video using FFmpeg
            # Using a subtle gradient background
            input_stream = ffmpeg.input(
                'color=c=0x1a1a2e:s={}x{}:d={}'.format(width, height, duration),
                f='lavfi'
            )
            
            output = ffmpeg.output(
                input_stream,
                output_path,
                vcodec='libx264',
                pix_fmt='yuv420p',
                r=frame_rate,
                preset='fast',
                crf=23
            )
            
            # Run FFmpeg
            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)
            
            # Upload to S3
            from app.services.s3 import s3_service
            fallback_url = await s3_service.upload_file(output_path, f"fallback/fallback_video_{width}x{height}.mp4")
            
            # Clean up temporary file
            os.unlink(output_path)
            
            return fallback_url
            
        except Exception as e:
            logger.error(f"Failed to create fallback video: {e}")
            # Clean up on error
            if output_path and os.path.exists(output_path):
                os.unlink(output_path)
            raise
    
    async def _compose_background_video(self, background_videos: List[Optional[Dict[str, Any]]],
                                      target_duration: float, width: int, height: int,
                                      frame_rate: int) -> str:
        """Compose background videos with proper timing and portrait format handling."""
        try:
            # Filter out None values and create fallback videos if needed
            valid_videos = [video for video in background_videos if video is not None]
            
            if not valid_videos:
                logger.warning("No valid background videos found, creating fallback video")
                # Create a simple fallback video with solid color
                fallback_video = await self._create_fallback_video(target_duration, width, height, frame_rate)
                return fallback_video
            
            composed_video = await self.video_composer.compose_timed_videos(
                video_segments=valid_videos,
                target_duration=target_duration,
                output_width=width,
                output_height=height,
                frame_rate=frame_rate
            )
            
            # Apply portrait format optimization if this is portrait orientation
            is_portrait = height > width
            if is_portrait:
                    return await self._apply_portrait_format(composed_video, width, height)
            
            return composed_video
            
        except Exception as e:
            logger.error(f"Failed to compose background video: {e}")
            raise ValueError(f"Video composition failed: {str(e)}")
    
    async def _compose_background_video_moviepy(self, background_videos: List[Optional[Dict[str, Any]]],
                                              target_duration: float, width: int, height: int,
                                              frame_rate: int) -> str:
        """Compose background videos using MoviePy with precise timing."""
        try:
            # Filter out None values and create fallback videos if needed
            valid_videos = [video for video in background_videos if video is not None]
            
            if not valid_videos:
                logger.warning("No valid background videos found, falling back to FFmpeg composer")
                # Fallback to FFmpeg composer for fallback video creation
                return await self._compose_background_video(background_videos, target_duration, width, height, frame_rate)
            
            
            # Use MoviePy composer for precise timing like original Text-To-Video-AI project
            composed_video = await self.moviepy_composer.compose_timed_videos(
                video_segments=valid_videos,
                target_duration=target_duration,
                output_width=width,
                output_height=height,
                frame_rate=frame_rate
            )
            
            # MoviePy composer already handles orientation correctly in FFmpeg preprocessing
            # No need for additional portrait format optimization
            return composed_video
            
        except Exception as e:
            logger.error(f"MoviePy composition failed: {e}")
            logger.info("Falling back to FFmpeg composer")
            # Fallback to original FFmpeg composer
            return await self._compose_background_video(background_videos, target_duration, width, height, frame_rate)
    
    async def _add_audio_to_video(self, video_url: str, audio_url: str) -> str:
        """Add audio to the composed video."""
        try:
            audio_params = {
                'video_url': video_url,
                'audio_url': audio_url,
                'sync_mode': 'replace',
                'match_length': 'audio',
                'video_volume': 0,  # Mute original video audio
                'audio_volume': 100
            }
            
            result = await add_audio_service.process_job(f"pipeline_audio_{uuid.uuid4().hex[:8]}", audio_params)
            return result['url']
            
        except Exception as e:
            logger.error(f"Failed to add audio to video: {e}")
            raise ValueError(f"Audio addition failed: {str(e)}")
    
    async def _add_captions_to_video(self, video_url: str, script_text: str,
                                   caption_style: str, width: int, height: int, estimated_duration: float, caption_properties: Dict[str, Any] | None = None) -> Dict[str, Any]:
        """Add captions to the video using the unified caption system with backend presets."""
        try:
            # Apply backend caption style presets if no explicit properties provided
            if caption_properties is None:
                caption_properties = self._get_responsive_caption_properties_from_config(caption_style, width, height)
            else:
                # Merge with config-based defaults while preserving user overrides
                config_properties = self._get_responsive_caption_properties_from_config(caption_style, width, height)
                config_properties.update(caption_properties)  # User properties override config
                caption_properties = config_properties
            
            # Ensure the style is set in the properties
            caption_properties["style"] = caption_style
            
            # Use the unified caption system instead of custom implementation
            job_id = str(uuid.uuid4())
            
            result = await add_captions_service.process_job(
                job_id=job_id,
                params={
                    "video_url": video_url,
                    "captions": script_text,
                    "caption_properties": caption_properties,
                    "language": "auto"
                }
            )
            
            # Verify the result has the expected structure (caption service returns 'url', not 'video_url')
            if not result or not (result.get('video_url') or result.get('url')):
                logger.error("Caption service returned invalid result structure")
                raise ValueError("Invalid caption service result")
            
            return {
                'video_url': result.get('video_url') or result.get('url', video_url),
                'srt_url': result.get('srt_url'),
                'caption_style': caption_style
            }
            
        except Exception as e:
            logger.error(f"Failed to add captions to video using unified system: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Video URL: {video_url}")
            logger.error(f"Script text length: {len(script_text) if script_text else 0}")
            # Fallback to custom implementation if unified system fails
            logger.warning("Falling back to custom caption implementation")
            # Always generate fresh responsive properties for fallback to avoid unbound variable issues
            fallback_responsive_properties = self._get_responsive_caption_properties_from_config(caption_style, width, height)
            return await self._add_captions_to_video_fallback(video_url, script_text, caption_style, width, height, estimated_duration, fallback_responsive_properties)
    
    def _get_responsive_caption_properties(self, style: str, width: int, height: int) -> Dict:
        """Get responsive caption properties based on style and video dimensions."""
        # Calculate responsive values based on video dimensions
        is_portrait = height > width
        is_square = abs(width - height) < 100
        
        # Smaller base font size calculation for better mobile experience
        if is_portrait:
            base_font_size = max(28, int(height * 0.028))  # Reduced to 2.8% of height (20% smaller)
            max_words_per_line = 3  # Reduced for better sync and readability
            margin_bottom = max(100, int(height * 0.08))   # Increased margin for mobile safe area
        elif is_square:
            base_font_size = max(24, int(height * 0.024))  # Reduced to 2.4% of height (20% smaller)
            max_words_per_line = 3
            margin_bottom = max(60, int(height * 0.04))    # 4% of height
        else:  # landscape
            base_font_size = max(42, int(height * 0.045))  # Increased to 4.5% of height for better readability
            max_words_per_line = 4
            margin_bottom = max(50, int(height * 0.035))   # 3.5% of height
        
        # Outline width relative to font size
        outline_width = max(2, int(base_font_size * 0.1))  # 10% of font size for better visibility
        
        style_presets = {
            'viral_bounce': {
                'style': 'viral_bounce',
                'font_size': base_font_size,  # Removed multiplier for mobile optimization
                'line_color': 'white',
                'word_color': 'yellow',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'center',
                'all_caps': True,
                'bounce_intensity': 1.5,
                'animation_speed': 1.2,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45
            },
            'typewriter': {
                'style': 'typewriter',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'typewriter_speed': 3.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,  # Optimized spacing
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45  # Reduced for better mobile fit
            },
            'fade_in': {
                'style': 'fade_in',
                'font_size': base_font_size,  # Removed multiplier for mobile optimization
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'animation_speed': 1.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            },
            'classic': {
                'style': 'classic',
                'font_size': base_font_size,  # Removed multiplier for mobile optimization
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.1,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            }
        }
        
        return style_presets.get(style, style_presets['classic'])

    def _get_responsive_caption_properties_from_config(self, style: str, width: int, height: int) -> Dict:
        """Get responsive caption properties from configuration with responsive adjustments."""
        try:
            # Get base configuration for the style
            base_config = get_caption_style(style)
            
            # Calculate responsive values based on video dimensions
            is_portrait = height > width
            is_square = abs(width - height) < 100
            
            # Responsive font size calculation
            base_font_size = base_config.get('font_size', 48)
            if is_portrait:
                # Reduce font size for mobile portrait
                responsive_font_size = max(24, int(base_font_size * 0.7))
                max_words = base_config.get('max_words_per_line', 4)
                margin_bottom = max(100, int(height * 0.08))
            elif is_square:
                responsive_font_size = max(20, int(base_font_size * 0.6))
                max_words = base_config.get('max_words_per_line', 4)
                margin_bottom = max(60, int(height * 0.04))
            else:  # landscape
                responsive_font_size = max(32, int(base_font_size * 0.8))
                max_words = base_config.get('max_words_per_line', 6)
                margin_bottom = max(50, int(height * 0.035))
            
            # Build responsive properties
            responsive_properties = {
                'style': base_config.get('style', style),
                'font_size': responsive_font_size,
                'font_family': base_config.get('font_family', 'Arial-Bold'),
                'line_color': base_config.get('line_color', '#FFFFFF'),
                'word_color': base_config.get('word_color', '#FFFF00'),
                'outline_color': base_config.get('outline_color', 'black'),
                'outline_width': max(1, int(responsive_font_size * 0.08)),
                'position': self._map_position(base_config.get('position', 'bottom_center')),
                'max_words_per_line': max_words,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'bold': base_config.get('bold', True),
                'all_caps': base_config.get('all_caps', False)
            }
            
            # Add style-specific properties
            if 'viral' in style or 'bounce' in style:
                responsive_properties.update({
                    'bounce_intensity': 1.5,
                    'animation_speed': 1.2
                })
            elif 'typewriter' in style:
                responsive_properties.update({
                    'typewriter_speed': 3.0
                })
            elif 'fade' in style:
                responsive_properties.update({
                    'animation_speed': 1.0
                })
            
            return responsive_properties
            
        except Exception as e:
            logger.warning(f"Failed to load caption style config for '{style}': {e}")
            # Fallback to the original hardcoded responsive properties
            return self._get_responsive_caption_properties(style, width, height)

    def _map_position(self, config_position: str) -> str:
        """Map configuration position to caption service format."""
        position_mapping = {
            'top_center': 'top',
            'center': 'center',
            'bottom_center': 'bottom',
            'top': 'top',
            'bottom': 'bottom'
        }
        return position_mapping.get(config_position, 'bottom')

    def _get_default_caption_properties(self, style: str) -> Dict:
        """Get default caption properties based on style."""
        style_presets = {
            'viral_bounce': {
                'style': 'viral_bounce',
                'font_size': 60,
                'line_color': 'white',
                'word_color': 'yellow',
                'outline_color': 'black',
                'outline_width': 3,
                'position': 'center',
                'all_caps': True,
                'bounce_intensity': 1.5,
                'animation_speed': 1.2
            },
            'typewriter': {
                'style': 'typewriter',
                'font_size': 42,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': 3,
                'position': 'bottom_center',
                'typewriter_speed': 3.0,
                'max_words_per_line': 4,
                'line_spacing': 1.2,
                'margin_bottom': 80,
                'text_align': 'center'
            },
            'fade_in': {
                'style': 'fade_in',
                'font_size': 52,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': 2,
                'position': 'bottom_center',
                'animation_speed': 1.0
            },
            'classic': {
                'style': 'classic',
                'font_size': 48,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': 2,
                'position': 'bottom_center'
            }
        }
        
        return style_presets.get(style, style_presets['classic'])

    async def _add_captions_to_video_fallback(self, video_url: str, script_text: str,
                                            caption_style: str, width: int, height: int, estimated_duration: float, responsive_properties: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback method using the original custom caption implementation."""
        try:
            
            # Create simple SRT content with proper timing
            srt_content = self._create_simple_srt(script_text, estimated_duration)
            
            # Upload SRT file to S3 as a separate file
            srt_url = await self._upload_srt_file(srt_content)
            
            # Use simple FFmpeg to burn subtitles with responsive properties
            result_url = await self._burn_simple_subtitles(video_url, srt_content, width, height, responsive_properties)
            
            return {
                'video_url': result_url,
                'srt_url': srt_url,
                'caption_style': caption_style
            }
        except Exception as e:
            logger.error(f"Failed to add captions using fallback method: {e}")
            logger.error(f"Fallback exception type: {type(e).__name__}")
            raise ValueError(f"Caption addition failed: {str(e)}")
    
    def _create_simple_srt(self, script_text: str, duration: float) -> str:
        """Create simple SRT content with word-by-word timing for better audio sync."""
        import re
        
        # Split script into words
        words = re.findall(r'\S+', script_text.strip())
        
        if not words:
            return ""
        
        # Calculate timing based on natural speaking rate (~2.5 words per second)
        speaking_rate = 2.5  # words per second
        time_per_word = 1.0 / speaking_rate
        
        # Group words into 2-word chunks for better readability and sync
        chunk_size = 2
        chunks = []
        for i in range(0, len(words), chunk_size):
            chunk = ' '.join(words[i:i + chunk_size])
            chunks.append(chunk)
        
        # Adjust total duration to match actual audio
        total_time_needed = len(words) * time_per_word
        time_scale = duration / total_time_needed if total_time_needed > 0 else 1.0
        
        srt_content = ""
        current_time = 0.0
        
        for i, chunk in enumerate(chunks):
            start_time = current_time
            # Calculate chunk duration based on word count
            words_in_chunk = len(chunk.split())
            chunk_duration = words_in_chunk * time_per_word * time_scale
            end_time = start_time + chunk_duration
            
            # Format SRT timestamps
            start_srt = self._seconds_to_srt_time(start_time)
            end_srt = self._seconds_to_srt_time(end_time)
            
            # Add SRT entry
            srt_content += f"{i + 1}\n{start_srt} --> {end_srt}\n{chunk}\n\n"
            
            current_time = end_time
        
        return srt_content
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    async def _burn_simple_subtitles(self, video_url: str, srt_content: str, width: int, height: int, responsive_properties: Dict[str, Any]) -> str:
        """Burn simple subtitles directly into video using FFmpeg with responsive properties."""
        import tempfile
        import uuid
        import ffmpeg
        
        # Download video file
        video_path = await self._download_video_file(video_url)
        
        # Create temporary SRT file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False) as srt_file:
            srt_file.write(srt_content)
            srt_path = srt_file.name
        
        # Create output path
        output_path = f"/tmp/captioned_{uuid.uuid4()}.mp4"
        
        try:
            # Use responsive properties for mobile-optimized captions
            font_size = responsive_properties.get('font_size', 45)
            outline_width = responsive_properties.get('outline_width', 2)
            margin_bottom = responsive_properties.get('margin_bottom', 80)
            line_color = responsive_properties.get('line_color', 'white')
            outline_color = responsive_properties.get('outline_color', 'black')
            
            # Convert colors to BGR hex format for ASS subtitles
            color_map = {
                'white': '&Hffffff',
                'black': '&H000000',
                'yellow': '&H00ffff',
                'red': '&H0000ff',
                'blue': '&Hff0000',
                'green': '&H00ff00'
            }
            
            primary_colour = color_map.get(line_color.lower(), '&Hffffff')
            outline_colour = color_map.get(outline_color.lower(), '&H000000')
            
            # Use FFmpeg to burn subtitles with responsive styling
            (
                ffmpeg
                .input(video_path)
                .output(
                    output_path,
                    vf=f"subtitles={srt_path}:force_style='FontSize={font_size},PrimaryColour={primary_colour},OutlineColour={outline_colour},BackColour=&H80000000,BorderStyle=1,Outline={outline_width},Shadow=0,Alignment=2,MarginV={margin_bottom}'",
                    vcodec='libx264',
                    acodec='copy'
                )
                .overwrite_output()
                .run(quiet=True)
            )
            
            # Upload result to S3
            filename = f"captioned_videos/{uuid.uuid4()}.mp4"
            result_url = await s3_service.upload_file(output_path, filename)
            
            # Cleanup temp files
            os.remove(video_path)
            os.remove(srt_path)
            os.remove(output_path)
            
            return result_url
            
        except Exception as e:
            logger.error(f"Failed to burn subtitles: {e}")
            # Cleanup on error
            for path in [video_path, srt_path, output_path]:
                if os.path.exists(path):
                    os.remove(path)
            raise
    
    async def _upload_srt_file(self, srt_content: str) -> str:
        """Upload SRT content to S3 as a separate file."""
        import tempfile
        import uuid
        
        # Create temporary SRT file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False) as srt_file:
            srt_file.write(srt_content)
            srt_path = srt_file.name
        
        try:
            # Upload to S3
            filename = f"subtitles/{uuid.uuid4()}.srt"
            srt_url = await s3_service.upload_file(srt_path, filename)
            
            # Clean up temporary file
            os.remove(srt_path)
            
            return srt_url
            
        except Exception as e:
            logger.error(f"Failed to upload SRT file: {e}")
            # Clean up on error
            if os.path.exists(srt_path):
                os.remove(srt_path)
            raise

    async def _apply_portrait_format(self, video_url: str, width: int, height: int) -> str:
        """Apply YouTube Shorts format to video using FFmpeg."""
        import uuid
        import subprocess
        
        # Download video file
        video_path = await self._download_video_file(video_url)
        
        # Create output path
        output_path = f"/tmp/portrait_formatted_{uuid.uuid4()}.mp4"
        
        try:
            # Use the same FFmpeg command as YouTube Shorts service for vertical format
            cmd = [
                'ffmpeg', '-i', video_path, '-c:v', 'libx264', '-c:a', 'aac',
                '-vf', 'scale=720:1280:force_original_aspect_ratio=increase,crop=720:1280',
                '-r', '30', '-b:a', '128k', '-crf', '18', '-preset', 'slow', '-b:v', '5M',
                '-y', output_path
            ]
            
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                raise RuntimeError("Portrait format application failed")
            
            # Upload formatted video to S3
            filename = f"portrait_formatted/{uuid.uuid4()}.mp4"
            formatted_url = await s3_service.upload_file(output_path, filename)
            
            # Clean up temporary files
            os.remove(video_path)
            os.remove(output_path)
            
            return formatted_url
            
        except Exception as e:
            logger.error(f"Failed to apply portrait format: {e}")
            # Clean up on error
            for path in [video_path, output_path]:
                if os.path.exists(path):
                    os.remove(path)
            raise ValueError(f"Portrait format application failed: {str(e)}")

    async def _add_background_music(
        self,
        voice_audio_url: str,
        background_music: str,
        mood: str,
        volume: float,
        duration: float
    ) -> Dict[str, Optional[str]]:
        """
        Add background music to voice audio.
        
        Args:
            voice_audio_url: URL of the voice audio
            background_music: Either 'generate' for AI music or filename of existing track
            mood: Music mood for selection/generation
            volume: Background music volume (0.0 to 1.0)
            duration: Target duration for the audio
            
        Returns:
            Dictionary containing:
            - mixed_audio_url: URL of the final audio with background music
            - background_music_url: URL of the background music file used
        """
        try:
            import tempfile
            import uuid
            import subprocess
            import os
            
            # Download voice audio
            voice_audio_path = await self._download_audio_file(voice_audio_url)
            
            # Get background music file
            background_music_path = None
            background_music_url = None
            
            if background_music == 'generate':
                # Use AI-generated music via MusicGen
                try:
                    from app.services.audio.music_generation import music_generation_service
                    from app.services.ai.image_prompt_generator import image_prompt_generator
                    
                    logger.info("Generating AI background music")
                    
                    # Generate music prompt based on mood and content
                    music_prompt = f"Instrumental background music with {mood} mood, suitable for video narration, gentle and unobtrusive"
                    
                    music_params = {
                        'description': music_prompt,
                        'duration': min(int(duration), 30),  # Limit to 30s for faster generation
                        'model_size': 'small',  # Use small model for faster generation
                        'output_format': 'wav'
                    }
                    
                    music_result = await music_generation_service.process_music_generation(f"topic_video_music_{uuid.uuid4().hex[:8]}", music_params)
                    generated_music_url = music_result.get('audio_url')
                    
                    if generated_music_url:
                        # Download the generated music
                        background_music_path = await self._download_audio_file(generated_music_url)
                        background_music_url = generated_music_url
                        logger.info(f"Successfully generated AI background music: {generated_music_url}")
                    else:
                        raise Exception("No audio URL returned from music generation")
                        
                except Exception as e:
                    logger.warning(f"AI music generation failed: {e}, falling back to mood-based track")
                    # Fall back to selecting a track by mood
                    tracks = music_service.get_tracks_by_mood(mood)
                    if tracks:
                        selected_track = tracks[0]  # Use first matching track
                        background_music_path = music_service.get_track_path(selected_track['file'])
                        # Upload the track to S3 for consistent access
                        if background_music_path:
                            s3_key = f"music/{selected_track['file']}"
                            background_music_url = await s3_service.upload_file(background_music_path, s3_key)
                        logger.info(f"Using fallback mood-based track: {selected_track['title']} for mood: {mood}")
                    else:
                        logger.warning(f"No tracks found for mood: {mood}, skipping background music")
                        return {"mixed_audio_url": voice_audio_url, "background_music_url": None}
            else:
                # Check if background_music is a mood or a specific track file
                logger.info(f"Looking for mood-based music: '{background_music}'")
                tracks = music_service.get_tracks_by_mood(background_music)
                logger.info(f"Found {len(tracks)} tracks for mood '{background_music}': {[t['title'] for t in tracks]}")
                if tracks:
                    # It's a mood, use the first matching track
                    selected_track = tracks[0]
                    background_music_path = music_service.get_track_path(selected_track['file'])
                    # Upload the track to S3 for consistent access
                    if background_music_path:
                        s3_key = f"music/{selected_track['file']}"
                        background_music_url = await s3_service.upload_file(background_music_path, s3_key)
                    logger.info(f"Using mood-based track: {selected_track['title']} for mood: {background_music}")
                elif music_service.validate_track_exists(background_music):
                    # It's a specific track file
                    background_music_path = music_service.get_track_path(background_music)
                    track_info = music_service.get_track_by_file(background_music)
                    # Upload the track to S3 for consistent access
                    if background_music_path:
                        s3_key = f"music/{background_music}"
                        background_music_url = await s3_service.upload_file(background_music_path, s3_key)
                    logger.info(f"Using specific track: {track_info['title'] if track_info else background_music}")
                else:
                    logger.warning(f"Background music file not found: {background_music}, skipping background music")
                    return {"mixed_audio_url": voice_audio_url, "background_music_url": None}
            
            if not background_music_path or not os.path.exists(background_music_path):
                logger.warning(f"Background music file not accessible, skipping - Path: {background_music_path}, Exists: {os.path.exists(background_music_path) if background_music_path else 'N/A'}")
                logger.warning(f"Music service directory: {music_service.music_dir}, Directory exists: {os.path.exists(music_service.music_dir) if music_service.music_dir else 'N/A'}")
                if background_music_path:
                    logger.warning(f"Music file directory listing: {os.listdir(os.path.dirname(background_music_path)) if os.path.exists(os.path.dirname(background_music_path)) else 'Directory not found'}")
                return {"mixed_audio_url": voice_audio_url, "background_music_url": None}
            
            # Create output path
            output_path = f"/tmp/audio_with_music_{uuid.uuid4()}.mp3"
            
            # Mix audio using FFmpeg
            # Use amix filter to combine voice and background music
            cmd = [
                'ffmpeg', '-y',
                '-i', voice_audio_path,
                '-i', background_music_path,
                '-filter_complex', 
                f'[0:a]volume=1.0[voice];[1:a]volume={volume}[music];[voice][music]amix=inputs=2:duration=first:dropout_transition=2',
                '-t', str(duration),  # Limit to target duration
                '-c:a', 'libmp3lame',
                '-b:a', '192k',
                output_path
            ]
            
            logger.info(f"Mixing audio with background music: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"FFmpeg audio mixing failed: {result.stderr}")
                # Return original audio if mixing fails
                return {"mixed_audio_url": voice_audio_url, "background_music_url": None}
            
            # Upload mixed audio to S3
            s3_key = f"audio/mixed_audio_{uuid.uuid4()}.mp3"
            mixed_audio_url = await s3_service.upload_file(output_path, s3_key)
            
            # Clean up temp files
            try:
                os.unlink(voice_audio_path)
                os.unlink(output_path)
            except Exception as cleanup_error:
                logger.warning(f"Failed to clean up temp files: {cleanup_error}")
            
            return {"mixed_audio_url": mixed_audio_url, "background_music_url": background_music_url}
            
        except Exception as e:
            logger.error(f"Failed to add background music: {e}")
            # Return original audio if background music processing fails
            return {"mixed_audio_url": voice_audio_url, "background_music_url": None}
    
    async def _download_audio_file(self, audio_url: str) -> str:
        """Download audio file from URL."""
        import aiohttp
        import uuid
        
        output_path = f"/tmp/audio_{uuid.uuid4()}.mp3"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(audio_url) as response:
                if response.status == 200:
                    with open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    return output_path
                else:
                    raise ValueError(f"Failed to download audio: HTTP {response.status}")
    
    async def _download_video_file(self, video_url: str) -> str:
        """Download video file from URL."""
        import aiohttp
        import uuid
        
        output_path = f"/tmp/video_{uuid.uuid4()}.mp4"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(video_url) as response:
                if response.status == 200:
                    with open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    return output_path
                else:
                    raise ValueError(f"Failed to download video: HTTP {response.status}")
    
    async def _get_pexels_image_to_video(
        self,
        query: str,
        orientation: str,
        duration: float,
        quality: str,
        used_urls: List[str],
        effect_type: str = "ken_burns",
        zoom_speed: float = 10.0,
        pan_direction: str = "right",
        ken_burns_keypoints: Optional[List[Dict[str, float]]] = None
    ) -> Optional[str]:
        """Get Pexels stock image and convert to video with motion effects."""
        try:
            if not pexels_image_service.is_available():
                logger.warning("Pexels image service not available")
                return None
            
            # Get stock image from Pexels
            image_url = await pexels_image_service.get_best_image(
                query=query,
                orientation=orientation,
                quality=quality,
                used_urls=used_urls
            )
            
            if not image_url:
                logger.warning(f"No Pexels image found for query: {query}")
                return None
            
            # Convert image to video using image-to-video service
            return await self._convert_image_to_video(
                image_url, 
                duration, 
                orientation,
                effect_type=effect_type,
                zoom_speed=zoom_speed,
                pan_direction=pan_direction,
                ken_burns_keypoints=ken_burns_keypoints
            )
            
        except Exception as e:
            logger.error(f"Failed to get Pexels image for query '{query}': {e}")
            return None
    
    async def _get_pixabay_image_to_video(
        self,
        query: str,
        orientation: str,
        duration: float,
        quality: str,
        used_urls: List[str],
        effect_type: str = "ken_burns",
        zoom_speed: float = 10.0,
        pan_direction: str = "right",
        ken_burns_keypoints: Optional[List[Dict[str, float]]] = None
    ) -> Optional[str]:
        """Get Pixabay stock image and convert to video with motion effects."""
        try:
            if not pixabay_image_service.is_available():
                logger.warning("Pixabay image service not available")
                return None
            
            # Get stock image from Pixabay
            image_url = await pixabay_image_service.get_best_image(
                query=query,
                orientation=orientation,
                quality=quality,
                used_urls=used_urls
            )
            
            if not image_url:
                logger.warning(f"No Pixabay image found for query: {query}")
                return None
            
            # Convert image to video using image-to-video service
            return await self._convert_image_to_video(
                image_url, 
                duration, 
                orientation,
                effect_type=effect_type,
                zoom_speed=zoom_speed,
                pan_direction=pan_direction,
                ken_burns_keypoints=ken_burns_keypoints
            )
            
        except Exception as e:
            logger.error(f"Failed to get Pixabay image for query '{query}': {e}")
            return None
    
    async def _generate_ai_image_to_video(
        self,
        query: str,
        orientation: str,
        duration: float,
        quality: str,
        seed_variation: int,
        image_provider: str,
        effect_type: str = "ken_burns",
        zoom_speed: float = 10.0,
        pan_direction: str = "right",
        ken_burns_keypoints: Optional[List[Dict[str, float]]] = None
    ) -> Optional[str]:
        """Generate AI image and convert to video with motion effects."""
        try:
            # This would use the existing AI image generation from aiimage_to_video_pipeline
            # For now, we'll use a simple approach
            from app.services.ai.together_ai_service import together_ai_service
            from app.services.ai.flux_service import flux_service
            from app.services.pollinations_service import pollinations_service
            
            # Determine dimensions based on orientation
            if orientation == 'portrait':
                width, height = 1080, 1920
            elif orientation == 'square':
                width, height = 1080, 1080
            else:  # landscape
                width, height = 1920, 1080
            
            # Generate image based on provider
            image_data = None
            if image_provider == "together":
                if together_ai_service.is_available():
                    result = await together_ai_service.generate_image(
                        prompt=f"A cinematic scene of {query}, high quality, professional photography",
                        width=width,
                        height=height,
                        steps=4
                    )
                    # Extract bytes from the result dictionary
                    if result and 'data' in result and len(result['data']) > 0:
                        import base64
                        b64_data = result['data'][0].get('b64_json')
                        if b64_data:
                            image_data = base64.b64decode(b64_data)
            elif image_provider == "flux":
                if flux_service.is_available():
                    image_data = await flux_service.generate_image(
                        prompt=f"A cinematic scene of {query}, high quality, professional photography",
                        width=width,
                        height=height,
                        num_inference_steps=4,
                        guidance_scale=3.5,
                        seed=hash(query + str(seed_variation)) % **********
                    )
            elif image_provider == "pollinations":
                # Pollinations service doesn't have is_available method, just try to use it
                try:
                    image_data = await pollinations_service.generate_image(
                        prompt=f"A cinematic scene of {query}, high quality, professional photography",
                        width=width,
                        height=height,
                        enhance=True,
                        seed=hash(query + str(seed_variation)) % **********
                    )
                except Exception as e:
                    logger.warning(f"Pollinations image generation failed: {e}")
                    image_data = None
            
            if not image_data:
                logger.warning(f"Failed to generate AI image for query: {query}")
                return None
            
            # Save image to temporary file and upload to S3
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
                temp_file.write(image_data)
                temp_file_path = temp_file.name
            
            try:
                # Upload image to S3
                job_id = str(uuid.uuid4())
                s3_path = f"ai-generated-images/{image_provider}_{job_id}.png"
                image_url = await s3_service.upload_file(
                    file_path=temp_file_path,
                    object_name=s3_path,
                    content_type="image/png"
                )
                
                # Convert image to video
                return await self._convert_image_to_video(
                    image_url, 
                    duration, 
                    orientation,
                    effect_type=effect_type,
                    zoom_speed=zoom_speed,
                    pan_direction=pan_direction,
                    ken_burns_keypoints=ken_burns_keypoints
                )
                
            finally:
                # Clean up temp file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            
        except Exception as e:
            logger.error(f"Failed to generate AI image for query '{query}': {e}")
            return None
    
    async def _convert_image_to_video(
        self,
        image_url: str,
        duration: float,
        orientation: str,
        effect_type: str = "ken_burns",
        zoom_speed: float = 10.0,
        pan_direction: str = "right",
        ken_burns_keypoints: Optional[List[Dict[str, float]]] = None
    ) -> Optional[str]:
        """Convert a single image to video with motion effects using the image-to-video service."""
        try:
            # Determine dimensions and frame rate based on orientation
            if orientation == 'portrait':
                frame_rate = 30
            elif orientation == 'square':
                frame_rate = 30
            else:  # landscape
                frame_rate = 30
            
            # Use the image-to-video service to convert the image
            # Don't use match_length="audio" since we have no audio at this stage
            params = {
                "image_url": image_url,
                "video_length": float(duration),
                "frame_rate": frame_rate,
                "zoom_speed": zoom_speed,  # Use passed zoom_speed parameter
                "effect_type": effect_type,  # Use passed effect_type parameter
                "pan_direction": pan_direction,  # Use passed pan_direction parameter
                "match_length": "video",  # Use video length since no audio provided
                "should_add_captions": False,  # No captions at this stage
                "narrator_vol": 0,  # No audio at this stage
                "background_music_vol": 0  # No audio at this stage
            }
            
            # Add Ken Burns keypoints if provided
            if ken_burns_keypoints:
                params["ken_burns_keypoints"] = ken_burns_keypoints
            
            result = await image_to_video_service.image_to_video(params)
            
            if result and 'video_url' in result:
                logger.info(f"Successfully converted image to video: {result['video_url']}")
                return result['video_url']
            else:
                logger.warning("Image-to-video conversion failed: no video URL returned")
                return None
                
        except Exception as e:
            logger.error(f"Failed to convert image to video: {e}")
            return None


# Create a singleton instance
footage_to_video_pipeline = FootageToVideoPipeline()