"""
Scenes-to-video service for converting scene-based input to video pipeline format.

This service adapts the external API scene format to work with the existing
footage-to-video pipeline infrastructure.
"""

import time
from typing import Any, Dict, List, Optional
from app.services.ai.footage_to_video_pipeline import FootageToVideoPipeline


class ScenesVideoService:
    """Service for creating videos from scene-based input."""
    
    def __init__(self):
        self.topic_video_pipeline = FootageToVideoPipeline()
    
    async def create_video(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create video from scenes with search terms.
        
        Converts scenes format to existing pipeline format and processes video.
        """
        start_time = time.time()
        
        try:
            scenes = request_data.get("scenes", [])
            config = request_data.get("config", {})
            
            if not scenes:
                raise ValueError("At least one scene is required")
            
            # Extract configuration with defaults
            voice = config.get("voice", "af_heart")
            provider = config.get("provider", "kokoro")
            music = config.get("music", "chill")
            caption_position = config.get("captionPosition", "bottom")
            orientation = config.get("orientation", "portrait")
            music_volume = config.get("musicVolume", "medium")
            padding_back = config.get("paddingBack", 1500)
            resolution = config.get("resolution", "1080x1920")
            caption_style = config.get("captionStyle", "viral_bounce")
            caption_color = config.get("captionColor")
            language = config.get("language", "en")
            
            # Convert scenes to script text
            script_parts = []
            search_queries = []
            
            for i, scene in enumerate(scenes):
                text = scene.get("text", "").strip()
                search_terms = scene.get("searchTerms", [])
                
                if text:
                    script_parts.append(text)
                    
                    # Create video search query from search terms
                    if search_terms:
                        # Use the first search term as primary query
                        primary_query = search_terms[0] if search_terms else "abstract"
                        search_queries.append({
                            "query": primary_query,
                            "start_time": i * 3.0,  # Estimate 3 seconds per scene
                            "end_time": (i + 1) * 3.0,
                            "duration": 3.0,
                            "visual_concept": ", ".join(search_terms)
                        })
            
            # Join all scene text into a complete script
            complete_script = " ".join(script_parts)
            
            # Convert volume level
            volume_map = {"low": 0.1, "medium": 0.3, "high": 0.5}
            background_music_volume = volume_map.get(music_volume, 0.3)
            
            # Convert orientation to video dimensions
            orientation_map = {
                "portrait": {"width": 1080, "height": 1920},
                "landscape": {"width": 1920, "height": 1080},
                "square": {"width": 1080, "height": 1080}
            }
            
            # Parse resolution if provided
            if "x" in resolution:
                try:
                    width, height = map(int, resolution.split("x"))
                    video_dimensions = {"width": width, "height": height}
                except:
                    video_dimensions = orientation_map.get(orientation, orientation_map["portrait"])
            else:
                video_dimensions = orientation_map.get(orientation, orientation_map["portrait"])
            
            # Create footage-to-video request format
            adapted_request = {
                "topic": f"Custom video with {len(scenes)} scenes",
                "language": language,
                "script_provider": "direct",  # Use direct script instead of generation
                "script_type": "custom",
                "max_duration": len(scenes) * 5,  # Estimate max duration
                "voice": voice,
                "tts_provider": provider,
                "tts_speed": 1.0,
                "video_orientation": orientation,
                "segment_duration": 3.0,
                "background_music": music if music != "none" else None,
                "background_music": music,
                "background_music_volume": background_music_volume,
                "add_captions": True,
                "caption_style": caption_style,
                "caption_color": caption_color,
                "caption_position": caption_position,
                "output_width": video_dimensions["width"],
                "output_height": video_dimensions["height"],
                "frame_rate": 30,
                # Footage provider settings
                "footage_provider": config.get("footageProvider", "pexels"),
                "footage_quality": config.get("footageQuality", "high"),
                "search_safety": config.get("searchSafety", "moderate"),
                "_direct_script": complete_script,  # Pass the complete script directly
                "_search_queries": search_queries,  # Pass predefined search queries
                "_scenes_mode": True  # Flag to indicate scenes mode
            }
            
            # Use the existing footage-to-video pipeline with adaptations
            result = await self.topic_video_pipeline.process_footage_to_video(adapted_request)
            
            # Adapt result format to match ScenesVideoResult
            processing_time = time.time() - start_time
            
            adapted_result = {
                "final_video_url": result.get("final_video_url"),
                "video_with_audio_url": result.get("video_with_audio_url"),
                "audio_url": result.get("audio_url"),
                "background_videos_used": result.get("background_videos_used", []),
                "srt_url": result.get("srt_url"),
                "video_duration": result.get("video_duration", 0.0),
                "processing_time": processing_time,
                "scenes_processed": len(scenes),
                "total_text_length": len(complete_script)
            }
            
            return adapted_result
            
        except Exception as e:
            raise Exception(f"Failed to create video from scenes: {str(e)}")
    
    def _convert_volume_level(self, volume_str: str) -> float:
        """Convert string volume level to float."""
        volume_map = {
            "low": 0.1,
            "medium": 0.3,
            "high": 0.5
        }
        return volume_map.get(volume_str.lower(), 0.3)
    
    def _get_video_dimensions(self, orientation: str, resolution: Optional[str] = None) -> Dict[str, int]:
        """Get video dimensions based on orientation and resolution."""
        if resolution and "x" in resolution:
            try:
                width, height = map(int, resolution.split("x"))
                return {"width": width, "height": height}
            except:
                pass
        
        # Default dimensions by orientation
        orientation_map = {
            "portrait": {"width": 1080, "height": 1920},
            "landscape": {"width": 1920, "height": 1080}, 
            "square": {"width": 1080, "height": 1080}
        }
        
        return orientation_map.get(orientation, orientation_map["portrait"])