import os
import time
import logging
import uuid
from typing import Dict, Any, List, Optional, Union
from app.services.ai.media_generation_strategy import MediaGenerationStrategy
from app.services.ai.script_generator import script_generator
from app.services.ai.video_search_query_generator import video_search_query_generator
from app.services.audio.tts_service import tts_service as text_to_speech_service
from app.services.video.add_captions import add_captions_service
from app.services.video.concatenate import concatenation_service
from app.services.video.add_audio import add_audio_service
from app.services.s3 import s3_service
from app.config import get_caption_style, apply_caption_style_preset, get_available_caption_styles
from app.services.media.metadata import metadata_service
from app.utils.video.background_video_composer import BackgroundVideoComposer
from app.services.music_service import music_service
from app.utils.video.moviepy_video_composer import MoviePyVideoComposer
from app.services.ai.topic_discovery_service import topic_discovery_service
from app.services.video.add_audio import add_audio_service
from app.services.music_service import music_service
import tempfile

logger = logging.getLogger(__name__)


class UnifiedVideoPipeline:
    """
    Unified pipeline for generating videos from topics using different media sources.
    
    This service orchestrates the entire process:
    1. Generate script from topic using AI
    2. Create TTS audio from script
    3. Generate media search queries from script
    4. Use appropriate strategy to generate media (stock videos, stock images, AI images, AI videos)
    5. Compose videos with timing
    6. Add captions if requested
    7. Final rendering and upload to S3
    
    Supports multiple media generation strategies:
    - Stock videos from Pexels/Pixabay
    - Stock images from Pexels/Pixabay with motion effects
    - AI-generated images from Together.ai/Flux with motion effects
    - AI-generated videos from LTX-Video/WaveSpeed
    """
    
    def __init__(self):
        self.video_composer = BackgroundVideoComposer()  # FFmpeg fallback composer
        self.moviepy_composer = MoviePyVideoComposer()   # Primary MoviePy composer
        
        # Initialize media generation strategies
        self.media_strategies: Dict[str, MediaGenerationStrategy] = {}
        self._register_strategies()
    
    def _register_strategies(self):
        """Register all available media generation strategies."""
        from app.services.ai.strategies.stock_video_strategy import StockVideoStrategy
        from app.services.ai.strategies.stock_image_strategy import StockImageStrategy
        from app.services.ai.strategies.ai_image_strategy import AIImageStrategy
        from app.services.ai.strategies.ai_video_strategy import AIVideoStrategy
        
        self.media_strategies = {
            'stock_video': StockVideoStrategy(),
            'stock_image': StockImageStrategy(),
            'ai_image': AIImageStrategy(),
            'ai_video': AIVideoStrategy()
        }
        
        logger.info(f"Registered {len(self.media_strategies)} media generation strategies")
    
    def _map_language_code_to_name(self, language_code: str) -> str:
        """Map language codes to full language names for script generation."""
        language_mapping = {
            'en': 'english',
            'fr': 'french', 
            'es': 'spanish',
            'de': 'german',
            'it': 'italian',
            'pt': 'portuguese',
            'ru': 'russian',
            'zh': 'chinese',
            'ja': 'japanese',
            'ko': 'korean',
            'ar': 'arabic',
            'hi': 'hindi',
            'th': 'thai',
            'vi': 'vietnamese',
            'pl': 'polish',
            'nl': 'dutch',
            'sv': 'swedish',
            'no': 'norwegian',
            'da': 'danish',
            'fi': 'finnish',
            'tr': 'turkish',
            'he': 'hebrew',
            'cs': 'czech',
            'hu': 'hungarian',
            'ro': 'romanian',
            'sk': 'slovak',
            'sl': 'slovenian',
            'hr': 'croatian',
            'bg': 'bulgarian',
            'et': 'estonian',
            'lv': 'latvian',
            'lt': 'lithuanian',
            'mt': 'maltese',
            'ga': 'irish',
            'cy': 'welsh',
        }
        return language_mapping.get(language_code.lower(), language_code)
    
    def _determine_media_strategy(self, params: Dict[str, Any]) -> str:
        """
        Determine which media generation strategy to use based on parameters.
        
        Args:
            params: Pipeline parameters
            
        Returns:
            Strategy key to use
        """
        footage_provider = params.get('footage_provider', 'pexels')
        media_type = params.get('media_type', 'video')
        
        # Map provider + media_type to strategy
        if footage_provider == 'ai_generated':
            if media_type == 'image':
                return 'ai_image'
            else:  # video
                return 'ai_video'
        else:  # pexels, pixabay
            if media_type == 'image':
                return 'stock_image'
            else:  # video
                return 'stock_video'
    
    async def process_unified_video_generation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process complete unified video generation pipeline.
        
        Args:
            params: Pipeline parameters including topic, voice settings, caption options, etc.
        
        Returns:
            Dictionary containing final video URL and processing metadata
        """
        start_time = time.time()
        
        # Log the media type and provider being used
        media_type = params.get('media_type', 'video')
        footage_provider = params.get('footage_provider', 'pexels')
        strategy_key = self._determine_media_strategy(params)
        
        logger.info(f"Processing unified video generation with media_type: '{media_type}', footage_provider: '{footage_provider}', strategy: '{strategy_key}'")
        
        # Debug: Log all parameters to understand what's being received
        logger.debug(f"All received parameters: {params}")
        logger.debug(f"Language-related parameters: language={params.get('language')}, voice_language={params.get('voice_language')}, tts_language={params.get('tts_language')}, script_language={params.get('script_language')}")
        
        try:
            # Handle video orientation first to ensure dimensions are set
            # Support both 'orientation' (frontend) and 'video_orientation' (backend) parameter names
            orientation = params.get('orientation') or params.get('video_orientation', 'landscape')

            # Map frontend dimension parameters to backend parameters
            if 'image_width' in params:
                params['width'] = params['image_width']
            if 'image_height' in params:
                params['height'] = params['image_height']

            # Set default dimensions based on orientation if not provided
            if orientation == 'portrait':
                params.setdefault('width', 720)
                params.setdefault('height', 1280)
            elif orientation == 'square':
                params.setdefault('width', 720)
                params.setdefault('height', 720)
            else:  # landscape
                params.setdefault('width', 1280)
                params.setdefault('height', 720)

            # Map frontend motion parameters to backend structure
            if 'effect_type' in params or 'zoom_speed' in params or 'pan_direction' in params:
                motion_params = {}
                if 'effect_type' in params:
                    motion_params['effect_type'] = params['effect_type']
                if 'zoom_speed' in params:
                    motion_params['zoom_speed'] = params['zoom_speed']
                if 'pan_direction' in params:
                    motion_params['pan_direction'] = params['pan_direction']
                if 'ken_burns_keypoints' in params:
                    motion_params['ken_burns_keypoints'] = params['ken_burns_keypoints']

                params['motion_params'] = motion_params
                
                # DEBUG: Log motion parameter extraction from frontend
                logger.info(f"=== UNIFIED PIPELINE MOTION PARAMS EXTRACTION ===")
                logger.info(f"Frontend parameters extracted:")
                logger.info(f"  effect_type in params: {'effect_type' in params} -> {params.get('effect_type', 'NOT_FOUND')}")
                logger.info(f"  zoom_speed in params: {'zoom_speed' in params} -> {params.get('zoom_speed', 'NOT_FOUND')}")
                logger.info(f"  pan_direction in params: {'pan_direction' in params} -> {params.get('pan_direction', 'NOT_FOUND')}")
                logger.info(f"  ken_burns_keypoints in params: {'ken_burns_keypoints' in params} -> {params.get('ken_burns_keypoints', 'NOT_FOUND')}")
                logger.info(f"Final motion_params structure: {motion_params}")
                logger.info(f"=== END UNIFIED PIPELINE MOTION PARAMS EXTRACTION ===")
            else:
                logger.warning("=== NO MOTION PARAMETERS DETECTED IN FRONTEND REQUEST ===")
                logger.warning(f"Available params keys: {list(params.keys())}")
                logger.warning("=== END NO MOTION PARAMETERS WARNING ===")
            
            
            # Extract language early for use in topic discovery and script generation
            # Frontend might send: 'language', 'voice_language', 'tts_language', or 'script_language'
            user_language = (
                params.get('language') or 
                params.get('voice_language') or 
                params.get('tts_language') or 
                params.get('script_language') or 
                'en'  # Default to English
            )
            logger.info(f"Using language: {user_language}")
            
            # Step 1: Handle auto-topic discovery if enabled
            if params.get('auto_topic', False) and not params.get('topic'):
                logger.info("Auto-topic discovery enabled, discovering trending topic...")
                script_type = params.get('script_type', 'facts')
                discovered_topic = await topic_discovery_service.discover_topic(script_type, use_trending=True, language=user_language)
                params['topic'] = discovered_topic
                logger.info(f"Discovered trending topic: {discovered_topic}")
            
            # Step 2: Generate script
            if params.get('custom_script'):
                script_content = params['custom_script']
                logger.info("Using provided custom script")
            else:
                topic = params.get('topic')
                auto_topic = params.get('auto_topic', False)
                
                logger.info(f"Topic processing: topic='{topic}', auto_topic={auto_topic}")
                
                if not topic and not auto_topic:
                    raise ValueError("Either 'topic' must be provided or 'auto_topic' must be set to true or 'custom_script' must be provided")
                
                # Handle auto-topic discovery using the same service as footage_to_video_pipeline
                if auto_topic and not topic:
                    logger.info("Auto topic discovery enabled, discovering topic")
                    try:
                        discovered_topic = await topic_discovery_service.discover_topic(
                            script_type=params.get('script_type', 'facts'),
                            language=user_language
                        )
                        # Extract topic string from the returned dict
                        if isinstance(discovered_topic, dict) and 'topic' in discovered_topic:
                            topic = discovered_topic['topic']
                        elif isinstance(discovered_topic, str):
                            topic = discovered_topic
                        else:
                            raise ValueError(f"Invalid topic discovery response: {discovered_topic}")
                        
                        logger.info(f"Auto-discovered topic: '{topic}'")
                        if not topic or not str(topic).strip():
                            raise ValueError("Topic discovery returned empty topic")
                    except Exception as e:
                        logger.error(f"Topic discovery failed: {e}")
                        raise ValueError(f"Auto-topic discovery failed: {str(e)}")
                elif not topic:
                    raise ValueError("No topic provided and auto_topic is not enabled")
                
                logger.info(f"Generating script for topic: {topic}")
                logger.debug(f"Topic length: {len(topic) if topic else 0}")
                
                # Map frontend duration parameter
                duration = params.get('max_duration') or params.get('duration', 60)

                # Ensure topic is always a string before passing to script generator
                if isinstance(topic, dict):
                    if 'topic' in topic:
                        topic = topic['topic']
                    else:
                        raise ValueError(f"Topic dictionary missing 'topic' key: {topic}")
                elif not isinstance(topic, str):
                    topic = str(topic)
                
                # Final validation
                if not topic or not topic.strip():
                    raise ValueError(f"Invalid topic for script generation: '{topic}'")

                logger.info(f"Using language for script generation: {user_language}")
                
                # Map language code to full language name for script generator
                script_language = self._map_language_code_to_name(user_language)
                logger.info(f"Mapped language code '{user_language}' to '{script_language}' for script generation")

                # Use exact parameters that match working footage_to_video_pipeline.py
                script_params = {
                    'topic': topic,  # Use the validated string topic
                    'provider': params.get('script_provider', 'auto'),
                    'script_type': params.get('script_type', 'facts'),
                    'max_duration': params.get('max_duration', 120),
                    'target_words': self._calculate_target_words(params.get('max_duration', 120)),
                    'language': script_language  # Use mapped language name
                }
                
                logger.info(f"Final topic being sent to script generator: '{topic}'")
                logger.debug(f"Script generation parameters: {script_params}")
                script_result = await script_generator.generate_script(script_params)
                script_content = script_result.get('script', '')
                
                if not script_content:
                    raise ValueError("Failed to generate script content")
                
                logger.info(f"Generated script ({len(script_content)} characters)")
            
            # Step 3: Generate TTS audio
            logger.info("Generating TTS audio...")
            # Map frontend voice parameters to backend parameters
            voice_provider = params.get('tts_provider') or params.get('voice_provider', 'kokoro')
            voice_name = params.get('voice') or params.get('voice_name', 'af_sarah')
            
            # Map frontend voice speed parameter
            voice_speed = params.get('tts_speed') or params.get('voice_speed', 1.0)
            
            logger.info(f"Using language code '{user_language}' for TTS")

            audio_result = await text_to_speech_service.generate_speech(
                text=script_content,
                voice=voice_name,
                provider=voice_provider,
                speed=voice_speed,
                lang_code=user_language  # Keep original language code for TTS
            )
            
            # TTS service returns tuple (bytes, filename) not dict
            if isinstance(audio_result, tuple):
                audio_bytes, audio_filename = audio_result
                # Upload to S3 to get URL
                import tempfile
                import os as file_os
                
                with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                    temp_file.write(audio_bytes)
                    temp_audio_path = temp_file.name
                
                try:
                    audio_url = await s3_service.upload_file(temp_audio_path, f"audio/{audio_filename}")
                finally:
                    if file_os.path.exists(temp_audio_path):
                        file_os.unlink(temp_audio_path)
            else:
                audio_url = audio_result.get('audio_url') if isinstance(audio_result, dict) else None
            
            if not audio_url:
                raise ValueError("Failed to generate TTS audio")
                
            logger.info(f"Generated TTS audio: {audio_url}")
            
            # Step 4: Get audio metadata for timing
            logger.info("Getting audio metadata for timing...")
            audio_metadata = await metadata_service.get_metadata(audio_url)
            audio_duration = audio_metadata.get('duration', 60.0)
            logger.info(f"Audio duration: {audio_duration:.2f} seconds")
            
            # Step 5: Generate video search queries
            logger.info("Generating video search queries...")
            query_params = {
                'script': script_content,
                'num_queries': max(3, int(audio_duration / 3)),  # ~3 seconds per segment
                'audio_duration': audio_duration,
                'script_type': params.get('script_type', 'facts'),
                'topic': params.get('topic', ''),
                'target_segments': params.get('target_segments', 5)
            }
            
            queries_result = await video_search_query_generator.generate_video_search_queries(query_params)
            video_queries = queries_result.get('queries', [])
            
            if not video_queries:
                raise ValueError("Failed to generate video search queries")
                
            logger.info(f"Generated {len(video_queries)} video queries")
            
            # Step 6: Use appropriate strategy to generate media
            strategy = self.media_strategies.get(strategy_key)
            if not strategy:
                raise ValueError(f"Unknown media generation strategy: {strategy_key}")
            
            logger.info(f"Using {strategy.get_strategy_name()} for media generation")
            
            background_media = await strategy.generate_media_segments(
                video_queries=video_queries,
                orientation=orientation,
                params=params
            )
            
            # Filter out None results
            valid_media = [media for media in background_media if media is not None]
            
            if not valid_media:
                raise ValueError("No valid background media generated")
                
            logger.info(f"Generated {len(valid_media)} valid media segments")
            
            # Step 7: Create video composition
            logger.info("Creating video composition...")
            composition_params = {
                'background_videos': valid_media,
                'audio_url': audio_url,
                'width': params['width'],
                'height': params['height'],
                'output_format': params.get('output_format', 'mp4'),
                'frame_rate': params.get('frame_rate', 30),
                'video_codec': params.get('video_codec', 'libx264'),
                'audio_codec': params.get('audio_codec', 'aac'),
                'quality': params.get('video_quality', 'high'),
                'fade_duration': params.get('fade_duration', 0.5),
                'crossfade_duration': params.get('crossfade_duration', 0.3)
            }
            
            # Use MoviePy composer (more reliable)
            composed_video_url = await self.moviepy_composer.compose_timed_videos(
                video_segments=valid_media,
                target_duration=audio_duration,
                output_width=composition_params['width'],
                output_height=composition_params['height'],
                frame_rate=composition_params.get('frame_rate', 30)
            )

            if not composed_video_url:
                raise ValueError("Failed to create video composition")

            logger.info(f"Created video composition: {composed_video_url}")

            # Step 8: Add audio to video (crucial step that was missing!)
            logger.info("Adding audio to video...")
            audio_params = {
                'video_url': composed_video_url,
                'audio_url': audio_url,
                'sync_mode': 'replace',
                'match_length': 'audio',
                'video_volume': 0,  # Mute original video audio
                'audio_volume': 100
            }

            audio_result = await add_audio_service.process_job(f"pipeline_audio_{uuid.uuid4().hex[:8]}", audio_params)
            video_with_audio_path = audio_result['url']

            logger.info(f"Video with audio: {video_with_audio_path}")

            # Step 9: Process background music (if requested)
            final_audio_url = audio_url
            background_music_url = None
            background_music = params.get('background_music')

            # Check if we should add background music
            should_add_music = (
                (params.get('background_music') and params.get('background_music') != 'none') or
                (background_music and background_music != '' and background_music != 'none')
            )

            if should_add_music:
                logger.info("Processing background music")
                try:
                    music_result = await self._add_background_music(
                        audio_url,
                        background_music or 'upbeat',  # Provide default if None
                        params.get('background_music_mood', 'upbeat'),
                        params.get('background_music_volume', 0.3),
                        audio_duration
                    )
                    final_audio_url = music_result.get("mixed_audio_url", audio_url) if music_result else audio_url
                    background_music_url = music_result.get("background_music_url") if music_result else None

                    # Re-add audio to video with background music
                    if final_audio_url != audio_url:
                        logger.info("Re-adding audio with background music to video...")
                        audio_params = {
                            'video_url': composed_video_url,  # Use original composed video
                            'audio_url': final_audio_url,
                            'sync_mode': 'replace',
                            'match_length': 'audio',
                            'video_volume': 0,
                            'audio_volume': 100
                        }
                        audio_result = await add_audio_service.process_job(f"pipeline_music_audio_{uuid.uuid4().hex[:8]}", audio_params)
                        video_with_audio_path = audio_result['url']
                        logger.info(f"Video with background music: {video_with_audio_path}")

                except Exception as e:
                    logger.error(f"Failed to add background music, using original audio: {e}")
                    final_audio_url = audio_url
                    background_music_url = None

            # Step 10: Add captions if requested
            final_video_path = video_with_audio_path
            srt_url = None
            
            if params.get('add_captions', False):
                logger.info("Adding captions to video...")
                
                # Build comprehensive caption properties from all available parameters
                caption_properties = params.get('caption_properties') or {}
                
                # Add individual caption parameters to properties if provided
                if params.get('caption_color'):
                    caption_properties['word_color'] = params['caption_color']
                    logger.info(f"Using custom caption color: {params['caption_color']}")
                
                if params.get('caption_position'):
                    position_map = {
                        'top': 'top_center',
                        'center': 'center',
                        'bottom': 'bottom_center'
                    }
                    caption_properties['position'] = position_map.get(params['caption_position'], 'bottom_center')
                    logger.info(f"Using caption position: {caption_properties['position']}")
                
                # Log the final caption style and properties being used
                caption_style = params.get('caption_style', 'viral_bounce')
                logger.info(f"Using caption style: '{caption_style}' with properties: {caption_properties}")
                
                caption_result = await self._add_captions_to_video(
                    video_with_audio_path,
                    script_content,
                    caption_style,
                    params['width'],
                    params['height'],
                    audio_duration,
                    caption_properties if caption_properties else None
                )
                final_video_path = caption_result['video_url']
                srt_url = caption_result.get('srt_url')

            
            # Step 10: Handle final video URL
            # Check if captions were added (final_video_path will be different from video_with_audio_path)
            if final_video_path != video_with_audio_path:
                # Captions were added, caption service already uploaded the video to S3
                # final_video_path is already an S3 URL, use it directly
                logger.info(f"Using captioned video URL: {final_video_path}")
                final_video_url = final_video_path
            else:
                # No captions added, use the video with audio URL directly
                final_video_url = video_with_audio_path

            # Video without captions is already uploaded by MoviePy composer
            video_no_captions_url = video_with_audio_path
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Prepare result
            result = {
                'final_video_url': final_video_url,
                'video_with_audio_url': video_no_captions_url,
                'script_generated': script_content,
                'audio_url': final_audio_url,  # Use final audio URL (with background music if added)
                'original_audio_url': audio_url,  # Keep original for reference
                'background_music_url': background_music_url,
                'video_duration': audio_duration,
                'processing_time': processing_time,
                'word_count': len(script_content.split()),
                'segments_count': len(valid_media),
                'media_strategy_used': strategy.get_strategy_name(),
                'background_media_used': [media.get('download_url', 'N/A') for media in valid_media],
                'orientation': orientation,
                'dimensions': f"{params['width']}x{params['height']}"
            }
            
            # Add caption-specific results if captions were added
            if srt_url:
                result['srt_url'] = srt_url
                result['captions_added'] = True
            else:
                result['captions_added'] = False
            
            logger.info(f"Video generation completed successfully in {processing_time:.2f} seconds")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error in unified video pipeline after {processing_time:.2f}s: {str(e)}", exc_info=True)
            raise
    
    def _format_srt_time(self, seconds: float) -> str:
        """Format time in seconds to SRT time format (HH:MM:SS,mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    async def _add_captions_to_video(self, video_url: str, script_text: str,
                                   caption_style: str, width: int, height: int, estimated_duration: float, caption_properties: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Add captions to the video using the unified caption system with backend presets."""
        try:
            # Start with config-based defaults for the style
            final_caption_properties = self._get_responsive_caption_properties_from_config(caption_style, width, height)
            
            # Apply user overrides if provided (user overrides take precedence)
            if caption_properties:
                logger.info(f"Applying user caption overrides: {caption_properties}")
                final_caption_properties.update(caption_properties)
            
            logger.info(f"Final caption properties for style '{caption_style}': {final_caption_properties}")

            # Use the unified caption service
            caption_params = {
                'video_url': video_url,
                'script_text': script_text,
                'caption_style': caption_style,
                'caption_properties': final_caption_properties,
                'estimated_duration': estimated_duration
            }

            result = await add_captions_service.process_job(f"pipeline_captions_{uuid.uuid4().hex[:8]}", caption_params)

            return {
                'video_url': result['url'],
                'srt_url': result.get('srt_url'),
                'caption_style': caption_style
            }

        except Exception as e:
            logger.error(f"Failed to add captions to video using unified system: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Video URL: {video_url}")
            logger.error(f"Script text length: {len(script_text) if script_text else 0}")
            # Fallback to custom implementation if unified system fails
            logger.warning("Falling back to custom caption implementation")
            # Use the final properties we already computed for the fallback
            fallback_properties = locals().get('final_caption_properties')
            if fallback_properties is None:
                fallback_properties = self._get_responsive_caption_properties_from_config(caption_style, width, height)
            return await self._add_captions_to_video_fallback(video_url, script_text, caption_style, width, height, estimated_duration, fallback_properties)

    def _get_responsive_caption_properties_from_config(self, style: str, width: int, height: int) -> Dict:
        """Get responsive caption properties from configuration with responsive adjustments."""
        try:
            # Get base configuration for the style
            base_config = get_caption_style(style)

            # Calculate responsive values based on video dimensions
            is_portrait = height > width
            is_square = abs(width - height) < 100

            # Base font size calculation (responsive to video dimensions)
            base_font_size = min(width, height) // 15  # Responsive base size
            if is_portrait:
                base_font_size = int(base_font_size * 0.9)  # Slightly smaller for portrait

            # Responsive margins
            margin_bottom = max(80, height // 10)  # At least 80px, or 10% of height
            max_words_per_line = 3 if is_portrait else 4

            # Create responsive properties based on config
            responsive_properties = {
                'style': style,
                'font_size': base_config.get('font_size', base_font_size),
                'line_color': base_config.get('line_color', '#FFFFFF'),
                'word_color': base_config.get('word_color', '#FFFF00'),
                'outline_color': base_config.get('outline_color', 'black'),
                'outline_width': base_config.get('outline_width', max(2, int(base_font_size * 0.1))),
                'position': base_config.get('position', 'bottom_center'),
                'max_words_per_line': base_config.get('max_words_per_line', max_words_per_line),
                'line_spacing': base_config.get('line_spacing', 1.3 if is_portrait else 1.2),
                'margin_bottom': margin_bottom,
                'text_align': base_config.get('text_align', 'center'),
                'all_caps': base_config.get('all_caps', False),
                'font_family': base_config.get('font_family', 'Arial-Bold'),
                'bold': base_config.get('bold', True)
            }

            # Add style-specific properties
            if 'viral' in style or 'bounce' in style:
                responsive_properties.update({
                    'bounce_intensity': 1.5,
                    'animation_speed': 1.2
                })
            elif 'typewriter' in style:
                responsive_properties.update({
                    'typewriter_speed': 3.0
                })
            elif 'fade' in style:
                responsive_properties.update({
                    'animation_speed': 1.0
                })

            return responsive_properties

        except Exception as e:
            logger.warning(f"Failed to load caption style config for '{style}': {e}")
            # Fallback to the original hardcoded responsive properties
            return self._get_responsive_caption_properties(style, width, height)

    def _get_responsive_caption_properties(self, style: str, width: int, height: int) -> Dict:
        """Get responsive caption properties based on video dimensions (fallback method)."""
        is_portrait = height > width

        # Base font size calculation (responsive to video dimensions)
        base_font_size = min(width, height) // 15  # Responsive base size
        if is_portrait:
            base_font_size = int(base_font_size * 0.9)  # Slightly smaller for portrait

        # Responsive margins
        margin_bottom = max(80, height // 10)  # At least 80px, or 10% of height
        max_words_per_line = 3 if is_portrait else 4

        # Outline width relative to font size
        outline_width = max(2, int(base_font_size * 0.1))  # 10% of font size for better visibility

        style_presets = {
            'viral_bounce': {
                'style': 'viral_bounce',
                'font_size': base_font_size,
                'line_color': 'white',
                'word_color': 'yellow',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'center',
                'all_caps': True,
                'bounce_intensity': 1.5,
                'animation_speed': 1.2,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45
            },
            'typewriter': {
                'style': 'typewriter',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'typewriter_speed': 3.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            },
            'fade_in': {
                'style': 'fade_in',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'animation_speed': 1.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            },
            'classic': {
                'style': 'classic',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.1,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            },
            'preferred_default': {
                'style': 'preferred_default',
                'font_size': base_font_size,
                'line_color': 'white',
                'word_color': 'yellow',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'center',
                'all_caps': True,
                'bounce_intensity': 1.5,
                'animation_speed': 1.2,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45
            }
        }

        return style_presets.get(style, style_presets['classic'])

    async def _add_captions_to_video_fallback(self, video_url: str, script_text: str,
                                            caption_style: str, width: int, height: int, estimated_duration: float, responsive_properties: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback method using the original custom caption implementation."""
        try:
            # Create simple SRT content with proper timing
            srt_content = self._create_simple_srt(script_text, estimated_duration)

            # Upload SRT file to S3 as a separate file
            srt_url = await self._upload_srt_file(srt_content)

            # Use simple FFmpeg to burn subtitles with responsive properties
            result_url = await self._burn_simple_subtitles(video_url, srt_content, width, height, responsive_properties)

            return {
                'video_url': result_url,
                'srt_url': srt_url,
                'caption_style': caption_style
            }

        except Exception as e:
            logger.error(f"Fallback caption implementation failed: {e}")
            # Return original video if all caption methods fail
            return {
                'video_url': video_url,
                'srt_url': None,
                'caption_style': caption_style
            }

    def _create_simple_srt(self, script_text: str, duration: float) -> str:
        """Create simple SRT content with proper timing."""
        words = script_text.split()
        words_per_segment = 5
        segment_duration = duration / max(1, len(words) // words_per_segment)

        srt_content = ""
        for i in range(0, len(words), words_per_segment):
            segment_words = words[i:i+words_per_segment]
            start_time = i // words_per_segment * segment_duration
            end_time = min(start_time + segment_duration, duration)

            srt_content += f"{i//words_per_segment + 1}\n"
            srt_content += f"{self._format_srt_time(start_time)} --> {self._format_srt_time(end_time)}\n"
            srt_content += f"{' '.join(segment_words)}\n\n"

        return srt_content

    async def _upload_srt_file(self, srt_content: str) -> str:
        """Upload SRT content to S3 and return URL."""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False) as srt_file:
                srt_file.write(srt_content)
                srt_path = srt_file.name

            try:
                s3_key = f"captions/captions_{uuid.uuid4()}.srt"
                srt_url = await s3_service.upload_file(srt_path, s3_key)
                return srt_url
            finally:
                import os
                if os.path.exists(srt_path):
                    os.unlink(srt_path)

        except Exception as e:
            logger.error(f"Failed to upload SRT file: {e}")
            return ""

    async def _burn_simple_subtitles(self, video_url: str, srt_content: str, width: int, height: int, responsive_properties: Dict[str, Any]) -> str:
        """Burn subtitles into video using FFmpeg with responsive properties."""
        try:
            # This is a simplified implementation - in practice you'd use FFmpeg
            # For now, return the original video URL
            logger.warning("Subtitle burning not implemented in fallback method, returning original video")
            return video_url

        except Exception as e:
            logger.error(f"Failed to burn subtitles: {e}")
            return video_url

    async def _add_background_music(
        self,
        voice_audio_url: str,
        background_music: str,
        mood: str,
        volume: float,
        duration: float
    ) -> Dict[str, Optional[str]]:
        """
        Add background music to voice audio.

        Args:
            voice_audio_url: URL of the voice audio
            background_music: Either 'generate' for AI music or filename of existing track
            mood: Music mood for selection/generation
            volume: Background music volume (0.0 to 1.0)
            duration: Target duration for the audio

        Returns:
            Dictionary with mixed_audio_url and background_music_url
        """
        try:
            logger.info(f"Adding background music: {background_music}, mood: {mood}, volume: {volume}")

            # Download voice audio to temp file
            import aiohttp
            import subprocess

            voice_audio_path = f"/tmp/voice_audio_{uuid.uuid4()}.mp3"
            async with aiohttp.ClientSession() as session:
                async with session.get(voice_audio_url) as response:
                    if response.status == 200:
                        with open(voice_audio_path, 'wb') as f:
                            f.write(await response.read())
                    else:
                        logger.error(f"Failed to download voice audio: {response.status}")
                        return {"mixed_audio_url": voice_audio_url, "background_music_url": None}

            background_music_url = None
            background_music_path = None

            if background_music == 'generate':
                # Use AI-generated music via MusicGen
                try:
                    from app.services.audio.music_generation import music_generation_service

                    logger.info("Generating AI background music")

                    # Generate music prompt based on mood and content
                    music_prompt = f"Instrumental background music with {mood} mood, suitable for video narration, gentle and unobtrusive"

                    music_params = {
                        'description': music_prompt,
                        'duration': min(int(duration), 30),  # Limit to 30s for faster generation
                        'model_size': 'small',  # Use small model for faster generation
                        'output_format': 'wav'
                    }

                    music_result = await music_generation_service.process_music_generation(f"bg_music_{uuid.uuid4().hex[:8]}", music_params)
                    if music_result and music_result.get('audio_url'):
                        background_music_url = music_result['audio_url']
                        # Download generated music
                        background_music_path = f"/tmp/bg_music_{uuid.uuid4()}.wav"
                        async with aiohttp.ClientSession() as session:
                            async with session.get(background_music_url) as response:
                                if response.status == 200:
                                    with open(background_music_path, 'wb') as f:
                                        f.write(await response.read())

                except Exception as e:
                    logger.warning(f"AI music generation failed: {e}, falling back to mood-based track")
                    # Fall back to selecting a track by mood
                    tracks = music_service.get_tracks_by_mood(mood)
                    if tracks:
                        selected_track = tracks[0]  # Use first matching track
                        background_music_path = music_service.get_track_path(selected_track['file'])
                        # Upload the track to S3 for consistent access
                        if background_music_path:
                            s3_key = f"music/{selected_track['file']}"
                            background_music_url = await s3_service.upload_file(background_music_path, s3_key)
                        logger.info(f"Using fallback mood-based track: {selected_track['title']} for mood: {mood}")
                    else:
                        logger.warning(f"No tracks found for mood: {mood}, skipping background music")
                        return {"mixed_audio_url": voice_audio_url, "background_music_url": None}
            else:
                # Check if background_music is a mood or a specific track file
                logger.info(f"Looking for mood-based music: '{background_music}'")
                tracks = music_service.get_tracks_by_mood(background_music)
                logger.info(f"Found {len(tracks)} tracks for mood '{background_music}': {[t['title'] for t in tracks]}")
                if tracks:
                    # It's a mood, use the first matching track
                    selected_track = tracks[0]
                    background_music_path = music_service.get_track_path(selected_track['file'])
                    # Upload the track to S3 for consistent access
                    if background_music_path:
                        s3_key = f"music/{selected_track['file']}"
                        background_music_url = await s3_service.upload_file(background_music_path, s3_key)
                        logger.info(f"Using mood-based track: {selected_track['title']} for mood: {background_music}")
                    else:
                        logger.warning(f"Track file not found: {selected_track['file']}")
                        return {"mixed_audio_url": voice_audio_url, "background_music_url": None}
                else:
                    logger.warning(f"No tracks found for mood: {background_music}, skipping background music")
                    return {"mixed_audio_url": voice_audio_url, "background_music_url": None}

            if not background_music_path or not os.path.exists(background_music_path):
                logger.warning("Background music file not found, skipping music mixing")
                return {"mixed_audio_url": voice_audio_url, "background_music_url": background_music_url}

            # Create output path
            output_path = f"/tmp/audio_with_music_{uuid.uuid4()}.mp3"

            # Mix audio using FFmpeg
            # Use amix filter to combine voice and background music
            cmd = [
                'ffmpeg', '-y',
                '-i', voice_audio_path,
                '-i', background_music_path,
                '-filter_complex',
                f'[0:a]volume=1.0[voice];[1:a]volume={volume}[music];[voice][music]amix=inputs=2:duration=first:dropout_transition=2',
                '-t', str(duration),  # Limit to target duration
                '-c:a', 'libmp3lame',
                '-b:a', '192k',
                output_path
            ]

            logger.info(f"Mixing audio with background music: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"FFmpeg audio mixing failed: {result.stderr}")
                # Return original audio if mixing fails
                return {"mixed_audio_url": voice_audio_url, "background_music_url": None}

            # Upload mixed audio to S3
            s3_key = f"audio/mixed_audio_{uuid.uuid4()}.mp3"
            mixed_audio_url = await s3_service.upload_file(output_path, s3_key)

            # Clean up temp files
            try:
                os.unlink(voice_audio_path)
                os.unlink(output_path)
            except Exception as cleanup_error:
                logger.warning(f"Failed to clean up temp files: {cleanup_error}")

            return {"mixed_audio_url": mixed_audio_url, "background_music_url": background_music_url}

        except Exception as e:
            logger.error(f"Failed to add background music: {e}")
            return {"mixed_audio_url": voice_audio_url, "background_music_url": None}

    def _calculate_target_words(self, max_duration: int) -> int:
        """Calculate target word count based on max duration."""
        # Average speaking rate: ~2.8 words per second
        return int(max_duration * 2.8)
    


# Global instance
unified_video_pipeline = UnifiedVideoPipeline()