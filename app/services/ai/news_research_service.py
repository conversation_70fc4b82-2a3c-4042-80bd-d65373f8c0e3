"""
Enhanced News research service using Google Search and Perplexity APIs for comprehensive news research.
"""
import os
import logging
import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from app.utils.ai_context import get_current_context

logger = logging.getLogger(__name__)


class NewsResearchService:
    """Enhanced service for researching current news and events with comprehensive search capabilities."""
    
    def __init__(self):
        self.google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.google_search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        self.perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
        self.news_api_key = os.getenv('NEWS_API_KEY')
        
        # Use search-enabled Perplexity models for better results
        self.perplexity_search_models = [
            'sonar-pro',
            'sonar-small-online', 
            'sonar-medium-online',
            'sonar'
        ]
        
        # Validate at least one service is available
        available_services = []
        if self.google_api_key and self.google_search_engine_id:
            available_services.append("Google Search")
        if self.perplexity_api_key:
            available_services.append("Perplexity")
        if self.news_api_key:
            available_services.append("NewsAPI")
            
        if not available_services:
            logger.warning("No news research APIs configured. At least one API required for news research.")
        else:
            logger.info(f"News research initialized with: {', '.join(available_services)}")
    
    async def research_topic(self, topic: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Comprehensive research of a topic using multiple sources and enhanced search capabilities.
        
        Args:
            topic: Topic to research
            max_results: Maximum number of news articles to fetch per source
            
        Returns:
            Dictionary containing comprehensive research results from multiple sources
        """
        logger.info(f"Starting comprehensive research for topic: {topic}")
        all_results = {
            'articles': [],
            'summary': '',
            'sources_used': [],
            'research_date': datetime.now().isoformat(),
            'query_used': topic,
            'total_sources': 0
        }
        
        # Try to get results from multiple sources
        google_results = None
        perplexity_results = None
        newsapi_results = None
        
        try:
            # Try Google Search for news articles
            if self.google_api_key and self.google_search_engine_id:
                try:
                    google_results = await self._search_google_news_enhanced(topic, max_results)
                    if google_results and google_results.get('articles'):
                        all_results['articles'].extend(google_results['articles'])
                        all_results['sources_used'].append('Google Search API')
                        logger.info(f"Google Search found {len(google_results['articles'])} articles")
                except Exception as e:
                    logger.warning(f"Google Search failed: {e}")
            
            # Try NewsAPI for additional news coverage
            if self.news_api_key:
                try:
                    newsapi_results = await self._search_newsapi(topic, max_results)
                    if newsapi_results and newsapi_results.get('articles'):
                        all_results['articles'].extend(newsapi_results['articles'])
                        all_results['sources_used'].append('NewsAPI')
                        logger.info(f"NewsAPI found {len(newsapi_results['articles'])} articles")
                except Exception as e:
                    logger.warning(f"NewsAPI failed: {e}")
            
            # Use Perplexity for comprehensive analysis and additional sources
            if self.perplexity_api_key:
                try:
                    perplexity_results = await self._search_perplexity_enhanced(topic)
                    if perplexity_results:
                        # Add Perplexity's comprehensive analysis
                        if perplexity_results.get('articles'):
                            all_results['articles'].extend(perplexity_results['articles'])
                        all_results['sources_used'].append('Perplexity AI')
                        logger.info("Perplexity research completed successfully")
                except Exception as e:
                    logger.warning(f"Perplexity API failed: {e}")
            
            # Generate comprehensive summary from all sources
            all_results['summary'] = await self._generate_comprehensive_summary(
                all_results['articles'], topic, google_results, perplexity_results, newsapi_results
            )
            
            all_results['total_sources'] = len(all_results['sources_used'])
            
            if all_results['articles'] or all_results['summary']:
                logger.info(f"Research completed: {len(all_results['articles'])} articles from {len(all_results['sources_used'])} sources")
                return all_results
            
            # If no results from either source, return informative fallback
            logger.warning("No research results from any source, returning fallback")
            return self._generate_fallback_response(topic)
            
        except Exception as e:
            logger.error(f"Comprehensive research failed: {e}")
            return self._generate_error_response(topic, str(e))
    
    async def _search_google_news_enhanced(self, topic: str, max_results: int) -> Dict[str, Any]:
        """Enhanced Google Search for recent news articles with better targeting."""
        logger.info(f"Starting enhanced Google Search for: {topic}")
        
        # Calculate date range for recent news (last 14 days for better coverage)
        date_restrict = "d14"
        
        # Enhanced search queries - try multiple approaches
        search_queries = [
            f"{topic} news latest",
            f'"{topic}" breaking news',
            f"{topic} recent developments",
            f"{topic} today news"
        ]
        
        all_articles = []
        
        for query in search_queries[:2]:  # Try top 2 queries to avoid rate limits
            try:
                url = "https://www.googleapis.com/customsearch/v1"
                params = {
                    'key': self.google_api_key,
                    'cx': self.google_search_engine_id,
                    'q': query,
                    'num': min(5, max_results),  # Fewer per query, more targeted
                    'dateRestrict': date_restrict,
                    'sort': 'date',
                    # Enhanced site search for major news sources
                    'siteSearch': ('news.google.com OR reuters.com OR bbc.com OR cnn.com OR '
                                 'apnews.com OR npr.org OR nbcnews.com OR abcnews.go.com OR '
                                 'cbsnews.com OR foxnews.com OR bloomberg.com OR wsj.com OR '
                                 'nytimes.com OR washingtonpost.com'),
                    'lr': 'lang_en',
                    'tbm': 'nws'  # News search
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            for item in data.get('items', []):
                                # Enhanced article parsing
                                article = {
                                    'title': item.get('title', '').replace(' - ', ' | '),
                                    'snippet': item.get('snippet', ''),
                                    'link': item.get('link', ''),
                                    'source': item.get('displayLink', '').replace('www.', ''),
                                    'date': self._extract_date_from_item(item),
                                    'search_query': query,
                                    'provider': 'google_search'
                                }
                                
                                # Filter out duplicates and low-quality results
                                if (article['title'] and article['snippet'] and 
                                    len(article['snippet']) > 50 and
                                    not any(existing['link'] == article['link'] for existing in all_articles)):
                                    all_articles.append(article)
                                    
                        else:
                            logger.warning(f"Google Search API returned {response.status} for query: {query}")
                            
            except Exception as e:
                logger.warning(f"Google Search query '{query}' failed: {e}")
                continue
        
        # Sort by date and limit results
        all_articles = sorted(all_articles, key=lambda x: x.get('date', ''), reverse=True)[:max_results]
        
        summary = self._generate_summary_from_articles(all_articles, topic) if all_articles else ""
        
        return {
            'articles': all_articles,
            'summary': summary,
            'source': 'google_search',
            'queries_used': search_queries[:2],
            'total_found': len(all_articles)
        }
    
    async def _search_perplexity_enhanced(self, topic: str) -> Dict[str, Any]:
        """Enhanced Perplexity search using search-enabled models for comprehensive research."""
        logger.info(f"Starting enhanced Perplexity research for: {topic}")
        
        # Try search-enabled models in order of preference
        for model in self.perplexity_search_models:
            try:
                url = "https://api.perplexity.ai/chat/completions"
                
                # Enhanced research prompt for comprehensive analysis
                research_prompt = f"""Conduct comprehensive research on "{topic}". Please provide:

1. **Current Status & Latest Developments**: What's happening with {topic} right now? Include specific recent events, dates, and developments from the past 2 weeks.

2. **Key Facts & Background**: Essential information about {topic} - what it is, key players/organizations involved, and important context.

3. **Recent News & Updates**: Summarize the most important recent news stories and developments.

4. **Multiple Perspectives**: Present different viewpoints or aspects of this topic if relevant.

5. **Sources & References**: Include specific sources, dates, and credible references where possible.

Please provide factual, up-to-date information with specific details and dates. Focus on recent developments and current status."""
                
                headers = {
                    'Authorization': f'Bearer {self.perplexity_api_key}',
                    'Content-Type': 'application/json'
                }
                
                payload = {
                    'model': model,
                    'messages': [
                        {
                            'role': 'system',
                            'content': f"""You are a professional researcher and journalist. Provide comprehensive, factual research with:
- Specific dates and recent developments
- Multiple credible sources
- Clear, structured information
- Current status and latest updates
- Balanced perspectives when relevant

{get_current_context()}"""
                        },
                        {
                            'role': 'user',
                            'content': research_prompt
                        }
                    ],
                    'max_tokens': 2000,  # Increased for comprehensive research
                    'temperature': 0.1,
                    'stream': False,
                    'search_domain_filter': ["news.google.com", "reuters.com", "bbc.com", "cnn.com", "apnews.com", "npr.org"]
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, json=payload) as response:
                        if response.status == 200:
                            data = await response.json()
                            content = data['choices'][0]['message']['content']
                            
                            # Extract search results if available (Perplexity provides these)
                            search_results = data.get('search_results', [])
                            articles = []
                            
                            # Create articles from search results if available
                            if search_results:
                                for result in search_results[:5]:  # Use top 5 search results
                                    article = {
                                        'title': result.get('title', ''),
                                        'snippet': content[:200] + "..." if len(content) > 200 else content,
                                        'content': content,
                                        'link': result.get('url', ''),
                                        'source': self._extract_domain_from_url(result.get('url', '')),
                                        'date': result.get('date', datetime.now().strftime('%Y-%m-%d')),
                                        'provider': 'perplexity',
                                        'search_result': True
                                    }
                                    if article['title'] and article['link']:
                                        articles.append(article)
                            
                            # Always include the comprehensive analysis as the main article
                            comprehensive_article = {
                                'title': f"Comprehensive Research: {topic}",
                                'snippet': content[:300] + "..." if len(content) > 300 else content,
                                'content': content,
                                'source': f'perplexity.ai ({model})',
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'provider': 'perplexity',
                                'model_used': model,
                                'research_type': 'comprehensive_analysis',
                                'link': f'https://perplexity.ai/search?q={topic.replace(" ", "+")}'
                            }
                            articles.insert(0, comprehensive_article)  # Put comprehensive analysis first
                            
                            # Extract citations/sources if mentioned in the content
                            sources = self._extract_sources_from_content(content)
                            if sources:
                                comprehensive_article['cited_sources'] = sources
                            
                            logger.info(f"Perplexity research successful with model: {model}, {len(search_results)} search results")
                            return {
                                'articles': articles,
                                'summary': content,
                                'source': 'perplexity',
                                'model_used': model,
                                'search_results_count': len(search_results),
                                'research_comprehensive': True
                            }
                            
                        elif response.status == 429:
                            logger.warning(f"Rate limit hit for model {model}, trying next model...")
                            continue
                        else:
                            error_text = await response.text()
                            logger.warning(f"Perplexity API error {response.status} for model {model}: {error_text}")
                            continue
                            
            except Exception as e:
                logger.warning(f"Perplexity model {model} failed: {e}")
                continue
        
        raise Exception("All Perplexity models failed or rate limited")
    
    async def _search_newsapi(self, topic: str, max_results: int) -> Dict[str, Any]:
        """Search NewsAPI for current news articles."""
        logger.info(f"Starting NewsAPI search for: {topic}")
        
        try:
            # Calculate date range for recent news (last 7 days)
            from_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            url = "https://newsapi.org/v2/everything"
            params = {
                'apiKey': self.news_api_key,
                'q': topic,
                'from': from_date,
                'sortBy': 'publishedAt',
                'pageSize': min(max_results, 20),  # NewsAPI allows up to 20
                'language': 'en',
                'domains': 'reuters.com,bbc.com,cnn.com,apnews.com,npr.org,nbcnews.com,abcnews.go.com,cbsnews.com,bloomberg.com'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        articles = []
                        for article in data.get('articles', []):
                            # Skip articles without proper content
                            if not article.get('title') or not article.get('description'):
                                continue
                            
                            news_article = {
                                'title': article['title'],
                                'snippet': article.get('description', ''),
                                'content': article.get('content', article.get('description', '')),
                                'link': article.get('url', ''),
                                'source': article.get('source', {}).get('name', 'NewsAPI'),
                                'date': article.get('publishedAt', '')[:10] if article.get('publishedAt') else datetime.now().strftime('%Y-%m-%d'),
                                'author': article.get('author', ''),
                                'provider': 'newsapi'
                            }
                            
                            # Filter out removed/deleted articles
                            if '[Removed]' not in news_article['title'] and news_article['snippet']:
                                articles.append(news_article)
                        
                        # Sort by date
                        articles = sorted(articles, key=lambda x: x.get('date', ''), reverse=True)
                        
                        summary = self._generate_summary_from_articles(articles, topic) if articles else ""
                        
                        logger.info(f"NewsAPI found {len(articles)} articles")
                        return {
                            'articles': articles,
                            'summary': summary,
                            'source': 'newsapi',
                            'total_found': len(articles)
                        }
                        
                    else:
                        error_text = await response.text()
                        logger.warning(f"NewsAPI returned {response.status}: {error_text}")
                        raise Exception(f"NewsAPI error {response.status}: {error_text}")
                        
        except Exception as e:
            logger.warning(f"NewsAPI search failed: {e}")
            raise
    
    def _generate_summary_from_articles(self, articles: List[Dict], topic: str) -> str:
        """Generate a comprehensive summary from multiple articles."""
        if not articles:
            return f"Recent developments about {topic}"
        
        # Enhanced summary generation
        summary_parts = []
        unique_sources = set()
        
        for article in articles[:5]:  # Use top 5 articles
            snippet = article.get('snippet', '')
            content = article.get('content', '')
            source = article.get('source', 'Unknown')
            
            # Use content if available, otherwise snippet
            text = content if content and len(content) > len(snippet) else snippet
            
            if text and len(text.strip()) > 30:
                # Clean up text
                clean_text = text.replace('...', '').replace('\n', ' ').strip()
                if clean_text and clean_text not in summary_parts:
                    summary_parts.append(clean_text)
                    unique_sources.add(source)
        
        if summary_parts:
            # Combine summaries with source attribution
            combined_summary = ' | '.join(summary_parts[:3])  # Top 3 summaries
            source_list = ', '.join(list(unique_sources)[:3])
            return f"{combined_summary}\n\nSources: {source_list}"
        else:
            return f"Latest news and updates about {topic}"
    
    async def _generate_comprehensive_summary(self, articles: List[Dict], topic: str, 
                                            google_results: Optional[Dict] = None, 
                                            perplexity_results: Optional[Dict] = None,
                                            newsapi_results: Optional[Dict] = None) -> str:
        """Generate a comprehensive summary combining all research sources."""
        summary_parts = []
        
        # Add Perplexity comprehensive analysis first (if available)
        if perplexity_results and perplexity_results.get('summary'):
            perplexity_summary = perplexity_results['summary']
            if len(perplexity_summary) > 100:  # Substantial content
                summary_parts.append(f"**Comprehensive Analysis:**\n{perplexity_summary}")
        
        # Add news summaries from different sources
        news_sources = []
        if google_results and google_results.get('articles'):
            google_summary = self._generate_summary_from_articles(google_results['articles'], topic)
            if google_summary and "Recent developments" not in google_summary:
                news_sources.append(f"Google Search: {google_summary}")
        
        if newsapi_results and newsapi_results.get('articles'):
            newsapi_summary = self._generate_summary_from_articles(newsapi_results['articles'], topic)
            if newsapi_summary and "Recent developments" not in newsapi_summary:
                news_sources.append(f"NewsAPI: {newsapi_summary}")
        
        if news_sources:
            summary_parts.append(f"**Latest News:**\n" + "\n\n".join(news_sources))
        
        # Add article count summary
        if articles:
            source_counts = {}
            for article in articles:
                provider = article.get('provider', 'unknown')
                source_counts[provider] = source_counts.get(provider, 0) + 1
            
            count_summary = ", ".join([f"{count} from {provider}" for provider, count in source_counts.items()])
            summary_parts.append(f"**Sources:** {len(articles)} articles total ({count_summary})")
        
        # Combine all summaries
        if summary_parts:
            return "\n\n".join(summary_parts)
        else:
            return f"Comprehensive research on {topic} - Recent developments and analysis from multiple sources."
    
    def _extract_date_from_item(self, item: Dict) -> str:
        """Extract and format date from Google Search item."""
        # Try multiple date fields
        date_fields = ['htmlFormattedUrl', 'formattedUrl', 'snippet']
        
        for field in date_fields:
            if field in item:
                text = item[field]
                # Look for date patterns in the text
                import re
                date_patterns = [
                    r'\d{1,2}/\d{1,2}/\d{4}',  # MM/DD/YYYY
                    r'\d{4}-\d{2}-\d{2}',      # YYYY-MM-DD
                    r'\w+ \d{1,2}, \d{4}'      # Month DD, YYYY
                ]
                
                for pattern in date_patterns:
                    match = re.search(pattern, text)
                    if match:
                        return match.group()
        
        # Fallback to current date
        return datetime.now().strftime('%Y-%m-%d')
    
    def _extract_sources_from_content(self, content: str) -> List[str]:
        """Extract source references from Perplexity content."""
        import re
        sources = []
        
        # Look for URL patterns
        url_pattern = r'https?://[^\s<>"{}|\\^`[\]]+'
        urls = re.findall(url_pattern, content)
        
        # Look for source mentions
        source_patterns = [
            r'according to ([^,\n]+)',
            r'reports ([^,\n]+)',
            r'source: ([^,\n]+)',
            r'via ([^,\n]+)'
        ]
        
        for pattern in source_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            sources.extend(matches)
        
        # Clean and deduplicate
        clean_sources = []
        for source in (urls + sources):
            clean_source = source.strip().rstrip('.,;')
            if clean_source and len(clean_source) > 3 and clean_source not in clean_sources:
                clean_sources.append(clean_source)
        
        return clean_sources[:10]  # Limit to 10 sources
    
    def _extract_domain_from_url(self, url: str) -> str:
        """Extract domain name from URL for display."""
        if not url:
            return 'Unknown'
        
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            # Remove www. prefix
            if domain.startswith('www.'):
                domain = domain[4:]
            return domain or 'Unknown'
        except Exception:
            return 'Unknown'
    
    def _generate_fallback_response(self, topic: str) -> Dict[str, Any]:
        """Generate a helpful fallback response when no APIs work."""
        return {
            'articles': [{
                'title': f"Research Topic: {topic}",
                'snippet': f"Unable to fetch current news about {topic} due to API limitations. Please try again later or check news sources directly.",
                'content': f"This topic requires real-time news research. Our news research APIs are currently unavailable, but you can find current information about '{topic}' by visiting major news websites like BBC, CNN, Reuters, or Google News.",
                'source': 'system_fallback',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'provider': 'fallback'
            }],
            'summary': f"News research for '{topic}' is currently unavailable. Please check major news sources or try again later.",
            'sources_used': ['fallback'],
            'total_sources': 0,
            'research_date': datetime.now().isoformat(),
            'query_used': topic
        }
    
    def _generate_error_response(self, topic: str, error_message: str) -> Dict[str, Any]:
        """Generate an error response with helpful information."""
        return {
            'articles': [{
                'title': f"Research Error: {topic}",
                'snippet': f"An error occurred while researching {topic}: {error_message}",
                'content': f"We encountered an issue while researching '{topic}'. Error: {error_message}. Please try again or contact support if the issue persists.",
                'source': 'system_error',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'provider': 'error'
            }],
            'summary': f"Unable to complete research for '{topic}' due to an error: {error_message}",
            'sources_used': ['error'],
            'total_sources': 0,
            'research_date': datetime.now().isoformat(),
            'query_used': topic,
            'error': error_message
        }
    
    def get_news_keywords(self, topic: str) -> List[str]:
        """Generate keywords for news-related video search."""
        base_keywords = [
            "breaking news", "news report", "journalism", "news anchor",
            "newsroom", "headlines", "current events", "media coverage",
            "press conference", "news bulletin", "live news"
        ]
        
        # Add topic-specific keywords
        topic_words = topic.lower().split()
        extended_keywords = base_keywords + topic_words
        
        return extended_keywords


# Singleton instance
news_research_service = NewsResearchService()