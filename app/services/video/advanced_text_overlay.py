"""
Advanced Text Overlay Service with modern rendering capabilities.

This service provides professional-grade text overlay functionality with:
- Advanced animations (fade, slide, typewriter, bounce, etc.)
- Smart positioning with content analysis and face detection
- Advanced effects (gradients, shadows, glow, outlines)
- GPU acceleration support
- Real-time preview generation
- Template system for complex layouts
- Performance optimizations with caching
"""
import os
import re
import uuid
import json
import time
import logging
import tempfile
import subprocess
from typing import Dict, Any, List, Tuple, Optional, Union
from urllib.parse import urlparse
from datetime import datetime, timedelta
import asyncio
import cv2
import numpy as np

from app.utils.media import download_media_file
from app.services.s3 import s3_service
from app.services.redis_service import redis_service

logger = logging.getLogger(__name__)


class TextRenderingEngine:
    """Advanced text rendering with modern effects and animations."""
    
    def __init__(self):
        self.font_cache = {}
        self.gpu_available = self._check_gpu_availability()
        
    def _check_gpu_availability(self) -> bool:
        """Check if GPU acceleration is available for FFmpeg."""
        try:
            result = subprocess.run(
                ["ffmpeg", "-encoders"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return "h264_nvenc" in result.stdout or "h264_videotoolbox" in result.stdout
        except Exception:
            return False
    
    def get_font_path(self, font_family: str, font_weight: str = "normal") -> str:
        """Get the optimal font path based on family and weight."""
        font_dir = os.path.join(os.path.dirname(__file__), "../../../fonts")
        
        # Font mapping with weights
        font_map = {
            "arial": {
                "normal": "Arial.ttf",
                "bold": "Arial-Bold.ttf"
            },
            "roboto": {
                "normal": "Roboto-Regular.ttf",
                "bold": "Roboto-Bold.ttf"
            },
            "dejavu": {
                "normal": "DejaVuSans.ttf",
                "bold": "DejaVuSans-Bold.ttf"
            },
            "oswald": {
                "normal": "Oswald-VariableFont_wght.ttf",
                "bold": "Oswald-VariableFont_wght.ttf"
            }
        }
        
        family_key = font_family.lower().split("-")[0]
        if family_key in font_map and font_weight.lower() in font_map[family_key]:
            font_file = font_map[family_key][font_weight.lower()]
            font_path = os.path.join(font_dir, font_file)
            if os.path.exists(font_path):
                return font_path
        
        # Fallback to system fonts
        fallbacks = [
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux
            "/Windows/Fonts/arial.ttf"  # Windows
        ]
        
        for fallback in fallbacks:
            if os.path.exists(fallback):
                return fallback
                
        return os.path.join(font_dir, "DejaVuSans.ttf")
    
    def create_animation_filter(self, animation_config: Dict[str, Any]) -> str:
        """Create FFmpeg filter for text animations."""
        anim_type = animation_config.get("type", "none")
        duration = animation_config.get("duration", 0.5)
        delay = animation_config.get("delay", 0.0)
        intensity = animation_config.get("intensity", 1.0)
        easing = animation_config.get("easing", "ease_out")
        
        if anim_type == "none":
            return ""
        
        # Easing function mapping
        easing_map = {
            "linear": "t",
            "ease_in": "t*t",
            "ease_out": "1-(1-t)*(1-t)",
            "ease_in_out": "if(lt(t,0.5),2*t*t,1-2*(1-t)*(1-t))",
            "bounce": "1+2.70158*pow(t-1,3)+1.70158*pow(t-1,2)",
            "elastic": "pow(2,-10*t)*sin((t-0.075)*(2*PI)/0.3)+1"
        }
        
        easing_func = easing_map.get(easing, "t")
        
        # Animation implementations
        if anim_type == "fade_in":
            return f"fade=t=in:st={delay}:d={duration}"
        elif anim_type == "fade_out":
            return f"fade=t=out:st={delay}:d={duration}"
        elif anim_type == "slide_up":
            return f"crop=ih*{intensity}:ih:0:'ih-ih*{easing_func}*between(t,{delay},{delay+duration})'"
        elif anim_type == "slide_down":
            return f"crop=ih*{intensity}:ih:0:'ih*{easing_func}*between(t,{delay},{delay+duration})'"
        elif anim_type == "zoom_in":
            zoom_factor = f"1+{intensity-1}*{easing_func}*between(t,{delay},{delay+duration})"
            return f"scale={zoom_factor}*iw:{zoom_factor}*ih"
        elif anim_type == "typewriter":
            # Typewriter effect using text reveal
            chars_per_sec = 20
            return f"drawtext=text='':enable='between(t,{delay},{delay+duration})'"
        
        return ""
    
    def create_effects_filter(self, effects_config: Dict[str, Any], font_size: int) -> List[str]:
        """Create FFmpeg filters for text effects."""
        filters = []
        
        # Shadow effect
        if effects_config.get("shadow_enabled", False):
            shadow_color = effects_config.get("shadow_color", "black")
            shadow_x = effects_config.get("shadow_offset_x", 3)
            shadow_y = effects_config.get("shadow_offset_y", 3)
            shadow_blur = effects_config.get("shadow_blur", 2.0)
            
            filters.append(f"boxblur={shadow_blur}:{shadow_blur}")
            
        # Glow effect
        if effects_config.get("glow_enabled", False):
            glow_color = effects_config.get("glow_color", "white")
            glow_intensity = effects_config.get("glow_intensity", 1.0)
            
            glow_size = int(font_size * 0.1 * glow_intensity)
            filters.append(f"boxblur={glow_size}:{glow_size}")
        
        return filters
    
    def escape_text_for_ffmpeg(self, text: str) -> str:
        """Enhanced text escaping for FFmpeg with Unicode support."""
        # Preserve Unicode characters but escape FFmpeg special characters
        text = text.replace("\\", "\\\\\\\\")
        text = text.replace(":", "\\\\:")
        text = text.replace("'", "\\\\'")
        text = text.replace("[", "\\\\[")
        text = text.replace("]", "\\\\]")
        text = text.replace(",", "\\\\,")
        text = text.replace(";", "\\\\;")
        text = text.replace("=", "\\\\=")
        
        return text


class ContentAnalyzer:
    """Content analysis for optimal text positioning."""
    
    def __init__(self):
        self.face_cascade = None
        self._load_face_detector()
    
    def _load_face_detector(self):
        """Load OpenCV face detection cascade."""
        try:
            # Try multiple approaches to locate the cascade file
            cascade_paths = [
                # Try the standard data directory (suppressing type error)
                getattr(getattr(cv2, 'data', None), 'haarcascades', '') + 'haarcascade_frontalface_default.xml',
                # Common system locations
                '/usr/share/opencv4/haarcascades/haarcascade_frontalface_default.xml',
                '/usr/local/share/opencv4/haarcascades/haarcascade_frontalface_default.xml',
                # Relative path
                'haarcascade_frontalface_default.xml'
            ]
            
            self.face_cascade = None
            for cascade_path in cascade_paths:
                if cascade_path and os.path.exists(cascade_path):
                    self.face_cascade = cv2.CascadeClassifier(cascade_path)
                    if not self.face_cascade.empty():
                        logger.info(f"Face detection loaded successfully from: {cascade_path}")
                        break
                elif cascade_path:  # Try loading even if path doesn't exist (cv2 may find it internally)
                    try:
                        self.face_cascade = cv2.CascadeClassifier(cascade_path)
                        if not self.face_cascade.empty():
                            logger.info(f"Face detection loaded successfully from: {cascade_path}")
                            break
                    except Exception:
                        continue
            
            if self.face_cascade is None or self.face_cascade.empty():
                logger.warning("Face cascade could not be loaded - face detection disabled")
                self.face_cascade = None
                
        except Exception as e:
            logger.warning(f"Could not load face detection: {e}")
            self.face_cascade = None
    
    async def analyze_video_content(
        self,
        video_path: str,
        sample_count: int = 5,
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """Analyze video content for optimal text positioning."""
        analysis_result = {
            "faces_detected": [],
            "content_regions": [],
            "recommended_positions": [],
            "confidence_score": 0.0
        }
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return analysis_result
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            frame_indices = np.linspace(0, total_frames-1, sample_count, dtype=int)
            
            all_faces = []
            frame = None  # Initialize frame variable
            
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, current_frame = cap.read()
                
                if not ret:
                    continue
                
                # Keep reference to the last valid frame for dimension calculation
                frame = current_frame
                
                # Face detection
                if self.face_cascade is not None:
                    gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
                    faces = self.face_cascade.detectMultiScale(
                        gray, 
                        scaleFactor=1.1, 
                        minNeighbors=5,
                        minSize=(30, 30)
                    )
                    
                    for (x, y, w, h) in faces:
                        all_faces.append({
                            "x": int(x),
                            "y": int(y),
                            "width": int(w),
                            "height": int(h),
                            "confidence": 0.8,  # Haar cascades don't provide confidence
                            "frame": frame_idx
                        })
            
            cap.release()
            
            # Process face detections
            analysis_result["faces_detected"] = all_faces
            
            # Generate positioning recommendations  
            if frame is not None:
                height, width = frame.shape[:2]
            else:
                # Default dimensions if no frames were processed
                height, width = 1080, 1920
            safe_positions = self._get_safe_positions(all_faces, width, height)
            analysis_result["recommended_positions"] = safe_positions
            analysis_result["confidence_score"] = min(1.0, len(all_faces) * 0.2 + 0.5)
            
        except Exception as e:
            logger.error(f"Content analysis failed: {e}")
        
        return analysis_result
    
    def _get_safe_positions(self, faces: List[Dict], width: int, height: int) -> List[str]:
        """Calculate safe positions that avoid detected faces."""
        # Define position zones
        zones = {
            "top-left": (0, 0, width//3, height//3),
            "top-center": (width//3, 0, 2*width//3, height//3),
            "top-right": (2*width//3, 0, width, height//3),
            "center-left": (0, height//3, width//3, 2*height//3),
            "center": (width//3, height//3, 2*width//3, 2*height//3),
            "center-right": (2*width//3, height//3, width, 2*height//3),
            "bottom-left": (0, 2*height//3, width//3, height),
            "bottom-center": (width//3, 2*height//3, 2*width//3, height),
            "bottom-right": (2*width//3, 2*height//3, width, height)
        }
        
        safe_positions = []
        
        for position, (x1, y1, x2, y2) in zones.items():
            is_safe = True
            
            for face in faces:
                face_x = face["x"]
                face_y = face["y"]
                face_w = face["width"]
                face_h = face["height"]
                
                # Check overlap with face
                if not (face_x + face_w < x1 or face_x > x2 or 
                       face_y + face_h < y1 or face_y > y2):
                    is_safe = False
                    break
            
            if is_safe:
                safe_positions.append(position)
        
        # Default safe positions if no specific safe zones found
        if not safe_positions:
            safe_positions = ["bottom-center", "top-center", "bottom-left", "bottom-right"]
        
        return safe_positions


class PreviewGenerator:
    """Generate real-time previews for text overlays."""
    
    def __init__(self):
        self.cache_duration = 3600  # 1 hour
    
    async def generate_preview(
        self,
        video_path: str,
        text: str,
        options: Dict[str, Any],
        preview_type: str = "image",
        timestamp: Optional[float] = None,
        duration: float = 3.0
    ) -> Dict[str, Any]:
        """Generate preview of text overlay."""
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(video_path, text, options, preview_type, timestamp)
            
            # Check cache first
            cached_preview = await redis_service.get(f"text_overlay_preview:{cache_key}")
            if cached_preview:
                return json.loads(cached_preview)
            
            # Generate new preview
            preview_result = await self._create_preview(
                video_path, text, options, preview_type, timestamp, duration
            )
            
            # Cache the result
            await redis_service.set(
                f"text_overlay_preview:{cache_key}",
                json.dumps(preview_result),
                expire=self.cache_duration
            )
            
            return preview_result
            
        except Exception as e:
            logger.error(f"Preview generation failed: {e}")
            raise Exception(f"Preview generation failed: {e}")
    
    async def _create_preview(
        self,
        video_path: str,
        text: str,
        options: Dict[str, Any],
        preview_type: str,
        timestamp: Optional[float],
        duration: float
    ) -> Dict[str, Any]:
        """Create the actual preview."""
        temp_dir = tempfile.mkdtemp()
        request_id = str(uuid.uuid4())
        
        try:
            # Get video info
            probe_cmd = [
                "ffprobe", "-v", "quiet", "-print_format", "json",
                "-show_format", "-show_streams", video_path
            ]
            probe_result = subprocess.run(probe_cmd, capture_output=True, text=True)
            video_info = json.loads(probe_result.stdout)
            video_duration = float(video_info["format"]["duration"])
            
            # Determine timestamp
            if timestamp is None:
                timestamp = video_duration * 0.3  # 30% into video
            
            # Create simplified drawtext filter for preview
            font_size = options.get("style", {}).get("font_size", 48)
            font_color = options.get("style", {}).get("text_color", "white")
            position = options.get("position", {}).get("preset", "bottom-center")
            
            # Generate preview based on type
            if preview_type == "image":
                output_path = os.path.join(temp_dir, f"{request_id}_preview.jpg")
                preview_url = await self._generate_image_preview(
                    video_path, text, output_path, timestamp, font_size, font_color, position
                )
            elif preview_type == "video_clip":
                output_path = os.path.join(temp_dir, f"{request_id}_preview.mp4")
                preview_url = await self._generate_video_preview(
                    video_path, text, output_path, timestamp, duration, font_size, font_color, position
                )
            elif preview_type == "gif":
                output_path = os.path.join(temp_dir, f"{request_id}_preview.gif")
                preview_url = await self._generate_gif_preview(
                    video_path, text, output_path, timestamp, duration, font_size, font_color, position
                )
            else:
                raise ValueError(f"Unsupported preview type: {preview_type}")
            
            expires_at = (datetime.now() + timedelta(seconds=self.cache_duration)).isoformat()
            
            return {
                "preview_url": preview_url,
                "preview_type": preview_type,
                "preview_duration": duration if preview_type != "image" else None,
                "cache_key": self._generate_cache_key(video_path, text, options, preview_type, timestamp),
                "expires_at": expires_at
            }
            
        finally:
            # Cleanup temp files
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
    
    async def _generate_image_preview(
        self, video_path: str, text: str, output_path: str,
        timestamp: float, font_size: int, font_color: str, position: str
    ) -> str:
        """Generate image preview."""
        # Simple drawtext filter for fast preview
        filter_complex = f"drawtext=text='{text}':fontsize={font_size}:fontcolor={font_color}:x=(w-text_w)/2:y=h-text_h-50"
        
        cmd = [
            "ffmpeg", "-ss", str(timestamp), "-i", video_path,
            "-vf", filter_complex,
            "-frames:v", "1", "-y", output_path
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        
        # Upload to S3
        object_name = f"text-overlay-previews/{os.path.basename(output_path)}"
        return await s3_service.upload_file(output_path, object_name)
    
    async def _generate_video_preview(
        self, video_path: str, text: str, output_path: str,
        timestamp: float, duration: float, font_size: int, font_color: str, position: str
    ) -> str:
        """Generate video clip preview."""
        filter_complex = f"drawtext=text='{text}':fontsize={font_size}:fontcolor={font_color}:x=(w-text_w)/2:y=h-text_h-50"
        
        cmd = [
            "ffmpeg", "-ss", str(timestamp), "-t", str(duration), "-i", video_path,
            "-vf", filter_complex,
            "-c:v", "libx264", "-preset", "ultrafast", "-crf", "28",
            "-y", output_path
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        
        # Upload to S3
        object_name = f"text-overlay-previews/{os.path.basename(output_path)}"
        return await s3_service.upload_file(output_path, object_name)
    
    async def _generate_gif_preview(
        self, video_path: str, text: str, output_path: str,
        timestamp: float, duration: float, font_size: int, font_color: str, position: str
    ) -> str:
        """Generate GIF preview."""
        filter_complex = f"drawtext=text='{text}':fontsize={font_size}:fontcolor={font_color}:x=(w-text_w)/2:y=h-text_h-50,fps=10,scale=480:-1:flags=lanczos"
        
        cmd = [
            "ffmpeg", "-ss", str(timestamp), "-t", str(duration), "-i", video_path,
            "-vf", filter_complex,
            "-y", output_path
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        
        # Upload to S3
        object_name = f"text-overlay-previews/{os.path.basename(output_path)}"
        return await s3_service.upload_file(output_path, object_name)
    
    def _generate_cache_key(
        self, video_path: str, text: str, options: Dict[str, Any], 
        preview_type: str, timestamp: Optional[float]
    ) -> str:
        """Generate cache key for preview."""
        key_data = {
            "video": os.path.basename(video_path),
            "text": text[:100],  # Truncate for key size
            "options": json.dumps(options, sort_keys=True)[:200],
            "type": preview_type,
            "timestamp": timestamp
        }
        return str(hash(json.dumps(key_data, sort_keys=True)))


class TemplateManager:
    """Manage complex text overlay templates."""
    
    def __init__(self):
        self.templates = {
            "social_media_viral": {
                "description": "Viral social media template with bounce animations",
                "elements": [
                    {
                        "type": "main_text",
                        "position": "center",
                        "animation": {"type": "bounce", "duration": 0.6},
                        "style": {"font_size": 72, "text_color": "#FFFF00"},
                        "effects": {"outline_enabled": True, "outline_width": 3}
                    }
                ]
            },
            "news_ticker": {
                "description": "News ticker with scrolling text",
                "elements": [
                    {
                        "type": "ticker",
                        "position": "bottom-center",
                        "animation": {"type": "slide_left", "duration": 10.0, "loop": True},
                        "style": {"font_size": 36, "text_color": "white"},
                        "background": {"enabled": True, "color": "#CC0000", "opacity": 0.9}
                    }
                ]
            },
            "tutorial_highlight": {
                "description": "Tutorial template with step highlighting",
                "elements": [
                    {
                        "type": "step_number",
                        "position": "top-left",
                        "style": {"font_size": 48, "text_color": "#FF6600"},
                        "background": {"enabled": True, "type": "bubble"}
                    },
                    {
                        "type": "instruction",
                        "position": "bottom-center",
                        "animation": {"type": "fade_in", "duration": 0.8},
                        "style": {"font_size": 42, "text_color": "white"}
                    }
                ]
            }
        }
    
    def get_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """Get template configuration."""
        return self.templates.get(template_name)
    
    def list_templates(self) -> Dict[str, str]:
        """List available templates with descriptions."""
        return {
            name: template["description"] 
            for name, template in self.templates.items()
        }


class AdvancedTextOverlayService:
    """Advanced text overlay service with modern features."""
    
    def __init__(self):
        self.text_renderer = TextRenderingEngine()
        self.content_analyzer = ContentAnalyzer()
        self.preview_generator = PreviewGenerator()
        self.template_manager = TemplateManager()
        
        # Load modern presets from config
        self.modern_presets = self._load_modern_presets()
        
        logger.info("Advanced text overlay service initialized")
    
    def _load_modern_presets(self) -> Dict[str, Any]:
        """Load modern presets from configuration."""
        config_path = os.path.join(
            os.path.dirname(__file__), 
            "../../config/caption_styles.json"
        )
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return config_data.get("optimal_caption_parameters_2025", {})
        except Exception as e:
            logger.warning(f"Could not load modern presets: {e}")
            return {}
    
    async def analyze_content(
        self, 
        video_url: str,
        text: str,
        analysis_type: str = "full",
        sample_count: int = 5,
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """Analyze video content for optimal positioning."""
        try:
            # Download video for analysis
            input_path, _ = await download_media_file(video_url)
            
            # Perform content analysis
            analysis_result = await self.content_analyzer.analyze_video_content(
                input_path, sample_count, confidence_threshold
            )
            
            # Clean up
            if os.path.exists(input_path):
                os.remove(input_path)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Content analysis failed: {e}")
            raise Exception(f"Content analysis failed: {e}")
    
    async def generate_preview(
        self,
        video_url: str,
        text: str,
        options: Dict[str, Any],
        preview_type: str = "image",
        timestamp: Optional[float] = None,
        duration: float = 3.0
    ) -> Dict[str, Any]:
        """Generate preview of text overlay."""
        try:
            # Download video for preview
            input_path, _ = await download_media_file(video_url)
            
            # Generate preview
            preview_result = await self.preview_generator.generate_preview(
                input_path, text, options, preview_type, timestamp, duration
            )
            
            # Clean up
            if os.path.exists(input_path):
                os.remove(input_path)
            
            return preview_result
            
        except Exception as e:
            logger.error(f"Preview generation failed: {e}")
            raise Exception(f"Preview generation failed: {e}")
    
    def _build_advanced_drawtext_filter(
        self, 
        text: str, 
        options: Dict[str, Any],
        video_width: int = 1920,
        video_height: int = 1080
    ) -> str:
        """Build advanced drawtext filter with all modern features."""
        # Extract styling options with defaults
        style = options.get("style", {})
        position = options.get("position", {})
        effects = options.get("effects", {})
        background = options.get("background", {})
        animation = options.get("animation", {})
        layout = options.get("layout", {})
        
        # Basic text properties
        font_path = self.text_renderer.get_font_path(
            style.get("font_family", "Arial-Bold"),
            style.get("font_weight", "bold")
        )
        font_size = style.get("font_size", 48)
        text_color = style.get("text_color", "white")
        
        # Text transformation
        text_transform = style.get("text_transform", "none")
        if text_transform == "uppercase":
            text = text.upper()
        elif text_transform == "lowercase":
            text = text.lower()
        elif text_transform == "capitalize":
            text = text.title()
        
        # Text wrapping
        if layout.get("auto_wrap", True):
            max_chars = layout.get("max_chars_per_line", 25)
            text = self._wrap_text_intelligent(text, max_chars)
        
        # Escape text for FFmpeg
        escaped_text = self.text_renderer.escape_text_for_ffmpeg(text)
        
        # Position calculation
        pos_preset = position.get("preset", "bottom-center")
        x_pos, y_pos = self._calculate_position(
            pos_preset, 
            position.get("x_offset", 0),
            position.get("y_offset", 50),
            position.get("margin_left", 80),
            position.get("margin_right", 80),
            position.get("margin_top", 150),
            position.get("margin_bottom", 180)
        )
        
        # Build base drawtext parameters
        drawtext_params = [
            f"fontfile='{font_path}'",
            f"text='{escaped_text}'",
            f"fontcolor={text_color}",
            f"fontsize={font_size}",
            f"x={x_pos}",
            f"y={y_pos}"
        ]
        
        # Text alignment
        text_align = style.get("text_align", "center")
        if text_align != "left":
            drawtext_params.append(f"text_align={text_align}")
        
        # Line height
        line_height = style.get("line_height", 1.2)
        if line_height != 1.0:
            line_spacing = int(font_size * (line_height - 1))
            drawtext_params.append(f"line_spacing={line_spacing}")
        
        # Letter and word spacing
        letter_spacing = style.get("letter_spacing", 0.0)
        if letter_spacing != 0.0:
            drawtext_params.append(f"letter_spacing={letter_spacing}")
        
        # Outline/stroke
        if effects.get("outline_enabled", True):
            outline_color = effects.get("outline_color", "black")
            outline_width = effects.get("outline_width", 2.0)
            drawtext_params.extend([
                f"bordercolor={outline_color}",
                f"borderw={outline_width}"
            ])
        
        # Background box
        if background.get("enabled", True):
            bg_color = background.get("color", "black")
            bg_opacity = background.get("opacity", 0.8)
            bg_padding = background.get("padding", 20)
            
            drawtext_params.extend([
                "box=1",
                f"boxcolor={bg_color}@{bg_opacity}",
                f"boxborderw={bg_padding}"
            ])
            
            # Border radius (if supported)
            border_radius = background.get("border_radius", 0)
            if border_radius > 0:
                # Note: FFmpeg drawtext doesn't support border radius directly
                # This would require additional processing
                pass
        
        # Shadow effect
        if effects.get("shadow_enabled", False):
            shadow_x = effects.get("shadow_offset_x", 3)
            shadow_y = effects.get("shadow_offset_y", 3)
            drawtext_params.extend([
                f"shadowcolor={effects.get('shadow_color', 'black')}",
                f"shadowx={shadow_x}",
                f"shadowy={shadow_y}"
            ])
        
        # Timing
        start_time = options.get("start_time", 0.0)
        duration = options.get("duration", 5.0)
        end_time = options.get("end_time")
        
        if end_time is not None:
            duration = end_time - start_time
        
        drawtext_params.append(f"enable='between(t,{start_time},{start_time + duration})'")
        
        return "drawtext=" + ":".join(drawtext_params)
    
    def _calculate_position(
        self, preset: str, x_offset: int, y_offset: int,
        margin_left: int, margin_right: int, margin_top: int, margin_bottom: int
    ) -> Tuple[str, str]:
        """Calculate position coordinates with safe areas."""
        position_map = {
            'top-left': (f'{margin_left + x_offset}', f'{margin_top + y_offset}'),
            'top-center': (f'(w-text_w)/2+{x_offset}', f'{margin_top + y_offset}'),
            'top-right': (f'w-text_w-{margin_right + x_offset}', f'{margin_top + y_offset}'),
            'center-left': (f'{margin_left + x_offset}', f'(h-text_h)/2+{y_offset}'),
            'center': (f'(w-text_w)/2+{x_offset}', f'(h-text_h)/2+{y_offset}'),
            'center-right': (f'w-text_w-{margin_right + x_offset}', f'(h-text_h)/2+{y_offset}'),
            'bottom-left': (f'{margin_left + x_offset}', f'h-text_h-{margin_bottom + y_offset}'),
            'bottom-center': (f'(w-text_w)/2+{x_offset}', f'h-text_h-{margin_bottom + y_offset}'),
            'bottom-right': (f'w-text_w-{margin_right + x_offset}', f'h-text_h-{margin_bottom + y_offset}')
        }
        
        return position_map.get(preset, position_map['bottom-center'])
    
    def _wrap_text_intelligent(self, text: str, max_chars: int) -> str:
        """Intelligent text wrapping with word boundary respect."""
        words = text.split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            word_length = len(word)
            
            # Check if adding this word would exceed the limit
            if current_line and current_length + word_length + 1 > max_chars:
                lines.append(' '.join(current_line))
                current_line = [word]
                current_length = word_length
            else:
                current_line.append(word)
                current_length += word_length + (1 if current_line else 0)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return '\\n'.join(lines)
    
    async def add_advanced_text_overlay(
        self,
        video_url: str,
        text: str,
        options: Dict[str, Any],
        quality_preset: str = "high",
        gpu_acceleration: bool = True
    ) -> Dict[str, Any]:
        """Add advanced text overlay with modern features."""
        start_time = time.time()
        logger.info(f"Starting advanced text overlay: {video_url}")
        
        request_id = str(uuid.uuid4())
        input_path = None
        output_path = None
        
        try:
            # Download video
            input_path, file_ext = await download_media_file(video_url)
            
            # Get video properties
            video_info = await self._get_video_info(input_path)
            video_width = video_info.get("width", 1920)
            video_height = video_info.get("height", 1080)
            
            # Build advanced filter
            drawtext_filter = self._build_advanced_drawtext_filter(
                text, options, video_width, video_height
            )
            
            # Setup output
            output_filename = f"{request_id}_advanced_overlay.mp4"
            output_path = os.path.join(tempfile.gettempdir(), output_filename)
            
            # Build FFmpeg command with quality settings
            ffmpeg_cmd = self._build_ffmpeg_command(
                input_path, output_path, drawtext_filter, 
                quality_preset, gpu_acceleration
            )
            
            # Execute FFmpeg
            env = os.environ.copy()
            env.update({
                'LC_ALL': 'C.UTF-8',
                'LANG': 'C.UTF-8',
                'PYTHONIOENCODING': 'utf-8'
            })
            
            result = subprocess.run(
                ffmpeg_cmd,
                check=True,
                capture_output=True,
                text=True,
                env=env,
                timeout=300  # 5 minute timeout
            )
            
            # Upload result
            object_name = f"text-overlay-advanced/{output_filename}"
            video_url_result = await s3_service.upload_file(output_path, object_name)
            
            # Get output video duration
            output_duration = await self._get_video_duration(output_path)
            
            processing_time = time.time() - start_time
            
            # Build result
            result_data = {
                "video_url": video_url_result,
                "duration": output_duration,
                "processing_time": processing_time,
                "effects_applied": self._get_effects_applied(options),
                "animations_applied": self._get_animations_applied(options),
                "metadata": {
                    "quality_preset": quality_preset,
                    "gpu_acceleration": gpu_acceleration and self.text_renderer.gpu_available,
                    "video_resolution": f"{video_width}x{video_height}",
                    "text_length": len(text)
                }
            }
            
            logger.info(f"Advanced text overlay completed in {processing_time:.2f}s")
            return result_data
            
        except subprocess.CalledProcessError as e:
            error_msg = f"FFmpeg failed: {e.stderr}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            logger.error(f"Advanced text overlay failed: {e}")
            raise Exception(f"Advanced text overlay failed: {e}")
        finally:
            # Cleanup
            for temp_file in [input_path, output_path]:
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except Exception as e:
                        logger.warning(f"Failed to delete {temp_file}: {e}")
    
    def _build_ffmpeg_command(
        self,
        input_path: str,
        output_path: str,
        drawtext_filter: str,
        quality_preset: str,
        gpu_acceleration: bool
    ) -> List[str]:
        """Build optimized FFmpeg command."""
        cmd = ["ffmpeg", "-i", input_path]
        
        # Video filter
        cmd.extend(["-vf", drawtext_filter])
        
        # Quality and encoding settings
        if gpu_acceleration and self.text_renderer.gpu_available:
            # GPU acceleration
            cmd.extend(["-c:v", "h264_nvenc"])
            if quality_preset == "ultra":
                cmd.extend(["-preset", "slow", "-cq", "18"])
            elif quality_preset == "high":
                cmd.extend(["-preset", "medium", "-cq", "23"])
            elif quality_preset == "standard":
                cmd.extend(["-preset", "fast", "-cq", "28"])
            else:  # draft
                cmd.extend(["-preset", "ultrafast", "-cq", "35"])
        else:
            # CPU encoding
            cmd.extend(["-c:v", "libx264"])
            if quality_preset == "ultra":
                cmd.extend(["-preset", "slower", "-crf", "18"])
            elif quality_preset == "high":
                cmd.extend(["-preset", "medium", "-crf", "23"])
            elif quality_preset == "standard":
                cmd.extend(["-preset", "fast", "-crf", "28"])
            else:  # draft
                cmd.extend(["-preset", "ultrafast", "-crf", "35"])
        
        # Audio settings
        cmd.extend(["-c:a", "copy"])  # Copy audio without re-encoding
        
        # Output
        cmd.extend(["-y", output_path])
        
        return cmd
    
    async def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Get video information using ffprobe."""
        cmd = [
            "ffprobe", "-v", "quiet", "-print_format", "json",
            "-show_format", "-show_streams", video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            return {}
        
        data = json.loads(result.stdout)
        video_stream = next(
            (s for s in data.get("streams", []) if s.get("codec_type") == "video"),
            {}
        )
        
        return {
            "width": video_stream.get("width", 1920),
            "height": video_stream.get("height", 1080),
            "duration": float(data.get("format", {}).get("duration", 0)),
            "fps": eval(video_stream.get("r_frame_rate", "30/1"))
        }
    
    async def _get_video_duration(self, video_path: str) -> float:
        """Get video duration."""
        cmd = [
            "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
            "-of", "csv=p=0", video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        return float(result.stdout.strip()) if result.stdout.strip() else 0.0
    
    def _get_effects_applied(self, options: Dict[str, Any]) -> List[str]:
        """Extract list of effects that were applied."""
        effects = []
        effects_config = options.get("effects", {})
        
        if effects_config.get("shadow_enabled"):
            effects.append("shadow")
        if effects_config.get("outline_enabled", True):
            effects.append("outline")
        if effects_config.get("glow_enabled"):
            effects.append("glow")
        if effects_config.get("gradient_enabled"):
            effects.append("gradient")
        
        background_config = options.get("background", {})
        if background_config.get("enabled", True):
            effects.append("background_box")
        
        return effects
    
    def _get_animations_applied(self, options: Dict[str, Any]) -> List[str]:
        """Extract list of animations that were applied."""
        animations = []
        animation_config = options.get("animation", {})
        
        anim_type = animation_config.get("type", "none")
        if anim_type != "none":
            animations.append(anim_type)
        
        return animations
    
    async def process_advanced_text_overlay_job(
        self, job_id: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process advanced text overlay job."""
        try:
            video_url = data.get('video_url')
            text = data.get('text')
            options = data.get('options', {})
            quality_preset = data.get('quality_preset', 'high')
            gpu_acceleration = data.get('gpu_acceleration', True)
            enable_content_analysis = data.get('enable_content_analysis', False)
            generate_preview = data.get('generate_preview', False)
            
            if not video_url:
                raise ValueError("video_url is required")
            if not text:
                raise ValueError("text is required")
            
            logger.info(f"Processing advanced text overlay job {job_id}")
            
            result_data = {}
            
            # Content analysis if requested
            if enable_content_analysis:
                analysis = await self.analyze_content(video_url, text)
                result_data["analysis"] = analysis
                
                # Use analysis results to optimize positioning
                if analysis.get("recommended_positions"):
                    recommended_pos = analysis["recommended_positions"][0]
                    if "position" not in options:
                        options["position"] = {}
                    if "preset" not in options["position"]:
                        options["position"]["preset"] = recommended_pos
            
            # Preview generation if requested
            if generate_preview:
                preview = await self.generate_preview(
                    video_url, text, options, "image"
                )
                result_data["preview"] = preview
            
            # Process the main text overlay
            overlay_result = await self.add_advanced_text_overlay(
                video_url, text, options, quality_preset, gpu_acceleration
            )
            
            # Merge results
            result_data.update(overlay_result)
            
            logger.info(f"Advanced text overlay job {job_id} completed successfully")
            return result_data
            
        except Exception as e:
            logger.error(f"Advanced text overlay job {job_id} failed: {e}")
            raise Exception(f"Advanced text overlay failed: {e}")


# Create singleton instance
advanced_text_overlay_service = AdvancedTextOverlayService()