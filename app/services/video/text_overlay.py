"""
Modern Text Overlay Service with advanced features, animations, and smart positioning.
Enhanced for professional video editing capabilities with CPU optimization.
"""
import os
import re
import uuid
import json
import hashlib
import logging
import tempfile
import subprocess
import unicodedata
from typing import Dict, Any, Tuple, Optional, List
from urllib.parse import urlparse
from dataclasses import dataclass, asdict

from app.utils.media import download_media_file
from app.services.s3 import s3_service
from app.services.redis_service import redis_service

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class AnimationConfig:
    """Animation configuration for text overlays"""
    type: str = "none"  # fade_in, slide_up, typewriter, pulse, bounce, zoom_in
    duration: float = 1.0
    delay: float = 0.0
    easing: str = "ease_out"  # linear, ease_in, ease_out, ease_in_out

@dataclass 
class EffectsConfig:
    """Advanced effects configuration"""
    shadow_enabled: bool = False
    shadow_color: str = "black@0.5"
    shadow_offset_x: int = 2
    shadow_offset_y: int = 2
    
    stroke_enabled: bool = False
    stroke_width: int = 2
    stroke_color: str = "black"
    
    glow_enabled: bool = False
    glow_color: str = "white@0.5"
    glow_size: int = 5
    
    background_enabled: bool = False
    background_color: str = "black@0.8"
    background_padding: int = 10

@dataclass
class TypographyConfig:
    """Advanced typography configuration"""
    font_family: str = "roboto"
    font_weight: str = "regular"  # light, regular, medium, bold
    font_size: int = 48
    font_color: str = "white"
    letter_spacing: float = 0.0
    line_height: float = 1.2
    text_align: str = "center"  # left, center, right

class TextOverlayService:
    """Service for adding text overlays to videos using FFmpeg."""
    
    def __init__(self):
        """Initialize the enhanced text overlay service with modern features."""
        self.font_cache = {}
        self._setup_modern_font_paths()
        self._setup_modern_presets()
        logger.info("Enhanced text overlay service initialized with modern features")
    
    def _setup_modern_font_paths(self):
        """Setup comprehensive font paths with multiple weights and families"""
        base_font_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'fonts')
        
        self.font_variants = {
            'roboto': {
                'light': [
                    os.path.join(base_font_dir, "Roboto-Light.ttf"),
                    "/System/Library/Fonts/Helvetica.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
                ],
                'regular': [
                    os.path.join(base_font_dir, "Roboto-Regular.ttf"),
                    "/System/Library/Fonts/Helvetica.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
                ],
                'medium': [
                    os.path.join(base_font_dir, "Roboto-Medium.ttf"),
                    os.path.join(base_font_dir, "Roboto-Bold.ttf"),
                    "/System/Library/Fonts/Helvetica-Bold.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
                ],
                'bold': [
                    os.path.join(base_font_dir, "Roboto-Bold.ttf"),
                    "/System/Library/Fonts/Helvetica-Bold.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
                ]
            },
            'montserrat': {
                'light': [
                    os.path.join(base_font_dir, "Montserrat-Light.ttf"),
                    os.path.join(base_font_dir, "Roboto-Light.ttf")
                ],
                'regular': [
                    os.path.join(base_font_dir, "Montserrat-Regular.ttf"),
                    os.path.join(base_font_dir, "Roboto-Regular.ttf")
                ],
                'medium': [
                    os.path.join(base_font_dir, "Montserrat-Medium.ttf"),
                    os.path.join(base_font_dir, "Montserrat-SemiBold.ttf")
                ],
                'bold': [
                    os.path.join(base_font_dir, "Montserrat-Bold.ttf"),
                    os.path.join(base_font_dir, "Roboto-Bold.ttf")
                ]
            },
            'inter': {
                'light': [
                    os.path.join(base_font_dir, "Inter-Light.ttf"),
                    os.path.join(base_font_dir, "Roboto-Light.ttf")
                ],
                'regular': [
                    os.path.join(base_font_dir, "Inter-Regular.ttf"),
                    os.path.join(base_font_dir, "Roboto-Regular.ttf")
                ],
                'medium': [
                    os.path.join(base_font_dir, "Inter-Medium.ttf"),
                    os.path.join(base_font_dir, "Roboto-Medium.ttf")
                ],
                'bold': [
                    os.path.join(base_font_dir, "Inter-Bold.ttf"),
                    os.path.join(base_font_dir, "Roboto-Bold.ttf")
                ]
            }
        }
    
    def _setup_modern_presets(self):
        """Setup modern presets alongside legacy ones"""
        # Keep existing presets for backward compatibility
        self.presets = {
            "title_overlay": {
                "description": "Large title text at the top, optimized for short phrases",
                "options": {
                    "duration": 5,
                    "font_size": 60,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.85,
                    "boxborderw": 80,
                    "position": "top-center",
                    "y_offset": 80,
                    "line_spacing": 18,
                    "auto_wrap": True
                }
            },
            "subtitle": {
                "description": "Subtitle text at the bottom, with good readability",
                "options": {
                    "duration": 10,
                    "font_size": 42,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.8,
                    "boxborderw": 60,
                    "position": "bottom-center",
                    "y_offset": 100,
                    "line_spacing": 15,
                    "auto_wrap": True
                }
            },
            "watermark": {
                "description": "Small, subtle watermark text",
                "options": {
                    "duration": 999999,
                    "font_size": 28,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.6,
                    "boxborderw": 45,
                    "position": "bottom-right",
                    "y_offset": 40,
                    "line_spacing": 10,
                    "auto_wrap": True
                }
            },
            "alert": {
                "description": "Alert/notification style overlay, prominent and attention-grabbing",
                "options": {
                    "duration": 4,
                    "font_size": 56,
                    "font_color": "white",
                    "box_color": "red",
                    "box_opacity": 0.9,
                    "boxborderw": 75,
                    "position": "center",
                    "y_offset": 0,
                    "line_spacing": 16,
                    "auto_wrap": True
                }
            },
            "modern_caption": {
                "description": "Modern caption style with solid background and good padding",
                "options": {
                    "duration": 5,
                    "font_size": 50,
                    "font_color": "black",
                    "box_color": "white",
                    "box_opacity": 0.85,
                    "boxborderw": 70,
                    "position": "top-center",
                    "y_offset": 100,
                    "line_spacing": 16,
                    "auto_wrap": True
                }
            },
            "social_post": {
                "description": "Instagram/TikTok style caption, trendy and engaging",
                "options": {
                    "duration": 6,
                    "font_size": 48,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.7,
                    "boxborderw": 65,
                    "position": "bottom-center",
                    "y_offset": 120,
                    "line_spacing": 15,
                    "auto_wrap": True
                }
            },
            "quote": {
                "description": "Quote or testimonial style with elegant presentation",
                "options": {
                    "duration": 8,
                    "font_size": 44,
                    "font_color": "white",
                    "box_color": "navy",
                    "box_opacity": 0.8,
                    "boxborderw": 70,
                    "position": "center",
                    "y_offset": 0,
                    "line_spacing": 17,
                    "auto_wrap": True
                }
            },
            "news_ticker": {
                "description": "News ticker style for breaking news or updates",
                "options": {
                    "duration": 15,
                    "font_size": 36,
                    "font_color": "white",
                    "box_color": "darkred",
                    "box_opacity": 0.95,
                    "boxborderw": 50,
                    "position": "bottom-center",
                    "y_offset": 50,
                    "line_spacing": 12,
                    "auto_wrap": True
                }
            }
        }
        
        # Add modern presets with advanced features
        self.modern_presets = {
            "tiktok_viral": {
                "name": "TikTok Viral",
                "description": "High-engagement TikTok-style captions with bold styling",
                "category": "social_media",
                "typography": {
                    "font_family": "montserrat",
                    "font_weight": "bold",
                    "font_size": 52,
                    "font_color": "white"
                },
                "effects": {
                    "stroke_enabled": True,
                    "stroke_width": 3,
                    "stroke_color": "black",
                    "shadow_enabled": True,
                    "shadow_color": "black@0.7",
                    "shadow_offset_x": 2,
                    "shadow_offset_y": 2
                },
                "animation": {
                    "type": "fade_in",
                    "duration": 0.8
                },
                "options": {
                    "duration": 4,
                    "position": "bottom-center",
                    "auto_wrap": True
                }
            },
            
            "instagram_story": {
                "name": "Instagram Story", 
                "description": "Clean Instagram Story text with modern aesthetics",
                "category": "social_media",
                "typography": {
                    "font_family": "inter",
                    "font_weight": "medium",
                    "font_size": 44,
                    "font_color": "white"
                },
                "effects": {
                    "background_enabled": True,
                    "background_color": "black@0.6",
                    "background_padding": 20
                },
                "animation": {
                    "type": "slide_up",
                    "duration": 1.0
                },
                "options": {
                    "duration": 6,
                    "position": "top-center",
                    "y_offset": 100,
                    "auto_wrap": True
                }
            },
            
            "youtube_title": {
                "name": "YouTube Title",
                "description": "Eye-catching YouTube thumbnail-style title",
                "category": "video_platform",
                "typography": {
                    "font_family": "roboto",
                    "font_weight": "bold", 
                    "font_size": 64,
                    "font_color": "yellow"
                },
                "effects": {
                    "stroke_enabled": True,
                    "stroke_width": 4,
                    "stroke_color": "red",
                    "shadow_enabled": True,
                    "shadow_color": "black@0.9",
                    "shadow_offset_x": 3,
                    "shadow_offset_y": 3
                },
                "animation": {
                    "type": "zoom_in",
                    "duration": 1.2
                },
                "options": {
                    "duration": 8,
                    "position": "center",
                    "y_offset": -50,
                    "auto_wrap": True
                }
            },
            
            "neon_glow": {
                "name": "Neon Glow",
                "description": "Futuristic neon-style text with glow effects",
                "category": "creative",
                "typography": {
                    "font_family": "montserrat",
                    "font_weight": "bold",
                    "font_size": 56,
                    "font_color": "cyan"
                },
                "effects": {
                    "glow_enabled": True,
                    "glow_color": "cyan@0.6",
                    "glow_size": 8,
                    "stroke_enabled": True,
                    "stroke_width": 2,
                    "stroke_color": "white"
                },
                "animation": {
                    "type": "pulse",
                    "duration": 2.0
                },
                "options": {
                    "duration": 6,
                    "position": "center",
                    "auto_wrap": True
                }
            }
        }
        logger.info("Enhanced text overlay service initialized with modern presets")
    
    def get_available_presets(self) -> Dict[str, Any]:
        """Get all available text overlay presets (legacy)."""
        return self.presets
    
    def get_modern_presets(self) -> Dict[str, Any]:
        """Get modern text overlay presets with advanced features."""
        return self.modern_presets
    
    def get_all_presets(self) -> Dict[str, Any]:
        """Get both legacy and modern presets combined."""
        combined_presets = {}
        # Add legacy presets with category
        for name, preset in self.presets.items():
            combined_presets[name] = {
                **preset,
                "category": "classic",
                "type": "legacy"
            }
        # Add modern presets
        for name, preset in self.modern_presets.items():
            combined_presets[name] = {
                **preset,
                "type": "modern"
            }
        return combined_presets
    
    def get_modern_font_path(self, family: str, weight: str) -> str:
        """Get the best available font path for modern typography."""
        cache_key = f"{family}_{weight}"
        if cache_key in self.font_cache:
            return self.font_cache[cache_key]
        
        # Get font candidates
        candidates = self.font_variants.get(family, {}).get(weight, [])
        if not candidates:
            # Fallback to roboto regular
            candidates = self.font_variants.get('roboto', {}).get('regular', [])
        
        # Find first existing font
        for font_path in candidates:
            if os.path.exists(font_path):
                self.font_cache[cache_key] = font_path
                logger.info(f"Using font: {font_path} for {family} {weight}")
                return font_path
        
        # Ultimate fallback
        fallback = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        self.font_cache[cache_key] = fallback
        logger.warning(f"Using fallback font: {fallback} for {family} {weight}")
        return fallback
    
    def process_modern_text_content(self, text: str) -> str:
        """Process text with modern Unicode support (no character replacement)."""
        # Normalize Unicode text properly
        normalized = unicodedata.normalize('NFC', text)
        
        # Smart FFmpeg escaping that preserves international characters
        escape_map = {
            ':': r'\:',
            "'": r"\'",
            '"': r'\"', 
            '\\': r'\\\\',
            '[': r'\[',
            ']': r'\]',
            '%': r'\%',
            '=': r'\=',
            ';': r'\;',
            ',': r'\,'
        }
        
        escaped = normalized
        for char, escape in escape_map.items():
            escaped = escaped.replace(char, escape)
            
        return escaped
    
    def wrap_text(self, text: str, max_width: int = 25) -> str:
        """
        Legacy text wrapping (kept for backward compatibility).
        """
        return self.intelligent_text_wrapping(text, max_width, 48)
    
    def intelligent_text_wrapping(self, text: str, max_width_chars: int = 25, font_size: int = 48) -> str:
        """
        Advanced text wrapping with visual width consideration and smart breaking.
        
        Args:
            text: Text to wrap
            max_width_chars: Maximum characters per line
            font_size: Font size for width estimation
            
        Returns:
            Intelligently wrapped text with newlines
        """
        # Character width estimation based on font size
        avg_char_width = font_size * 0.6
        max_pixel_width = max_width_chars * avg_char_width
        
        words = text.split()
        lines = []
        current_line = []
        current_width = 0
        
        for word in words:
            # Estimate visual width of word (considering common wide/narrow chars)
            word_width = 0
            for char in word:
                if char in 'mwMW@%':
                    word_width += avg_char_width * 1.3  # Wide characters
                elif char in 'il1|!':
                    word_width += avg_char_width * 0.4  # Narrow characters
                elif char in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
                    word_width += avg_char_width * 1.1  # Capital letters
                else:
                    word_width += avg_char_width  # Normal characters
            
            space_width = avg_char_width * 0.3 if current_line else 0
            
            if current_width + word_width + space_width > max_pixel_width and current_line:
                lines.append(' '.join(current_line))
                current_line = [word]
                current_width = word_width
            else:
                current_line.append(word)
                current_width += word_width + space_width
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return '\\n'.join(lines)
    
    def escape_text_for_ffmpeg(self, text: str) -> str:
        """
        Legacy text escaping with character replacement (kept for backward compatibility).
        Use process_modern_text_content() for better Unicode support.
        """
        # Use modern processing but keep method signature for compatibility
        return self.process_modern_text_content(text)
    
    def get_font_file(self, text: str) -> str:
        """
        Get appropriate font file for the text.
        
        Args:
            text: Text content to check for emojis
            
        Returns:
            Path to the appropriate font file
        """
        # Font directory path
        font_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'fonts')
        
        # Check if text contains emojis
        has_emoji = bool(re.search(r'[\U0001F600-\U0001F64F]|[\U0001F300-\U0001F5FF]|[\U0001F680-\U0001F6FF]|[\U0001F1E0-\U0001F1FF]|[\U00002600-\U000027BF]|[\U0001F900-\U0001F9FF]', text))
        
        # Font files with priority order - local fonts first
        if has_emoji:
            font_files = [
                os.path.join(font_dir, "OpenSansEmoji.ttf"),
                os.path.join(font_dir, "DejaVuSans.ttf"),
                os.path.join(font_dir, "Roboto-Regular.ttf"),
                os.path.join(font_dir, "Arial.ttf"),
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ]
        else:
            font_files = [
                os.path.join(font_dir, "Roboto-Bold.ttf"),
                os.path.join(font_dir, "Arial.ttf"),
                os.path.join(font_dir, "DejaVuSans-Bold.ttf"),
                os.path.join(font_dir, "DejaVuSans.ttf"),
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ]
        
        # Find first existing font file
        for font in font_files:
            if os.path.exists(font):
                return font
        
        # Final fallback
        fallback_font = os.path.join(font_dir, "DejaVuSans.ttf")
        if not os.path.exists(fallback_font):
            fallback_font = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        
        return fallback_font
    
    def get_position_coordinates(self, position: str, y_offset: int) -> str:
        """
        Get FFmpeg position coordinates for text placement.
        
        Args:
            position: Position name (e.g., 'top-center', 'bottom-left')
            y_offset: Vertical offset in pixels
            
        Returns:
            FFmpeg position coordinates string
        """
        position_map = {
            'top-left': 'x=30:y=30',
            'top-center': 'x=(w-text_w)/2:y=30',
            'top-right': 'x=w-text_w-30:y=30',
            'center-left': 'x=30:y=(h-text_h)/2',
            'center': 'x=(w-text_w)/2:y=(h-text_h)/2',
            'center-right': 'x=w-text_w-30:y=(h-text_h)/2',
            'bottom-left': 'x=30:y=h-text_h-30',
            'bottom-center': 'x=(w-text_w)/2:y=h-text_h-30',
            'bottom-right': 'x=w-text_w-30:y=h-text_h-30',
        }
        
        position_coords = position_map.get(position, 'x=(w-text_w)/2:y=50')
        
        # Adjust y_offset based on position
        if 'top' in position and y_offset != 30:
            position_coords = position_coords.replace('y=30', f'y={y_offset}')
        elif 'bottom' in position and y_offset != 30:
            position_coords = position_coords.replace('y=h-text_h-30', f'y=h-text_h-{y_offset}')
        elif 'center' in position and 'top' not in position and 'bottom' not in position and y_offset != 0:
            position_coords = position_coords.replace('y=(h-text_h)/2', f'y=(h-text_h)/2+{y_offset}')
        
        return position_coords
    
    def get_smart_position(self, video_path: str, text: str, options: Dict[str, Any]) -> Tuple[str, str]:
        """
        Analyze video for optimal text placement using content analysis.
        
        Args:
            video_path: Path to the video file
            text: Text content to position
            options: Text overlay options
            
        Returns:
            Tuple of (x_position, y_position) expressions
        """
        try:
            # Get video dimensions and basic info
            probe_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-show_entries", "stream=width,height", "-select_streams", "v:0",
                "-of", "csv=p=0", video_path
            ]
            
            result = subprocess.run(probe_cmd, capture_output=True, text=True)
            lines = result.stdout.strip().split('\n')
            
            if len(lines) >= 2:
                width, height = map(int, lines[0].split(','))
                duration = float(lines[1])
            else:
                # Fallback dimensions
                width, height, duration = 1920, 1080, 10.0
            
            # Analyze text characteristics
            text_length = len(text)
            line_count = text.count('\\n') + 1
            aspect_ratio = width / height
            
            # Smart positioning based on content analysis
            position = options.get('position', 'smart')
            
            if position == 'smart' or position == 'content_aware':
                # For short text (like titles), use bottom center with safe margins
                if text_length < 50 and line_count <= 2:
                    x = "(w-text_w)/2"
                    y = f"h-text_h-{max(height * 0.1, 50)}"
                
                # For medium text, use top area to avoid typical UI elements
                elif text_length < 150:
                    x = "(w-text_w)/2" 
                    y = f"{max(height * 0.1, 50)}"
                
                # For long text, use left alignment with proper margins
                else:
                    x = f"{max(width * 0.05, 30)}"
                    y = f"h*0.75-text_h"
                    
                # Adjust for aspect ratio
                if aspect_ratio > 1.5:  # Wide landscape
                    y = "h*0.85-text_h"
                elif aspect_ratio < 0.8:  # Portrait/mobile
                    y = "h*0.75-text_h"
                    
            else:
                # Use standard positioning
                y_offset = options.get('y_offset', 50)
                x, y = self.get_position_coordinates(position, y_offset).split(':')
                x = x.replace('x=', '')
                y = y.replace('y=', '')
            
            return x, y
            
        except Exception as e:
            logger.warning(f"Smart positioning failed, using fallback: {e}")
            return "(w-text_w)/2", "h*0.85-text_h"
    
    def create_animation_expressions(self, animation_type: str, duration: float, start_time: float = 0.0) -> Dict[str, str]:
        """
        Create FFmpeg expressions for text animations.
        
        Args:
            animation_type: Type of animation (fade_in, slide_up, etc.)
            duration: Animation duration in seconds
            start_time: Start time for animation
            
        Returns:
            Dictionary of FFmpeg parameter expressions
        """
        expressions = {}
        end_time = start_time + duration
        
        if animation_type == "fade_in":
            expressions['alpha'] = f"'if(lt(t-{start_time},{duration}),(t-{start_time})/{duration},1)'"
            
        elif animation_type == "fade_out":
            fade_start = end_time - duration
            expressions['alpha'] = f"'if(gt(t,{fade_start}),1-(t-{fade_start})/{duration},1)'"
            
        elif animation_type == "slide_up":
            expressions['y'] = f"'y+50*max(0,1-(t-{start_time})/{duration})'"
            expressions['alpha'] = f"'min(1,(t-{start_time})/{duration*0.3})'"
            
        elif animation_type == "slide_down":
            expressions['y'] = f"'y-50*max(0,1-(t-{start_time})/{duration})'"
            expressions['alpha'] = f"'min(1,(t-{start_time})/{duration*0.3})'"
            
        elif animation_type == "slide_left":
            expressions['x'] = f"'x+100*max(0,1-(t-{start_time})/{duration})'"
            expressions['alpha'] = f"'min(1,(t-{start_time})/{duration*0.3})'"
            
        elif animation_type == "slide_right":
            expressions['x'] = f"'x-100*max(0,1-(t-{start_time})/{duration})'"
            expressions['alpha'] = f"'min(1,(t-{start_time})/{duration*0.3})'"
            
        elif animation_type == "zoom_in":
            expressions['fontsize'] = f"'fontsize*(0.1+0.9*min(1,(t-{start_time})/{duration}))'"
            expressions['alpha'] = f"'min(1,(t-{start_time})/{duration*0.5})'"
            
        elif animation_type == "pulse":
            expressions['fontsize'] = f"'fontsize*(1+0.1*sin(2*PI*(t-{start_time})*2))'"
            
        elif animation_type == "bounce":
            expressions['y'] = f"'y-abs(sin(PI*(t-{start_time})/{duration}))*20'"
            
        elif animation_type == "typewriter":
            text_len = 50  # Default approximation
            expressions['text'] = f"'substr(text,0,max(0,min({text_len},floor((t-{start_time})/{duration}*{text_len}))))'"
        
        return expressions
    
    def build_advanced_filter_chain(self, text: str, options: Dict[str, Any], video_path: Optional[str] = None) -> str:
        """
        Build comprehensive FFmpeg filter chain with modern features.
        
        Args:
            text: Text content
            options: Configuration options
            video_path: Path to video for smart positioning
            
        Returns:
            Complete FFmpeg filter string
        """
        filters = []
        
        # Get font configuration (modern or legacy)
        if 'typography' in options:
            typography = options['typography']
            font_path = self.get_modern_font_path(
                typography.get('font_family', 'roboto'),
                typography.get('font_weight', 'regular')
            )
            font_size = typography.get('font_size', 48)
            font_color = typography.get('font_color', 'white')
        else:
            # Legacy options
            font_path = self.get_font_file(text)
            font_size = options.get('font_size', 48)
            font_color = options.get('font_color', 'white')
        
        # Process text content
        auto_wrap = options.get('auto_wrap', True)
        if auto_wrap:
            wrapped_text = self.intelligent_text_wrapping(text, options.get('max_width_chars', 25), font_size)
        else:
            wrapped_text = text
            
        escaped_text = self.process_modern_text_content(wrapped_text)
        
        # Get positioning (smart or standard)
        if video_path and options.get('position') in ['smart', 'content_aware']:
            x_pos, y_pos = self.get_smart_position(video_path, text, options)
        else:
            position = options.get('position', 'bottom-center')
            y_offset = options.get('y_offset', 50)
            position_coords = self.get_position_coordinates(position, y_offset)
            x_pos, y_pos = position_coords.split(':')
            x_pos = x_pos.replace('x=', '')
            y_pos = y_pos.replace('y=', '')
        
        # Base drawtext parameters
        base_params = {
            'fontfile': font_path,
            'text': escaped_text,  # Remove extra quotes here
            'fontsize': font_size,
            'fontcolor': font_color,
            'x': x_pos,
            'y': y_pos
        }
        
        # Add modern effects if available
        effects = options.get('effects', {})
        
        # Stroke/border effects
        if effects.get('stroke_enabled') or options.get('boxborderw'):
            stroke_width = effects.get('stroke_width', options.get('boxborderw', 2))
            stroke_color = effects.get('stroke_color', 'black')
            base_params['borderw'] = stroke_width
            base_params['bordercolor'] = stroke_color
            
        # Shadow effects
        if effects.get('shadow_enabled'):
            base_params['shadowcolor'] = effects.get('shadow_color', 'black@0.5')
            base_params['shadowx'] = effects.get('shadow_offset_x', 2)
            base_params['shadowy'] = effects.get('shadow_offset_y', 2)
            
        # Background box effects
        if effects.get('background_enabled') or options.get('box_color'):
            base_params['box'] = 1
            bg_color = effects.get('background_color', f"{options.get('box_color', 'black')}@{options.get('box_opacity', 0.8)}")
            base_params['boxcolor'] = bg_color
            base_params['boxborderw'] = effects.get('background_padding', options.get('boxborderw', 60))
        
        # Line spacing
        line_spacing = options.get('line_spacing', 8)
        if 'typography' in options:
            typography = options['typography']
            line_height = typography.get('line_height', 1.2)
            line_spacing = int(font_size * (line_height - 1))
        base_params['line_spacing'] = line_spacing
        
        # Add animation expressions
        animation = options.get('animation', {})
        if animation and animation.get('type', 'none') != 'none':
            anim_duration = animation.get('duration', 1.0)
            start_time = options.get('start_time', 0.0)
            animation_exprs = self.create_animation_expressions(animation['type'], anim_duration, start_time)
            base_params.update(animation_exprs)
        
        # Add timing with proper FFmpeg escaping
        duration = options.get('duration', 5)
        start_time = options.get('start_time', 0.0)
        
        # Only add enable parameter if we have specific timing requirements
        # and the duration is less than a reasonable maximum (to avoid always-on text)
        if duration < 3600 and start_time >= 0:  # 1 hour max, non-negative start
            # Use a simpler approach: instead of between(), use conditional expressions
            # This avoids the complex function call syntax that was causing issues
            if start_time > 0:
                # Text appears after start_time and disappears after start_time + duration
                end_time = start_time + duration
                enable_expr = f"'gte(t,{start_time})*lte(t,{end_time})'"
            else:
                # Text appears immediately and disappears after duration
                enable_expr = f"'lte(t,{duration})'"
            
            base_params['enable'] = enable_expr
        
        # Build the filter string with proper escaping
        params_list = []
        for k, v in base_params.items():
            # Ensure all values are properly formatted
            if isinstance(v, str) and k == 'text':
                # For text, ensure it's properly escaped
                params_list.append(f"{k}={v}")
            else:
                params_list.append(f"{k}={v}")
        
        params_str = ':'.join(params_list)
        drawtext_filter = f"drawtext={params_str}"
        
        # Add glow effect as separate filter if enabled
        if effects.get('glow_enabled'):
            glow_params = base_params.copy()
            glow_params['fontcolor'] = effects.get('glow_color', 'white@0.5')
            glow_params['fontsize'] = font_size + effects.get('glow_size', 5)
            
            glow_params_str = ':'.join([f"{k}={v}" for k, v in glow_params.items()])
            glow_filter = f"drawtext={glow_params_str}"
            filters.append(glow_filter)
        
        filters.append(drawtext_filter)
        return ','.join(filters)
    
    def optimize_cpu_performance(self) -> Dict[str, str]:
        """Get CPU-optimized FFmpeg settings."""
        cpu_count = os.cpu_count() or 4
        optimal_threads = min(cpu_count - 1, 6)  # Leave one core free, max 6 threads
        
        return {
            'threads': str(optimal_threads),
            'preset': 'faster',  # Good balance of speed/quality
            'crf': '23',         # Good quality setting
            'tune': 'fastdecode' # Optimize for playback
        }
    
    async def add_text_overlay(
        self,
        video_url: str,
        text: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Add text overlay to a video using modern FFmpeg processing.
        
        Args:
            video_url: URL of the video to add text overlay to
            text: Text content to overlay
            options: Text overlay options (supports both legacy and modern formats)
            
        Returns:
            Dictionary with result information
        """
        logger.info(f"Starting modern text overlay for video: {video_url}")
        
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Download video file
            input_path, _ = await download_media_file(video_url, temp_dir)
            
            # Generate output filename
            output_filename = f"text_overlay_{request_id}.mp4"
            output_path = os.path.join(temp_dir, output_filename)
            
            # Build advanced filter chain (supports both modern and legacy options)
            if 'typography' in options or 'effects' in options or 'animation' in options:
                # Use modern filter chain
                filter_chain = self.build_advanced_filter_chain(text, options, input_path)
            else:
                # Use legacy approach but with modern improvements
                filter_chain = self.build_legacy_compatible_filter(text, options, input_path)
            
            # Get CPU optimization settings
            cpu_settings = self.optimize_cpu_performance()
            
            # Log filter chain for debugging if needed
            logger.debug(f"Using filter chain: {filter_chain}")
            
            filter_file_path = None
            try:
                # If filter chain is very long, use a filter file approach
                if len(filter_chain) > 1000:  # Arbitrary limit to avoid command line issues
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                        f.write(filter_chain)
                        filter_file_path = f.name
                    
                    logger.info(f"Using filter file due to long filter chain ({len(filter_chain)} chars)")
                    ffmpeg_command = [
                        "ffmpeg", "-i", input_path,
                        "-filter_complex_script", filter_file_path,
                        "-c:v", "libx264",
                        "-preset", cpu_settings['preset'],
                        "-crf", cpu_settings['crf'],
                        "-threads", cpu_settings['threads'],
                        "-tune", cpu_settings['tune'],
                        "-c:a", "copy",  # Copy audio without re-encoding
                        "-movflags", "+faststart",  # Optimize for streaming
                        "-y", output_path
                    ]
                else:
                    # Build optimized FFmpeg command
                    ffmpeg_command = [
                        "ffmpeg", "-i", input_path,
                        "-vf", filter_chain,
                        "-c:v", "libx264",
                        "-preset", cpu_settings['preset'],
                        "-crf", cpu_settings['crf'],
                        "-threads", cpu_settings['threads'],
                        "-tune", cpu_settings['tune'],
                        "-c:a", "copy",  # Copy audio without re-encoding
                        "-movflags", "+faststart",  # Optimize for streaming
                        "-y", output_path
                    ]
                
                # Execute with optimized environment
                env = os.environ.copy()
                env.update({
                    'LC_ALL': 'C.UTF-8',
                    'LANG': 'C.UTF-8',
                    'PYTHONIOENCODING': 'utf-8'
                })
                
                logger.info("Executing optimized FFmpeg processing...")
                result = subprocess.run(ffmpeg_command, capture_output=True, text=True, env=env)
                
                if result.returncode != 0:
                    # Try fallback with simpler filter
                    logger.warning("Primary processing failed, trying fallback...")
                    fallback_filter = self.build_legacy_compatible_filter(text, options, input_path)
                    
                    # Clean up filter file if used
                    if filter_file_path and os.path.exists(filter_file_path):
                        os.unlink(filter_file_path)
                        filter_file_path = None
                    
                    ffmpeg_command = [
                        "ffmpeg", "-i", input_path,
                        "-vf", fallback_filter,
                        "-c:v", "libx264",
                        "-preset", cpu_settings['preset'],
                        "-crf", cpu_settings['crf'],
                        "-threads", cpu_settings['threads'],
                        "-tune", cpu_settings['tune'],
                        "-c:a", "copy",
                        "-movflags", "+faststart",
                        "-y", output_path
                    ]
                    
                    result = subprocess.run(ffmpeg_command, capture_output=True, text=True, env=env)
                    if result.returncode != 0:
                        raise Exception(f"Text overlay processing failed: {result.stderr}")
            
            finally:
                # Clean up filter file if it was created
                if filter_file_path and os.path.exists(filter_file_path):
                    try:
                        os.unlink(filter_file_path)
                    except OSError:
                        pass
            
            # Upload to S3
            s3_key = f"text_overlays/{output_filename}"
            video_output_url = await s3_service.upload_file(output_path, s3_key)
            
            # Get video info
            probe_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", output_path
            ]
            duration_result = subprocess.run(probe_cmd, capture_output=True, text=True)
            duration = float(duration_result.stdout.strip()) if duration_result.stdout.strip() else 0.0
            
            logger.info(f"Modern text overlay completed: {video_output_url}")
            
            return {
                "video_url": video_output_url,
                "duration": duration,
                "processing_optimized": True,
                "features_used": self._get_features_used(options)
            }
            
        except Exception as e:
            logger.error(f"Text overlay processing failed: {str(e)}")
            raise Exception(f"Text overlay failed: {str(e)}")
            
        finally:
            # Cleanup
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    def build_legacy_compatible_filter(self, text: str, options: Dict[str, Any], video_path: Optional[str] = None) -> str:
        """Build filter chain compatible with legacy options but using modern processing."""
        # Convert legacy options to modern format for internal processing
        modern_options = {
            'typography': {
                'font_size': options.get('font_size', 48),
                'font_color': options.get('font_color', 'white'),
                'font_family': 'roboto',
                'font_weight': 'regular'
            },
            'effects': {},
            'duration': options.get('duration', 5),
            'position': options.get('position', 'bottom-center'),
            'y_offset': options.get('y_offset', 50),
            'line_spacing': options.get('line_spacing', 8),
            'auto_wrap': options.get('auto_wrap', True)
        }
        
        # Add legacy effects
        if options.get('box_color'):
            modern_options['effects']['background_enabled'] = True
            modern_options['effects']['background_color'] = f"{options.get('box_color')}@{options.get('box_opacity', 0.8)}"
            modern_options['effects']['background_padding'] = options.get('boxborderw', 60)
        
        return self.build_advanced_filter_chain(text, modern_options, video_path)
    
    def _get_features_used(self, options: Dict[str, Any]) -> List[str]:
        """Get list of modern features used in this overlay."""
        features = []
        
        if 'typography' in options:
            features.append('modern_typography')
        if 'effects' in options:
            effects = options['effects']
            if effects.get('shadow_enabled'): features.append('shadow_effects')
            if effects.get('stroke_enabled'): features.append('stroke_effects') 
            if effects.get('glow_enabled'): features.append('glow_effects')
            if effects.get('background_enabled'): features.append('background_effects')
        if 'animation' in options and options['animation'].get('type', 'none') != 'none':
            features.append('text_animation')
        if options.get('position') in ['smart', 'content_aware']:
            features.append('smart_positioning')
            
        return features
    
    async def generate_preview(self, video_url: str, text: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a fast preview for real-time feedback.
        
        Args:
            video_url: URL of the video
            text: Text content
            options: Overlay options
            
        Returns:
            Dictionary with preview information
        """
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Download video
            input_path, _ = await download_media_file(video_url, temp_dir)
            
            # Create preview (3 seconds, lower resolution)
            preview_filename = f"preview_{uuid.uuid4().hex[:8]}.mp4"
            preview_path = os.path.join(temp_dir, preview_filename)
            
            # Build filter chain (use simplified for speed)
            filter_chain = self.build_legacy_compatible_filter(text, options, input_path)
            
            # CPU-optimized preview command
            preview_cmd = [
                "ffmpeg", "-i", input_path,
                "-t", "3",  # 3 second preview
                "-vf", f"scale=640:360,{filter_chain}",  # Lower resolution
                "-c:v", "libx264", "-preset", "ultrafast",
                "-crf", "28",  # Lower quality for speed
                "-an",  # No audio
                "-y", preview_path
            ]
            
            result = subprocess.run(preview_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"Preview generation failed: {result.stderr}")
            
            # Upload preview to S3
            s3_key = f"previews/text_overlay/{preview_filename}"
            preview_url = await s3_service.upload_file(preview_path, s3_key)
            
            return {
                "preview_url": preview_url,
                "duration": 3.0,
                "resolution": "640x360",
                "generated_at": "realtime"
            }
            
        finally:
            # Cleanup
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    async def get_cached_preview(self, video_url: str, text: str, options: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Get cached preview if available, otherwise generate new one.
        
        Args:
            video_url: URL of the video
            text: Text content  
            options: Overlay options
            
        Returns:
            Preview information or None if caching fails
        """
        try:
            # Create cache key from options and content
            cache_data = {
                'video_url': video_url,
                'text': text,
                'options': options
            }
            cache_json = json.dumps(cache_data, sort_keys=True)
            cache_key = f"text_overlay_preview:{hashlib.md5(cache_json.encode()).hexdigest()}"
            
            # Check cache
            cached_result = await redis_service.get(cache_key)
            if cached_result:
                logger.info("Using cached preview")
                return json.loads(cached_result)
                
            # Generate new preview
            preview_result = await self.generate_preview(video_url, text, options)
            
            # Cache for 1 hour
            await redis_service.set(cache_key, json.dumps(preview_result), expire=3600)
            logger.info("Generated and cached new preview")
            
            return preview_result
            
        except Exception as e:
            logger.warning(f"Preview caching failed: {e}")
            # Fallback to direct generation
            return await self.generate_preview(video_url, text, options)
    
    async def process_text_overlay_job(self, job_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process text overlay job with support for modern and legacy formats.
        
        Args:
            job_id: The job ID (unused but required for job queue signature)
            data: Dictionary containing text overlay parameters
            
        Returns:
            Dictionary with text overlay results
        """
        try:
            video_url = data.get('video_url')
            text = data.get('text')
            preset_used = data.get('preset_used')
            
            if not video_url:
                raise ValueError("video_url is required")
            
            if not text:
                raise ValueError("text is required")
            
            logger.info(f"Processing text overlay job {job_id}")
            
            # Handle different configuration formats
            if 'modern_config' in data:
                # New modern configuration format
                options = data['modern_config']
                logger.info("Using modern configuration format")
                
            elif preset_used:
                # Handle preset-based configuration
                if preset_used in self.modern_presets:
                    # Modern preset
                    preset_config = self.modern_presets[preset_used]
                    options = {
                        'typography': preset_config['typography'],
                        'effects': preset_config['effects'],
                        'animation': preset_config.get('animation', {}),
                        **preset_config['options']
                    }
                    # Override text
                    if 'options' in data:
                        options.update(data['options'])
                    logger.info(f"Using modern preset: {preset_used}")
                    
                elif preset_used in self.presets:
                    # Legacy preset
                    options = self.presets[preset_used]['options'].copy()
                    # Override with any provided options
                    if 'options' in data:
                        options.update(data['options'])
                    logger.info(f"Using legacy preset: {preset_used}")
                    
                else:
                    raise ValueError(f"Unknown preset: {preset_used}")
                    
            else:
                # Standard options (legacy or modern)
                options = data.get('options', {})
                logger.info("Using provided options")
            
            # Process the overlay
            result = await self.add_text_overlay(video_url, text, options)
            
            # Add metadata
            result.update({
                "preset_used": preset_used,
                "configuration_type": "modern" if any(k in options for k in ['typography', 'effects', 'animation']) else "legacy",
                "job_id": job_id
            })
            
            logger.info(f"Text overlay job {job_id} completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Text overlay job {job_id} failed: {str(e)}", exc_info=True)
            raise Exception(f"Text overlay processing failed: {str(e)}")


# Create a singleton instance
text_overlay_service = TextOverlayService()