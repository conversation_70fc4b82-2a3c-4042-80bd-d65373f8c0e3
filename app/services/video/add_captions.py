"""
Service for adding captions to videos.
"""
import os
import uuid
import logging
import json
import tempfile
import subprocess
import asyncio
from typing import Dict, Any, Tuple, Optional, List
from urllib.parse import urlparse

from app.utils.media import download_media_file, download_subtitle_file
from app.services.s3 import s3_service
from app.utils.captions import prepare_subtitle_styling, create_enhanced_ass_from_timestamps, create_timestamps_from_text
from app.services.media.transcription import get_transcription_service
from app.config import get_caption_style, apply_caption_style_preset, get_available_caption_styles

# Configure logging
logger = logging.getLogger(__name__)

class AddCaptionsService:
    """Service for adding captions to videos."""
    
    async def add_captions_to_video(
        self,
        video_path: str,
        captions_path: str,
        caption_properties: Optional[Dict] = None
    ) -> str:
        """
        Add captions to a video.
        
        Args:
            video_path: Path to the video file
            captions_path: Path to the captions file (SRT or ASS)
            caption_properties: Dictionary of caption styling properties
            
        Returns:
            Path to the output video with captions
        """
        try:
            # Get file extension to determine if it's SRT or ASS
            captions_ext = os.path.splitext(captions_path)[1].lower()
            # Create output directory if it doesn't exist
            os.makedirs("temp/output", exist_ok=True)
            
            # Create output path
            output_path = os.path.join("temp/output", f"captioned_{uuid.uuid4()}.mp4")
            
            # Prepare subtitle styling options
            style_options = prepare_subtitle_styling(caption_properties)
            
            # For both SRT and ASS files, use the subtitles filter for better color support
            if captions_ext == '.ass':
                # For ASS subtitles, use subtitles filter for better color rendering
                subtitle_filter = f"subtitles='{captions_path}'"
                logger.info(f"Using subtitles filter for ASS file: {captions_path}")
            else:
                # For SRT files, use subtitle filter with styling
                subtitle_filter = f"subtitles='{captions_path}'"
                
                if style_options:
                    # Convert dictionary to style string
                    style_parts = []
                    
                    # Handle font specially - check if we need to add font lookup path
                    if 'FontName' in style_options:
                        font_name = style_options['FontName']
                        # First try adding the font name directly
                        style_parts.append(f"fontname={font_name}")
                        
                        # Check if we should also provide a font path hint
                        try:
                            # Try to find the font file path
                            font_path_cmd = ["fc-match", "-v", font_name]
                            font_match_result = subprocess.run(
                                font_path_cmd,
                                capture_output=True,
                                text=True
                            )
                            
                            # Log the font match results for debugging
                            logger.info(f"Font match result for '{font_name}':\n{font_match_result.stdout}")
                            
                            # Alternatively, add the default font directory path hint
                            font_dirs = [
                                "/usr/share/fonts/truetype/custom",
                                "/usr/share/fonts",
                                "/usr/local/share/fonts"
                            ]
                            for font_dir in font_dirs:
                                if os.path.exists(font_dir):
                                    subtitle_filter += f":fontsdir='{font_dir}'"
                                    logger.info(f"Added font directory hint: {font_dir}")
                                    break
                        except Exception as e:
                            logger.warning(f"Error getting font path: {e}")
                    
                    # Add all other style options
                    for key, value in style_options.items():
                        if key != 'FontName':  # Skip font name as we've already handled it
                            style_parts.append(f"{key}={value}")
                    
                    # Add the force_style parameter
                    force_style = ','.join(style_parts)
                    subtitle_filter += f":force_style='{force_style}'"
            
            # Build the command using the filter (optimized for speed)
            cmd = [
                "ffmpeg",
                "-y",
                "-i", video_path,
                "-vf", subtitle_filter,
                "-c:v", "libx264",
                "-preset", "fast",  # Changed from 'medium' to 'fast' for speed
                "-crf", "25",       # Slightly higher CRF for speed
                "-c:a", "copy",
                "-pix_fmt", "yuv420p",
                "-movflags", "+faststart",
                output_path
            ]
            
            # Log the command
            logger.info(f"Running FFmpeg command: {' '.join(cmd)}")
            
            # Run FFmpeg
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown FFmpeg error"
                logger.error(f"FFmpeg error: {error_msg}")
                raise RuntimeError(f"FFmpeg command failed: {error_msg}")
            
            # Check if output file was created
            if not os.path.exists(output_path):
                raise RuntimeError(f"Output file was not created: {output_path}")
                
            logger.info(f"Successfully added captions to video: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error in add_captions_to_video: {e}")
            raise
    
    async def process_job(self, job_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a job to add captions to a video with comprehensive dahopevi-style features.
        
        Args:
            job_id: The ID of the job
            params: Job parameters
                - video_url: URL of the video to add captions to
                - captions: Text content for captions, URL to SRT/ASS file, or None to use audio
                - caption_properties: Enhanced styling properties for captions (optional)
                - replace: List of text replacements to apply to captions
                - language: Language for transcription (default 'auto')
                
        Returns:
            Dictionary with result information
        """
        # Track created files for cleanup
        temp_files = []
        
        try:
            # Extract parameters
            video_url = params["video_url"]
            captions = params.get("captions")
            caption_properties = params.get("caption_properties")
            replace_rules = params.get("replace", [])
            language = params.get("language", "auto")
            
            # Download video
            video_path, _ = await download_media_file(video_url)
            temp_files.append(video_path)
            
            # Variable to hold captions path
            captions_path = None
            srt_url = None
            
            # Apply text replacements to captions if provided
            if captions and replace_rules:
                captions = self._apply_text_replacements(captions, replace_rules)
                logger.info(f"Applied {len(replace_rules)} text replacements to captions")
            
            # Process captions - could be raw text, SRT/ASS file URL, or None (use audio)
            if captions:
                # Check if captions is a URL
                try:
                    parsed_url = urlparse(captions)
                    if parsed_url.scheme and parsed_url.netloc:
                        # It's a URL, download the file
                        captions_path = await download_subtitle_file(captions)
                        temp_files.append(captions_path)
                        srt_url = captions
                        
                        # If it's an SRT file and style is highlight, we may want to convert it to ASS
                        # for better styling, but we'll leave as is for now
                        captions_ext = os.path.splitext(captions_path)[1].lower()
                        if captions_ext == '.srt' and caption_properties and caption_properties.get("style") in ["highlight", "word_by_word"]:
                            logger.info(f"Downloaded SRT file for {caption_properties.get('style')} style. Using as is, but ASS would give better results.")
                    else:
                        # It's raw text, create SRT file
                        # Get video duration
                        duration = self._get_media_duration(video_path)
                        
                        # Get enhanced style and properties
                        max_words_per_line = 10  # Default
                        style = "classic"  # Default to classic style for dahopevi compatibility
                        
                        if caption_properties:
                            if "max_words_per_line" in caption_properties and caption_properties["max_words_per_line"] is not None:
                                max_words_per_line = caption_properties["max_words_per_line"]
                            if "style" in caption_properties and caption_properties["style"] is not None:
                                style = caption_properties["style"]
                        
                        # Convert all_caps if specified
                        if caption_properties and caption_properties.get("all_caps"):
                            captions = captions.upper()
                            logger.info("Converted captions to uppercase")
                        
                        # For advanced styles, create artificial word timestamps for better rendering
                        if style in ["highlight", "word_by_word", "karaoke", "underline", "bounce", "viral_bounce", "typewriter", "fade_in"]:
                            # Split text into words
                            words = captions.split()
                            word_count = len(words)
                            
                            if word_count > 0:
                                # Create improved artificial word timestamps with natural speaking patterns
                                speaking_rate = 2.5  # words per second (natural speech rate)
                                time_per_word = 1.0 / speaking_rate
                                
                                # Scale timing to match actual duration
                                total_time_needed = word_count * time_per_word
                                time_scale = duration / total_time_needed if total_time_needed > 0 else 1.0
                                
                                # Create word timestamps array with natural timing
                                word_timestamps = []
                                current_time = 0.0
                                
                                for word in words:
                                    start_time = current_time
                                    word_duration = time_per_word * time_scale
                                    end_time = start_time + word_duration
                                    
                                    word_timestamps.append({
                                        "word": word,
                                        "start": start_time,
                                        "end": end_time
                                    })
                                    
                                    current_time = end_time
                                
                                # Use enhanced ASS creation for advanced styles
                                captions_path = await create_enhanced_ass_from_timestamps(
                                    word_timestamps,
                                    duration,
                                    max_words_per_line,
                                    style,
                                    caption_properties=caption_properties
                                )
                                logger.info(f"Created {style} style captions from text using artificial word timestamps")
                            else:
                                # No words, create timestamps from text
                                word_timestamps = create_timestamps_from_text(captions, duration)
                                captions_path = await create_enhanced_ass_from_timestamps(
                                    word_timestamps,
                                    duration,
                                    max_words_per_line,
                                    style,
                                    caption_properties=caption_properties
                                )
                                logger.info(f"Created simple captions with empty text")
                        else:
                            # For classic style, use enhanced approach with properties
                            word_timestamps = create_timestamps_from_text(captions, duration)
                            captions_path = await create_enhanced_ass_from_timestamps(
                                word_timestamps,
                                duration,
                                max_words_per_line,
                                style,
                                caption_properties=caption_properties
                            )
                            logger.info(f"Created {style} style captions from text")
                        
                        temp_files.append(captions_path)
                except Exception as e:
                    logger.error(f"Error processing captions URL/text: {e}")
                    # Assume it's raw text if URL parsing fails
                    # Get video duration
                    duration = self._get_media_duration(video_path)
                    
                    # Get enhanced style and properties (fallback case)
                    max_words_per_line = 10  # Default
                    style = "classic"  # Default to classic style
                    
                    if caption_properties:
                        if "max_words_per_line" in caption_properties and caption_properties["max_words_per_line"] is not None:
                            max_words_per_line = caption_properties["max_words_per_line"]
                        if "style" in caption_properties and caption_properties["style"] is not None:
                            style = caption_properties["style"]
                    
                    # Convert all_caps if specified (fallback case)
                    if caption_properties and caption_properties.get("all_caps"):
                        captions = captions.upper()
                        logger.info("Converted captions to uppercase (fallback)")
                    
                    # For advanced styles, create artificial word timestamps for better rendering (fallback)
                    if style in ["highlight", "word_by_word", "karaoke", "underline", "bounce", "viral_bounce", "typewriter", "fade_in"]:
                        # Split text into words
                        words = captions.split()
                        word_count = len(words)
                        
                        if word_count > 0:
                            # Create improved artificial word timestamps with natural speaking patterns
                            # Use the same approach as footage-to-video pipeline for consistency
                            speaking_rate = 2.5  # words per second (natural speech rate)
                            time_per_word = 1.0 / speaking_rate
                            
                            # Scale timing to match actual duration
                            total_time_needed = word_count * time_per_word
                            time_scale = duration / total_time_needed if total_time_needed > 0 else 1.0
                            
                            # Create word timestamps array with natural timing
                            word_timestamps = []
                            current_time = 0.0
                            
                            for word in words:
                                start_time = current_time
                                word_duration = time_per_word * time_scale
                                end_time = start_time + word_duration
                                
                                word_timestamps.append({
                                    "word": word,
                                    "start": start_time,
                                    "end": end_time
                                })
                                
                                current_time = end_time
                            
                            # Use enhanced ASS creation for advanced styles (fallback)
                            captions_path = await create_enhanced_ass_from_timestamps(
                                word_timestamps,
                                duration,
                                max_words_per_line,
                                style,
                                caption_properties=caption_properties
                            )
                            logger.info(f"Created {style} style captions from text using artificial word timestamps (fallback)")
                        else:
                            # No words, create timestamps from text
                            word_timestamps = create_timestamps_from_text(captions, duration)
                            captions_path = await create_enhanced_ass_from_timestamps(
                                word_timestamps,
                                duration,
                                max_words_per_line,
                                style,
                                caption_properties=caption_properties
                            )
                            logger.info(f"Created simple captions with empty text (fallback)")
                    else:
                        # For classic style, use enhanced approach with properties (fallback)
                        word_timestamps = create_timestamps_from_text(captions, duration)
                        captions_path = await create_enhanced_ass_from_timestamps(
                            word_timestamps,
                            duration,
                            max_words_per_line,
                            style,
                            caption_properties=caption_properties
                        )
                        logger.info(f"Created {style} style captions from text (fallback)")
                    
                    temp_files.append(captions_path)
            else:
                # No captions provided, transcribe the audio from the video
                logger.info("No captions provided, transcribing audio from video")
                
                # Create a temporary file for the extracted audio
                temp_dir = "temp"
                audio_path = os.path.join(temp_dir, f"extracted_audio_{uuid.uuid4()}.mp3")
                
                # Extract audio from video
                cmd = [
                    "ffmpeg",
                    "-y",
                    "-i", video_path,
                    "-vn",
                    "-c:a", "libmp3lame",
                    "-q:a", "4",
                    audio_path
                ]
                
                # Run FFmpeg
                result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if result.returncode != 0:
                    logger.error(f"Failed to extract audio: {result.stderr}")
                    raise RuntimeError(f"Failed to extract audio from video: {result.stderr}")
                
                temp_files.append(audio_path)
                
                # Get audio duration
                audio_duration = self._get_media_duration(audio_path)
                logger.info(f"Audio duration: {audio_duration} seconds")
                
                # Get enhanced max_words_per_line and style for transcription
                max_words_per_line = 10  # Default
                style = "classic"  # Default to classic style
                
                if caption_properties:
                    if "max_words_per_line" in caption_properties and caption_properties["max_words_per_line"] is not None:
                        max_words_per_line = caption_properties["max_words_per_line"]
                    if "style" in caption_properties and caption_properties["style"] is not None:
                        style = caption_properties["style"]
                
                # For advanced styles, we need word-level timestamps
                need_word_timestamps = style in ["highlight", "word_by_word", "karaoke", "underline", "bounce", "viral_bounce", "typewriter", "fade_in"]
                
                # Transcribe the audio file with language support - ALWAYS get word timestamps for better sync
                transcription_params = {
                    "include_text": True,
                    "include_srt": False,  # We'll create our own captions with precise timing
                    "word_timestamps": True,  # ALWAYS get word timestamps for accurate sync
                    "max_words_per_line": max_words_per_line
                }
                
                # Add language parameter if specified and not auto
                if language and language != "auto":
                    transcription_params["language"] = language
                    logger.info(f"Using language '{language}' for transcription")
                
                transcription_result = await get_transcription_service().transcribe(
                    audio_path,
                    **transcription_params
                )
                
                # Log transcription result keys for debugging
                logger.info(f"Transcription result contains keys: {', '.join(transcription_result.keys())}")
                
                # Always use word timestamps if available for better sync (regardless of style)
                if "words" in transcription_result:
                    # Apply text replacements to transcribed words if specified
                    words_data = transcription_result["words"]
                    if replace_rules:
                        words_data = self._apply_word_replacements(words_data, replace_rules)
                        logger.info(f"Applied {len(replace_rules)} text replacements to transcribed words")
                    
                    # Convert words to uppercase if specified
                    if caption_properties and caption_properties.get("all_caps"):
                        words_data = self._convert_words_to_uppercase(words_data)
                        logger.info("Converted transcribed words to uppercase")
                    
                    # Create enhanced styled subtitles using word timestamps for PERFECT SYNC
                    captions_path = await create_enhanced_ass_from_timestamps(
                        words_data,
                        audio_duration,
                        max_words_per_line,
                        style,
                        caption_properties=caption_properties
                    )
                    temp_files.append(captions_path)
                    logger.info(f"Created {style} style captions using PRECISE word timestamps from Whisper transcription - perfect sync guaranteed!")
                    
                    # Set srt_url if available in transcription result
                    if "srt_url" in transcription_result:
                        srt_url = transcription_result["srt_url"]
                elif "srt_url" in transcription_result:
                    # Download the SRT file for other styles
                    captions_path = await download_subtitle_file(transcription_result["srt_url"])
                    temp_files.append(captions_path)
                    srt_url = transcription_result["srt_url"]
                    logger.info(f"Downloaded SRT file for {style} style captions")
                else:
                    # If we don't have word timestamps but need them for highlight style,
                    # use text from transcription to create artificial timestamps
                    if need_word_timestamps and "text" in transcription_result:
                        text = transcription_result["text"]
                        
                        # Apply text replacements if specified
                        if replace_rules:
                            text = self._apply_text_replacements(text, replace_rules)
                            logger.info(f"Applied {len(replace_rules)} text replacements to transcribed text")
                        
                        # Convert to uppercase if specified
                        if caption_properties and caption_properties.get("all_caps"):
                            text = text.upper()
                            logger.info("Converted transcribed text to uppercase")
                        
                        words = text.split()
                        word_count = len(words)
                        
                        if word_count > 0:
                            # Create improved artificial word timestamps with natural speaking patterns (transcription fallback)
                            speaking_rate = 2.5  # words per second (natural speech rate)
                            time_per_word = 1.0 / speaking_rate
                            
                            # Scale timing to match actual audio duration
                            total_time_needed = word_count * time_per_word
                            time_scale = audio_duration / total_time_needed if total_time_needed > 0 else 1.0
                            
                            # Create word timestamps array with natural timing
                            word_timestamps = []
                            current_time = 0.0
                            
                            for word in words:
                                start_time = current_time
                                word_duration = time_per_word * time_scale
                                end_time = start_time + word_duration
                                
                                word_timestamps.append({
                                    "word": word,
                                    "start": start_time,
                                    "end": end_time
                                })
                                
                                current_time = end_time
                            
                            # Use enhanced ASS creation for advanced styles
                            captions_path = await create_enhanced_ass_from_timestamps(
                                word_timestamps,
                                audio_duration,
                                max_words_per_line,
                                style,
                                caption_properties=caption_properties
                            )
                            temp_files.append(captions_path)
                            logger.info(f"Created {style} style captions using artificial word timestamps from transcription text")
                        else:
                            raise RuntimeError("Transcription text is empty")
                    else:
                        raise RuntimeError("Transcription failed to generate subtitles")
            
            # Get video dimensions for responsive caption properties
            video_info = self._get_video_info(video_path)
            width = video_info.get("width", 1920)
            height = video_info.get("height", 1080)
            
            # Use responsive caption properties if none provided or merge with existing
            if not caption_properties:
                style = "viral_bounce"  # Default to viral_bounce for best results
                caption_properties = self._get_responsive_caption_properties(style, width, height)
                logger.info(f"Using responsive {style} caption properties for {width}x{height} video")
            else:
                # Merge with responsive properties to ensure good defaults
                style = caption_properties.get("style", "viral_bounce")
                responsive_props = self._get_responsive_caption_properties(style, width, height)
                
                # Only merge non-None user properties to preserve responsive defaults
                merged_properties = responsive_props.copy()
                for key, value in caption_properties.items():
                    if value is not None:
                        merged_properties[key] = value
                
                caption_properties = merged_properties
                logger.info(f"Merged user properties with responsive {style} properties for {width}x{height} video")
                logger.info(f"[DEBUG] Final merged properties: word_color={caption_properties.get('word_color')}, font_size={caption_properties.get('font_size')}, font_family={caption_properties.get('font_family')}")
            
            # Add captions to video
            output_path = await self.add_captions_to_video(
                video_path=video_path,
                captions_path=captions_path,
                caption_properties=caption_properties
            )
            temp_files.append(output_path)
            
            # Upload to S3
            object_name = f"videos/captioned_{uuid.uuid4()}.mp4"
            result_url = await s3_service.upload_file(output_path, object_name)
            
            # Get video info
            video_info = self._get_video_info(output_path)

            video_info["url"] = result_url

            # remove the signature from the url
            video_info["url"] = video_info["url"].split("?")[0]
            
            # Prepare response
            result = {
                "final_video_url": video_info["url"],  # Standardize to final_video_url
                "url": video_info["url"],  # Keep for backward compatibility
                "path": object_name,
                "duration": video_info.get("duration", 0),
                "width": video_info.get("width", 0),
                "height": video_info.get("height", 0)
            }
            
            # Add SRT URL if available
            if srt_url:
                result["srt_url"] = srt_url
            
            return result
            
        except Exception as e:
            logger.error(f"Error in process_job: {e}")
            raise
        finally:
            # Clean up temporary files
            for file_path in temp_files:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        logger.info(f"Removed temporary file: {file_path}")
                    except Exception as e:
                        logger.warning(f"Failed to remove temporary file {file_path}: {e}")
    
    def _apply_text_replacements(self, text: str, replace_rules: List[Dict]) -> str:
        """
        Apply text replacement rules to caption text.
        
        Args:
            text: Original text
            replace_rules: List of replacement rules with 'find' and 'replace' keys
            
        Returns:
            Text with replacements applied
        """
        try:
            result_text = text
            for rule in replace_rules:
                if isinstance(rule, dict) and "find" in rule and "replace" in rule:
                    find_text = rule["find"]
                    replace_text = rule["replace"]
                    result_text = result_text.replace(find_text, replace_text)
                    logger.debug(f"Replaced '{find_text}' with '{replace_text}'")
            return result_text
        except Exception as e:
            logger.error(f"Error applying text replacements: {e}")
            return text
    
    def _apply_word_replacements(self, words_data: List[Dict], replace_rules: List[Dict]) -> List[Dict]:
        """
        Apply text replacement rules to word timestamp data.
        
        Args:
            words_data: List of word timestamp dictionaries
            replace_rules: List of replacement rules with 'find' and 'replace' keys
            
        Returns:
            Word data with replacements applied
        """
        try:
            result_words = []
            for word_data in words_data:
                if "word" in word_data:
                    word = word_data["word"]
                    for rule in replace_rules:
                        if isinstance(rule, dict) and "find" in rule and "replace" in rule:
                            find_text = rule["find"]
                            replace_text = rule["replace"]
                            word = word.replace(find_text, replace_text)
                    
                    # Create new word data with replaced word
                    new_word_data = word_data.copy()
                    new_word_data["word"] = word
                    result_words.append(new_word_data)
                else:
                    result_words.append(word_data)
            return result_words
        except Exception as e:
            logger.error(f"Error applying word replacements: {e}")
            return words_data
    
    def _convert_words_to_uppercase(self, words_data: List[Dict]) -> List[Dict]:
        """
        Convert words in word timestamp data to uppercase.
        
        Args:
            words_data: List of word timestamp dictionaries
            
        Returns:
            Word data with uppercase words
        """
        try:
            result_words = []
            for word_data in words_data:
                if "word" in word_data:
                    new_word_data = word_data.copy()
                    new_word_data["word"] = word_data["word"].upper()
                    result_words.append(new_word_data)
                else:
                    result_words.append(word_data)
            return result_words
        except Exception as e:
            logger.error(f"Error converting words to uppercase: {e}")
            return words_data
    
    def _get_media_duration(self, media_path: str) -> float:
        """
        Get the duration of a media file in seconds using FFprobe.
        
        Args:
            media_path: Path to the media file
            
        Returns:
            Duration in seconds
            
        Raises:
            RuntimeError: If the FFprobe operation fails
        """
        try:
            # Use FFprobe to get the duration
            cmd = [
                "ffprobe",
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                media_path
            ]
            
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            duration = float(result.stdout.strip())
            
            return duration
        except subprocess.CalledProcessError as e:
            logger.error(f"FFprobe error: {e.stderr}")
            raise RuntimeError(f"Failed to get media duration: {e.stderr}")
        except Exception as e:
            logger.error(f"Error getting media duration: {e}")
            raise RuntimeError(f"Failed to get media duration: {str(e)}")
    
    def _get_responsive_caption_properties(self, style: str, width: int, height: int) -> Dict:
        """Get responsive caption properties from configuration with responsive adjustments."""
        try:
            # Get base configuration for the style
            base_config = get_caption_style(style)
            
            # Calculate responsive values based on video dimensions
            is_portrait = height > width
            is_square = abs(width - height) < 100
            
            # Responsive font size calculation based on config
            base_font_size = base_config.get('font_size', 48)
            if is_portrait:
                # Reduce font size for mobile portrait
                responsive_font_size = max(24, int(base_font_size * 0.7))
                max_words = base_config.get('max_words_per_line', 4)
                margin_bottom = max(100, int(height * 0.08))
            elif is_square:
                responsive_font_size = max(20, int(base_font_size * 0.6))
                max_words = base_config.get('max_words_per_line', 4)
                margin_bottom = max(60, int(height * 0.04))
            else:  # landscape
                responsive_font_size = max(32, int(base_font_size * 0.8))
                max_words = base_config.get('max_words_per_line', 6)
                margin_bottom = max(50, int(height * 0.035))
            
            # Outline width relative to font size
            outline_width = max(1, int(responsive_font_size * 0.08))
            
            # Build responsive properties from config
            responsive_properties = {
                'style': base_config.get('style', style),
                'font_size': responsive_font_size,
                'font_family': base_config.get('font_family', 'Arial-Bold'),
                'line_color': base_config.get('line_color', '#FFFFFF'),
                'word_color': base_config.get('word_color', '#FFFF00'),
                'outline_color': base_config.get('outline_color', 'black'),
                'outline_width': outline_width,
                'position': self._map_position(base_config.get('position', 'bottom_center')),
                'max_words_per_line': max_words,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'bold': base_config.get('bold', True),
                'all_caps': base_config.get('all_caps', False),
                'line_spacing': 1.3 if is_portrait else 1.2,
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45
            }
            
            # Add style-specific properties based on style name
            if 'viral' in style or 'bounce' in style:
                responsive_properties.update({
                    'bounce_intensity': 1.5,
                    'animation_speed': 1.2
                })
            elif 'typewriter' in style:
                responsive_properties.update({
                    'typewriter_speed': 3.0
                })
            elif 'fade' in style:
                responsive_properties.update({
                    'animation_speed': 1.0
                })
            
            return responsive_properties
            
        except Exception as e:
            logger.warning(f"Failed to load caption style config for '{style}': {e}")
            # Fallback to hardcoded responsive properties
            return self._get_responsive_caption_properties_fallback(style, width, height)

    def _map_position(self, config_position: str) -> str:
        """Map configuration position to caption service format."""
        position_mapping = {
            'top_center': 'top',
            'center': 'center', 
            'bottom_center': 'bottom_center',
            'top': 'top',
            'bottom': 'bottom'
        }
        return position_mapping.get(config_position, 'bottom_center')

    def _get_responsive_caption_properties_fallback(self, style: str, width: int, height: int) -> Dict:
        """Get responsive caption properties based on style and video dimensions (hardcoded fallback)."""
        # Calculate responsive values based on video dimensions
        is_portrait = height > width
        is_square = abs(width - height) < 100
        
        # Smaller base font size calculation for better mobile experience
        if is_portrait:
            base_font_size = max(28, int(height * 0.028))  # Reduced to 2.8% of height (20% smaller)
            max_words_per_line = 3  # Reduced for better sync and readability
            margin_bottom = max(100, int(height * 0.08))   # Increased margin for mobile safe area
        elif is_square:
            base_font_size = max(24, int(height * 0.024))  # Reduced to 2.4% of height (20% smaller)
            max_words_per_line = 3
            margin_bottom = max(60, int(height * 0.04))    # 4% of height
        else:  # landscape
            base_font_size = max(42, int(height * 0.045))  # Increased to 4.5% of height for better readability
            max_words_per_line = 4
            margin_bottom = max(50, int(height * 0.035))   # 3.5% of height
        
        # Outline width relative to font size
        outline_width = max(2, int(base_font_size * 0.1))  # 10% of font size for better visibility
        
        style_presets = {
            'viral_bounce': {
                'style': 'viral_bounce',
                'font_size': base_font_size,
                'line_color': 'white',
                'word_color': 'yellow',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'center',
                'all_caps': True,
                'bounce_intensity': 1.5,
                'animation_speed': 1.2,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45
            },
            'typewriter': {
                'style': 'typewriter',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'typewriter_speed': 3.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45
            },
            'fade_in': {
                'style': 'fade_in',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'animation_speed': 1.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            },
            'classic': {
                'style': 'classic',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.1,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            }
        }
        
        return style_presets.get(style, style_presets['classic'])

    def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        Get video information (duration, width, height) using FFprobe.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Dictionary with video information
            
        Raises:
            RuntimeError: If the FFprobe operation fails
        """
        try:
            # Use FFprobe to get video information
            cmd = [
                "ffprobe",
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=width,height:format=duration",
                "-of", "json",
                video_path
            ]
            
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            info = json.loads(result.stdout)
            
            video_info = {}
            
            # Get duration
            if "format" in info and "duration" in info["format"]:
                video_info["duration"] = float(info["format"]["duration"])
            
            # Get width and height
            if "streams" in info and len(info["streams"]) > 0:
                stream = info["streams"][0]
                if "width" in stream:
                    video_info["width"] = stream["width"]
                if "height" in stream:
                    video_info["height"] = stream["height"]
            
            return video_info
        except subprocess.CalledProcessError as e:
            logger.error(f"FFprobe error: {e.stderr}")
            raise RuntimeError(f"Failed to get video information: {e.stderr}")
        except Exception as e:
            logger.error(f"Error getting video information: {e}")
            raise RuntimeError(f"Failed to get video information: {str(e)}")

# Create a singleton instance
add_captions_service = AddCaptionsService() 