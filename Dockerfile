# syntax=docker/dockerfile:1
# Enable BuildKit for advanced caching

# Frontend build stage - optimized with cache mounts
FROM node:18-alpine AS frontend-builder
WORKDIR /frontend

# Copy package files first for better caching
COPY frontend/package*.json ./
# Use cache mount for npm cache to speed up builds
# Need dev dependencies for building (vite is in devDependencies)
RUN --mount=type=cache,target=/root/.npm \
    npm ci --legacy-peer-deps

# Copy source and build (this layer only rebuilds if frontend changes)
COPY frontend/ ./
RUN npm run build

# Base stage with dependencies
FROM python:3.12-slim AS base

WORKDIR /app

# Install system dependencies including audiocraft requirements
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg tesseract-ocr tesseract-ocr-eng build-essential \
    wget git fontconfig curl ca-certificates postgresql-client redis-tools \
    pkg-config libavformat-dev libavcodec-dev libavdevice-dev \
    libavutil-dev libswscale-dev libswresample-dev libavfilter-dev \
    libsndfile1-dev libfftw3-dev && \
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies with cache mount
COPY requirements.txt .
# Set environment variables to speed up pip builds
ENV PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_COMPILE=1 \
    PIP_PREFER_BINARY=1
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --no-cache-dir -r requirements.txt

# Create font directory and copy fonts (cached - done before app code)
RUN mkdir -p /usr/share/fonts/truetype/custom
COPY fonts/*.ttf /usr/share/fonts/truetype/custom/
RUN fc-cache -f -v

# Create directories for model caching and temporary files (cached - done before app code)
RUN mkdir -p /app/temp/output /root/.cache/huggingface

# Development stage
FROM base AS development
# Install development dependencies with cache mount
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --no-cache-dir uvicorn[standard] watchdog

# Copy scripts first (less likely to change than app code)
COPY scripts/ ./scripts/
RUN chmod +x ./scripts/*.sh

# Copy static files (music, voices, etc.)
COPY app/static/ ./static/
# Create backup copy for volume initialization
COPY app/static/ ./static_backup/

# Copy configuration files
COPY *.py ./

# Copy application code LAST (most likely to change)
COPY app/ ./app/

# Copy frontend source for development builds
COPY frontend/ ./frontend/

# Build frontend for development (with hot reload support)
WORKDIR /app/frontend
RUN npm install
RUN npm run build

WORKDIR /app

# Use reload for development
CMD ["sh", "-c", "./scripts/init-music.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app/app"]

# Production stage
FROM base AS production

# Copy scripts first (less likely to change than app code)
COPY scripts/ ./scripts/
RUN chmod +x ./scripts/*.sh

# Copy static files (music, voices, etc.)
COPY app/static/ ./static/
# Create backup copy for volume initialization
COPY app/static/ ./static_backup/

# Copy configuration files
COPY *.py ./

# Copy application code LAST (most likely to change)
COPY app/ ./app/

# Copy built frontend from the frontend-builder stage
COPY --from=frontend-builder /frontend/dist ./frontend/dist

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application using startup script
CMD ["./scripts/startup.sh"] 