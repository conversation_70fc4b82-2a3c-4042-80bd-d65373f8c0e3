# Docker Deployment Guide for Simplified Music Generation

This guide covers the Docker deployment requirements for the simplified music generation implementation using Meta's MusicGen.

## 🚀 Quick Start

### Development Deployment
```bash
# Clone the repository
git clone <your-repo-url>
cd ouinhi-api

# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env

# Build and start services
docker-compose up --build
```

### Production Deployment
```bash
# Use production configuration
docker-compose -f docker-compose.prod.yml up --build -d
```

## 📋 System Requirements

### Minimum Requirements
- **CPU**: 4 cores (x86_64)
- **RAM**: 8GB (4GB reserved for music generation)
- **Storage**: 20GB free space
- **Docker**: 20.10+ with BuildKit support
- **Docker Compose**: 2.0+

### Recommended for Production
- **CPU**: 8+ cores
- **RAM**: 16GB+ (8GB reserved for music generation)
- **Storage**: 50GB+ SSD
- **GPU**: Optional (CUDA-compatible for faster generation)

## 🔧 Docker Configuration Updates

### System Dependencies Added
The Dockerfile now includes all required system packages for audiocraft:

```dockerfile
# Audio processing libraries for MusicGen/audiocraft
pkg-config libavformat-dev libavcodec-dev libavdevice-dev \
libavutil-dev libswscale-dev libswresample-dev libavfilter-dev \
libsndfile1-dev libfftw3-dev
```

### Environment Variables

#### Required for Music Generation
```bash
# Model configuration (simplified implementation)
MUSICGEN_MODEL_SIZE=small  # Only 'small' supported
ENABLE_MODEL_WARMUP=true   # Preload model on startup
LOCAL_STORAGE_PATH=/app/temp  # Temporary file storage

# Cache configuration
TRANSFORMERS_CACHE=/root/.cache/huggingface
```

#### Optional Performance Tuning
```bash
# CPU optimization (production)
TORCH_NUM_THREADS=4
OMP_NUM_THREADS=4
MKL_NUM_THREADS=4
```

### Volume Mounts

#### Development (`docker-compose.yml`)
```yaml
volumes:
  - kokoro_models:/app/models
  - huggingface_cache:/root/.cache/huggingface
  - temp_storage:/app/temp
```

#### Production (`docker-compose.prod.yml`)
```yaml
volumes:
  - kokoro_models:/app/models
  - huggingface_cache:/root/.cache/huggingface  # Persistent model cache
  - temp_storage:/app/temp                      # Temporary audio files
  - production_logs:/app/logs                   # Application logs
```

## 🎵 Music Generation Features

### Supported Capabilities
- **Provider**: MusicGen only (simplified implementation)
- **Model**: facebook/musicgen-small
- **Duration**: 1-20 seconds per generation
- **Format**: WAV output only
- **Quality**: High-quality stereo audio

### API Endpoints
- `POST /v1/audio/music` - Generate music from text
- `GET /v1/audio/music/{job_id}` - Check generation status
- `GET /v1/audio/music/info` - Get API information

### Example Usage
```bash
curl -X POST "http://localhost:8000/v1/audio/music" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "lo-fi music with a soothing melody",
    "duration": 10,
    "provider": "musicgen"
  }'
```

## 🚀 Deployment Steps

### 1. Prepare Environment
```bash
# Create deployment directory
sudo mkdir -p /opt/ouinhi/{model_cache,logs,temp}
sudo chown -R 1000:1000 /opt/ouinhi

# Set up environment file
cp .env.example .env
# Edit .env with production values
```

### 2. Configure Resources
```bash
# For production, ensure adequate memory
# Edit docker-compose.prod.yml if needed:
deploy:
  resources:
    limits:
      memory: 8G  # Adjust based on your system
    reservations:
      memory: 2G
```

### 3. Deploy Services
```bash
# Production deployment
docker-compose -f docker-compose.prod.yml up -d

# Check service health
docker-compose -f docker-compose.prod.yml ps
docker-compose -f docker-compose.prod.yml logs api
```

### 4. Verify Music Generation
```bash
# Test the API
curl -f http://localhost:8000/v1/audio/music/info

# Check model loading in logs
docker-compose -f docker-compose.prod.yml logs api | grep -i musicgen
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Model Loading Fails
```bash
# Check available memory
docker stats

# Check logs for specific errors
docker-compose logs api | grep -i "musicgen\|error"

# Increase memory limits if needed
```

#### 2. Audio Generation Timeout
```bash
# Check if model is loaded
docker-compose exec api python -c "
from app.services.audio.music_generation import music_generation_service
print('Service available:', music_generation_service is not None)
"
```

#### 3. Storage Issues
```bash
# Check disk space
df -h

# Clean up temporary files
docker-compose exec api find /app/temp -name "*.wav" -mtime +1 -delete
```

### Performance Optimization

#### 1. Model Caching
- Models are cached in `/root/.cache/huggingface`
- First generation takes longer (model download)
- Subsequent generations are faster

#### 2. Memory Management
- Monitor memory usage during generation
- Adjust `memory` limits in docker-compose
- Consider using swap if RAM is limited

#### 3. CPU Optimization
- Set `TORCH_NUM_THREADS` based on CPU cores
- Use `--cpus` limit in docker-compose if needed

## 📊 Monitoring

### Health Checks
```bash
# API health
curl -f http://localhost:8000/health

# Service status
docker-compose ps

# Resource usage
docker stats
```

### Logs
```bash
# Application logs
docker-compose logs -f api

# Music generation specific logs
docker-compose logs api | grep -i musicgen
```

## 🔒 Security Considerations

### Production Checklist
- [ ] Change default passwords in `.env`
- [ ] Use strong JWT secret keys
- [ ] Configure proper firewall rules
- [ ] Enable log rotation
- [ ] Set up monitoring and alerting
- [ ] Regular security updates

### Network Security
```yaml
# Expose only necessary ports
ports:
  - "127.0.0.1:8000:8000"  # Bind to localhost only

# Use reverse proxy (nginx/traefik) for HTTPS
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Music generation is CPU/memory intensive
- Consider dedicated music generation workers
- Use load balancer for multiple instances

### Vertical Scaling
- Increase memory for larger models (future)
- Add GPU support for faster generation
- Optimize storage for model caching

---

For additional support, check the application logs and refer to the main README.md file.
