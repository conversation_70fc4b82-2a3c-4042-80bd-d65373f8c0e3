#!/usr/bin/env python3
"""
Test script to validate music generation optimizations.
This script tests the improved timeout handling, model loading, and performance.
"""
import os
import sys
import asyncio
import time
import logging
from typing import Dict, Any

# Add the app directory to the path
sys.path.append('/app')

try:
    from app.services.audio.music_generation import music_generation_service
    SERVICE_AVAILABLE = True
except ImportError as e:
    SERVICE_AVAILABLE = False
    import_error = str(e)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MusicGenerationOptimizationTester:
    """Test suite for music generation optimizations"""
    
    def __init__(self):
        self.test_results = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all optimization tests"""
        logger.info("🧪 Starting music generation optimization tests...")
        
        if not SERVICE_AVAILABLE:
            logger.error(f"❌ Music generation service not available: {import_error}")
            return {"status": "failed", "error": "Service not available"}
        
        tests = [
            ("health_check", self.test_health_check),
            ("warmup_performance", self.test_warmup_performance),
            ("timeout_configuration", self.test_timeout_configuration),
            ("concurrent_loading", self.test_concurrent_loading),
            ("token_optimization", self.test_token_optimization),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"🔬 Running test: {test_name}")
            try:
                start_time = time.time()
                result = await test_func()
                end_time = time.time()
                
                self.test_results[test_name] = {
                    "status": "passed" if result.get("success") else "failed",
                    "duration": round(end_time - start_time, 2),
                    "details": result
                }
                
                if result.get("success"):
                    logger.info(f"✅ {test_name}: PASSED ({self.test_results[test_name]['duration']}s)")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"💥 {test_name}: EXCEPTION - {str(e)}")
                self.test_results[test_name] = {
                    "status": "exception",
                    "duration": 0,
                    "error": str(e)
                }
        
        # Generate summary
        passed = sum(1 for result in self.test_results.values() if result["status"] == "passed")
        total = len(self.test_results)
        
        summary = {
            "overall_status": "passed" if passed == total else "failed",
            "tests_passed": passed,
            "tests_total": total,
            "results": self.test_results
        }
        
        logger.info(f"📊 Test Summary: {passed}/{total} tests passed")
        return summary
    
    async def test_health_check(self) -> Dict[str, Any]:
        """Test health check functionality"""
        try:
            health = await music_generation_service.health_check()
            
            required_fields = ["service", "status", "dependencies", "cached_models", "warmup_started"]
            missing_fields = [field for field in required_fields if field not in health]
            
            if missing_fields:
                return {
                    "success": False,
                    "error": f"Missing health check fields: {missing_fields}",
                    "health_data": health
                }
            
            return {
                "success": True,
                "health_data": health,
                "cached_models": len(health.get("cached_models", [])),
                "dependencies_ok": health.get("dependencies", {}).get("transformers", False)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_warmup_performance(self) -> Dict[str, Any]:
        """Test model warmup performance"""
        try:
            model_size = os.environ.get('MUSICGEN_MODEL_SIZE', 'small')
            
            # Test warmup
            warmup_result = await music_generation_service.warm_up_model(model_size)
            
            if not warmup_result.get("success"):
                return {
                    "success": False,
                    "error": f"Warmup failed: {warmup_result.get('error')}",
                    "warmup_result": warmup_result
                }
            
            load_time = warmup_result.get("load_time", 0)
            
            # Check if load time is reasonable (under 5 minutes for cached model)
            max_expected_time = 300  # 5 minutes
            if load_time > max_expected_time:
                return {
                    "success": False,
                    "error": f"Warmup took too long: {load_time}s (expected < {max_expected_time}s)",
                    "load_time": load_time
                }
            
            return {
                "success": True,
                "load_time": load_time,
                "model_size": model_size,
                "cached": warmup_result.get("cached", False)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_timeout_configuration(self) -> Dict[str, Any]:
        """Test that timeout configurations are properly applied"""
        try:
            # Test different duration scenarios
            test_durations = [5, 10, 20]
            timeout_configs = {}
            
            for duration in test_durations:
                # Calculate expected timeout based on new configuration
                if duration <= 5:
                    expected_generation_timeout = max(duration * 60, 300)
                elif duration <= 10:
                    expected_generation_timeout = max(duration * 90, 600)
                else:
                    expected_generation_timeout = max(duration * 120, 1200)
                
                timeout_configs[f"{duration}s"] = {
                    "duration": duration,
                    "expected_generation_timeout": expected_generation_timeout
                }
            
            # Verify model loading timeout
            is_preloaded = os.environ.get('ENABLE_MODEL_PRELOAD', 'false').lower() == 'true'
            expected_model_timeout = 300 if is_preloaded else 1800
            
            return {
                "success": True,
                "timeout_configs": timeout_configs,
                "model_timeout": expected_model_timeout,
                "preload_enabled": is_preloaded
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_concurrent_loading(self) -> Dict[str, Any]:
        """Test concurrent model loading protection"""
        try:
            model_size = os.environ.get('MUSICGEN_MODEL_SIZE', 'small')
            
            # Clear cache first
            music_generation_service.clear_cache()
            
            # Start multiple concurrent warmup requests
            concurrent_requests = 3
            tasks = [
                music_generation_service.warm_up_model(model_size)
                for _ in range(concurrent_requests)
            ]
            
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Check results
            successful_results = [r for r in results if isinstance(r, dict) and r.get("success")]
            failed_results = [r for r in results if not (isinstance(r, dict) and r.get("success"))]
            
            # All should succeed (due to concurrent loading protection)
            if len(successful_results) != concurrent_requests:
                return {
                    "success": False,
                    "error": f"Expected {concurrent_requests} successes, got {len(successful_results)}",
                    "successful": len(successful_results),
                    "failed": len(failed_results),
                    "total_time": round(end_time - start_time, 2)
                }
            
            return {
                "success": True,
                "concurrent_requests": concurrent_requests,
                "all_succeeded": len(successful_results),
                "total_time": round(end_time - start_time, 2),
                "avg_time_per_request": round((end_time - start_time) / concurrent_requests, 2)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_token_optimization(self) -> Dict[str, Any]:
        """Test token calculation optimization"""
        try:
            # Test token calculations for different durations
            test_cases = [
                {"duration": 3, "expected_max": 160, "rate": 32},
                {"duration": 8, "expected_max": 250, "rate": 25},
                {"duration": 15, "expected_max": 360, "rate": 18},
                {"duration": 25, "expected_max": 400, "rate": 12}
            ]
            
            token_results = {}
            
            for case in test_cases:
                duration = case["duration"]
                expected_rate = case["rate"]
                expected_max = case["expected_max"]
                
                # Calculate tokens using same logic as in the service
                if duration <= 5:
                    calculated_tokens = min(duration * 32, 160)
                elif duration <= 10:
                    calculated_tokens = min(duration * 25, 250)
                elif duration <= 20:
                    calculated_tokens = min(duration * 18, 360)
                else:
                    calculated_tokens = min(duration * 12, 400)
                
                token_results[f"{duration}s"] = {
                    "duration": duration,
                    "calculated_tokens": calculated_tokens,
                    "expected_max": expected_max,
                    "rate_used": calculated_tokens / duration,
                    "expected_rate": expected_rate,
                    "within_expected": calculated_tokens <= expected_max
                }
            
            # Check if all calculations are within expected bounds
            all_within_bounds = all(
                result["within_expected"] for result in token_results.values()
            )
            
            return {
                "success": all_within_bounds,
                "token_calculations": token_results,
                "optimization_applied": True
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

async def main():
    """Main test runner"""
    tester = MusicGenerationOptimizationTester()
    results = await tester.run_all_tests()
    
    print("\n" + "="*60)
    print("🎵 MUSIC GENERATION OPTIMIZATION TEST RESULTS")
    print("="*60)
    
    if results.get("overall_status") == "passed":
        print(f"✅ ALL TESTS PASSED ({results['tests_passed']}/{results['tests_total']})")
    else:
        print(f"❌ TESTS FAILED ({results['tests_passed']}/{results['tests_total']})")
    
    print("\nDetailed Results:")
    for test_name, result in results.get("results", {}).items():
        status_emoji = "✅" if result["status"] == "passed" else "❌"
        print(f"{status_emoji} {test_name}: {result['status']} ({result.get('duration', 0)}s)")
        
        if result["status"] != "passed" and "error" in result:
            print(f"   Error: {result['error']}")
    
    print("\n" + "="*60)
    
    # Exit with appropriate code
    if results.get("overall_status") == "passed":
        print("🎉 All optimizations are working correctly!")
        sys.exit(0)
    else:
        print("⚠️ Some optimizations need attention. Check the logs above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())