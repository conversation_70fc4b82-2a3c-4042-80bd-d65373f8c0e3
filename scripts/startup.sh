#!/bin/bash
set -e

echo "🚀 Starting Ouinhi API..."

# Wait for services
timeout=30
counter=0
while ! pg_isready -h postgres -p 5432 -U postgres >/dev/null 2>&1; do
    sleep 1
    counter=$((counter + 1))
    if [ $counter -gt $timeout ]; then
        echo "❌ PostgreSQL timeout"
        exit 1
    fi
done

counter=0
while ! redis-cli -h redis -p 6379 -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; do
    sleep 1
    counter=$((counter + 1))
    if [ $counter -gt $timeout ]; then
        echo "❌ Redis timeout"
        exit 1
    fi
done

# Initialize services
./scripts/init-music.sh >/dev/null 2>&1

# Pre-load models for production optimization
echo "🚀 Pre-loading models for production optimization..."
python scripts/preload_models.py

INIT_LOCK_FILE="/tmp/ouinhi_initialized"
if [ ! -f "$INIT_LOCK_FILE" ]; then
    cd /app
    python scripts/init_database.py >/dev/null 2>&1
    touch "$INIT_LOCK_FILE"
fi

WORKER_COUNT=1
if [ "${DEBUG:-false}" = "true" ]; then
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --no-access-log
else
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers $WORKER_COUNT --no-access-log
fi