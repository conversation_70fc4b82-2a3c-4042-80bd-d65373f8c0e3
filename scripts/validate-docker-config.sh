#!/bin/bash

# Docker Configuration Validation Script for Music Generation
# This script validates that the Docker setup has everything needed for production deployment

set -e

echo "🔍 Validating Docker Configuration for Music Generation..."
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# Check Docker and Docker Compose
echo "📋 Checking Prerequisites..."
echo "--------------------------------"

docker --version > /dev/null 2>&1
print_status $? "Docker is installed"

docker-compose --version > /dev/null 2>&1 || docker compose version > /dev/null 2>&1
print_status $? "Docker Compose is available"

# Check if Docker daemon is running
docker info > /dev/null 2>&1
print_status $? "Docker daemon is running"

echo ""

# Check system resources
echo "💻 Checking System Resources..."
echo "--------------------------------"

# Check available memory
TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
if [ "$TOTAL_MEM" -ge 8 ]; then
    print_status 0 "Memory: ${TOTAL_MEM}GB (sufficient for music generation)"
elif [ "$TOTAL_MEM" -ge 4 ]; then
    print_warning "Memory: ${TOTAL_MEM}GB (minimum, may affect performance)"
else
    print_status 1 "Memory: ${TOTAL_MEM}GB (insufficient, need at least 4GB)"
fi

# Check available disk space
DISK_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
if [ "$DISK_SPACE" -ge 20 ]; then
    print_status 0 "Disk space: ${DISK_SPACE}GB available"
else
    print_status 1 "Disk space: ${DISK_SPACE}GB (need at least 20GB)"
fi

# Check CPU cores
CPU_CORES=$(nproc)
if [ "$CPU_CORES" -ge 4 ]; then
    print_status 0 "CPU cores: $CPU_CORES (sufficient)"
else
    print_warning "CPU cores: $CPU_CORES (minimum, may affect performance)"
fi

echo ""

# Check Docker configuration files
echo "📄 Checking Configuration Files..."
echo "--------------------------------"

[ -f "Dockerfile" ]
print_status $? "Dockerfile exists"

[ -f "docker-compose.yml" ]
print_status $? "docker-compose.yml exists"

[ -f "docker-compose.prod.yml" ]
print_status $? "docker-compose.prod.yml exists"

[ -f "requirements.txt" ]
print_status $? "requirements.txt exists"

echo ""

# Check requirements.txt for music generation dependencies
echo "🎵 Checking Music Generation Dependencies..."
echo "--------------------------------"

grep -q "audiocraft" requirements.txt
print_status $? "audiocraft dependency found"

grep -q "torch" requirements.txt
print_status $? "torch dependency found"

grep -q "torchaudio" requirements.txt
print_status $? "torchaudio dependency found"

echo ""

# Check Dockerfile for system dependencies
echo "🔧 Checking System Dependencies in Dockerfile..."
echo "--------------------------------"

grep -q "pkg-config" Dockerfile
print_status $? "pkg-config dependency found"

grep -q "libavformat-dev" Dockerfile
print_status $? "FFmpeg development libraries found"

grep -q "libsndfile1-dev" Dockerfile
print_status $? "Audio processing libraries found"

echo ""

# Check environment configuration
echo "🌍 Checking Environment Configuration..."
echo "--------------------------------"

if [ -f ".env" ]; then
    print_status 0 ".env file exists"
    
    # Check for required environment variables
    if grep -q "MUSICGEN_MODEL_SIZE" .env; then
        MODEL_SIZE=$(grep "MUSICGEN_MODEL_SIZE" .env | cut -d'=' -f2)
        if [ "$MODEL_SIZE" = "small" ]; then
            print_status 0 "MUSICGEN_MODEL_SIZE set to 'small' (recommended)"
        else
            print_warning "MUSICGEN_MODEL_SIZE set to '$MODEL_SIZE' (only 'small' supported in simplified implementation)"
        fi
    else
        print_warning "MUSICGEN_MODEL_SIZE not set (will default to 'small')"
    fi
    
    if grep -q "LOCAL_STORAGE_PATH" .env; then
        print_status 0 "LOCAL_STORAGE_PATH configured"
    else
        print_warning "LOCAL_STORAGE_PATH not set (will default to /app/temp)"
    fi
else
    print_warning ".env file not found (copy from .env.example)"
fi

echo ""

# Check Docker Compose configuration
echo "🐳 Checking Docker Compose Configuration..."
echo "--------------------------------"

# Check if temp_storage volume is configured
grep -q "temp_storage" docker-compose.yml
print_status $? "temp_storage volume configured in docker-compose.yml"

grep -q "temp_storage" docker-compose.prod.yml
print_status $? "temp_storage volume configured in docker-compose.prod.yml"

# Check memory limits
if grep -q "memory:" docker-compose.prod.yml; then
    MEMORY_LIMIT=$(grep -A1 "limits:" docker-compose.prod.yml | grep "memory:" | awk '{print $2}')
    print_status 0 "Memory limit configured: $MEMORY_LIMIT"
else
    print_warning "No memory limits configured in production"
fi

echo ""

# Test Docker build (dry run)
echo "🏗️  Testing Docker Build..."
echo "--------------------------------"

print_info "Testing Docker build (this may take a few minutes)..."

if docker build --target base -t ouinhi-api-test . > /dev/null 2>&1; then
    print_status 0 "Docker build test successful"
    docker rmi ouinhi-api-test > /dev/null 2>&1 || true
else
    print_status 1 "Docker build test failed"
fi

echo ""

# Summary
echo "📊 Validation Summary"
echo "================================"

print_info "Configuration validation complete!"
echo ""
print_info "Next steps:"
echo "  1. If any issues were found, address them before deployment"
echo "  2. Copy .env.example to .env and configure your environment"
echo "  3. For development: docker-compose up --build"
echo "  4. For production: docker-compose -f docker-compose.prod.yml up -d"
echo ""
print_info "For detailed deployment instructions, see DOCKER_DEPLOYMENT.md"

echo ""
echo "🎉 Ready for deployment!"
