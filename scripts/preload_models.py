#!/usr/bin/env python3
"""
Model pre-loading script for production optimization.
This script downloads and caches models during container startup to reduce cold start times.
"""
import os
import sys
import logging
import asyncio
import time
from typing import Dict, Any, TYPE_CHECKING
from pathlib import Path

# Add the app directory to the path
sys.path.append('/app')

# Type checking imports
if TYPE_CHECKING:
    from transformers.pipelines import pipeline
    import torch

# Try to import dependencies with graceful fallback
try:
    from transformers.pipelines import pipeline
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError as e:
    TRANSFORMERS_AVAILABLE = False
    missing_deps = str(e)
    # Create stub objects to prevent "possibly unbound" warnings
    pipeline = None
    torch = None

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModelPreloader:
    """Pre-loads models during container startup for better production performance"""
    
    def __init__(self):
        self.models_to_preload = self._get_models_config()
        self.cache_dir = os.environ.get('TRANSFORMERS_CACHE', '/root/.cache/huggingface')
        
        # Ensure cache directory exists
        os.makedirs(self.cache_dir, exist_ok=True)
        
    def _get_models_config(self) -> Dict[str, Dict[str, Any]]:
        """Define which models to pre-load based on environment configuration"""
        models = {}
        
        # MusicGen models - only if transformers is available
        if TRANSFORMERS_AVAILABLE:
            # Default to small model for faster startup, can be overridden via env var
            musicgen_model_size = os.environ.get('MUSICGEN_MODEL_SIZE', 'small')
            models[f'facebook/musicgen-stereo-{musicgen_model_size}'] = {
                'task': 'text-to-audio',
                'device': -1,  # CPU
                'priority': 1
            }
            
            # Optional: Pre-load larger models if specified
            if os.environ.get('PRELOAD_MUSICGEN_MEDIUM', 'false').lower() == 'true':
                models['facebook/musicgen-stereo-medium'] = {
                    'task': 'text-to-audio',
                    'device': -1,
                    'priority': 2
                }
                
            if os.environ.get('PRELOAD_MUSICGEN_LARGE', 'false').lower() == 'true':
                models['facebook/musicgen-stereo-large'] = {
                    'task': 'text-to-audio',
                    'device': -1,
                    'priority': 3
                }
        
        return models
    
    def preload_model(self, model_name: str, config: Dict[str, Any]) -> bool:
        """Pre-load a single model with enhanced error handling and progress tracking"""
        start_time = time.time()  # Initialize start_time before try block
        try:
            logger.info(f"🚀 Pre-loading model: {model_name} (Priority: {config.get('priority', 'N/A')})")
            
            # Check if pipeline is available
            if pipeline is None:
                logger.error(f"❌ Pipeline not available for {model_name} - transformers not properly imported")
                return False
            
            # Set cache directory as environment variable (transformers uses this)
            original_cache = os.environ.get('TRANSFORMERS_CACHE')
            os.environ['TRANSFORMERS_CACHE'] = self.cache_dir
            
            try:
                # Load model with specified configuration and progress tracking
                logger.info(f"📥 Downloading/loading model files for {model_name}...")
                model = pipeline(
                    config['task'],
                    model_name,
                    device=config['device'],
                    torch_dtype="auto",  # Let transformers choose optimal dtype
                    use_fast=True  # Use fast tokenizers when available
                )
                
                elapsed_time = time.time() - start_time
                logger.info(f"✅ Successfully pre-loaded: {model_name} in {elapsed_time:.1f}s")
            finally:
                # Restore original cache setting
                if original_cache is not None:
                    os.environ['TRANSFORMERS_CACHE'] = original_cache
                elif 'TRANSFORMERS_CACHE' in os.environ:
                    del os.environ['TRANSFORMERS_CACHE']
            
            # Clean up the pipeline object to free memory
            del model
            
            # Force garbage collection to free memory immediately
            import gc
            gc.collect()
            
            return True
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ Failed to pre-load {model_name} after {elapsed_time:.1f}s: {str(e)}")
            
            # Specific error handling for common issues
            if "timeout" in str(e).lower():
                logger.error(f"💡 Suggestion: Increase timeout or check network connectivity")
            elif "memory" in str(e).lower() or "oom" in str(e).lower():
                logger.error(f"💡 Suggestion: Try a smaller model size or increase available memory")
            elif "disk" in str(e).lower() or "space" in str(e).lower():
                logger.error(f"💡 Suggestion: Check available disk space in cache directory: {self.cache_dir}")
            
            return False
    
    def preload_all_models(self) -> Dict[str, bool]:
        """Pre-load all configured models with progress tracking"""
        if not TRANSFORMERS_AVAILABLE:
            logger.warning(f"🚨 Transformers not available: {missing_deps}")
            logger.info("📦 Skipping model pre-loading")
            return {}
        
        logger.info("🎵 Starting model pre-loading for production optimization...")
        logger.info(f"📁 Cache directory: {self.cache_dir}")
        logger.info(f"📋 Models to preload: {len(self.models_to_preload)}")
        
        # Log available disk space
        try:
            import shutil
            total, _, free = shutil.disk_usage(self.cache_dir)
            free_gb = free // (1024**3)
            logger.info(f"💾 Available disk space: {free_gb}GB")
        except Exception:
            pass
        
        results = {}
        
        # Sort models by priority
        sorted_models = sorted(
            self.models_to_preload.items(),
            key=lambda x: x[1].get('priority', 999)
        )
        
        for model_name, config in sorted_models:
            results[model_name] = self.preload_model(model_name, config)
        
        # Summary with detailed results
        successful = sum(results.values())
        total = len(results)
        
        if successful > 0:
            logger.info(f"✅ Model pre-loading completed: {successful}/{total} models loaded successfully")
            
            # Log which models were successful
            successful_models = [name for name, success in results.items() if success]
            failed_models = [name for name, success in results.items() if not success]
            
            if successful_models:
                logger.info(f"🎉 Successfully loaded: {', '.join(successful_models)}")
            if failed_models:
                logger.warning(f"❌ Failed to load: {', '.join(failed_models)}")
        else:
            logger.warning("⚠️ No models were successfully pre-loaded")
            
        return results

def main():
    """Main entry point for model pre-loading"""
    logger.info("🚀 Initializing model pre-loading service...")
    
    # Check if pre-loading is enabled
    if os.environ.get('ENABLE_MODEL_PRELOAD', 'true').lower() != 'true':
        logger.info("📦 Model pre-loading disabled via environment variable")
        return
    
    # Initialize preloader
    preloader = ModelPreloader()
    
    # Pre-load models
    results = preloader.preload_all_models()
    
    # Exit with appropriate code
    if results and any(results.values()):
        logger.info("🎉 Model pre-loading service completed successfully")
        sys.exit(0)
    else:
        logger.warning("⚠️ Model pre-loading service completed with warnings")
        # Don't fail container startup if pre-loading fails
        sys.exit(0)

if __name__ == "__main__":
    main()
