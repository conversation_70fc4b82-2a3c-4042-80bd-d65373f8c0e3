#!/bin/bash
"""
Quick validation script for music generation optimizations.
This script checks that the optimizations are properly configured.
"""

echo "🔍 Validating Music Generation Optimizations..."
echo "================================================"

# Check if optimization files exist
echo "📁 Checking optimization files..."

if [ -f "app/services/audio/music_generation.py" ]; then
    echo "✅ Music generation service found"
else
    echo "❌ Music generation service not found"
    exit 1
fi

if [ -f "scripts/preload_models.py" ]; then
    echo "✅ Preload script found"
else
    echo "❌ Preload script not found"
    exit 1
fi

if [ -f "MUSIC_GENERATION_OPTIMIZATIONS.md" ]; then
    echo "✅ Optimization documentation found"
else
    echo "❌ Optimization documentation not found"
    exit 1
fi

# Check for key optimization features in the code
echo ""
echo "🔬 Checking optimization implementation..."

# Check for improved timeouts
if grep -q "generation_timeout = max(duration \* 60, 300)" app/services/audio/music_generation.py; then
    echo "✅ Enhanced timeout configuration implemented"
else
    echo "⚠️  Enhanced timeout configuration may not be implemented"
fi

# Check for background warmup
if grep -q "_background_warmup" app/services/audio/music_generation.py; then
    echo "✅ Background model warmup implemented"
else
    echo "⚠️  Background model warmup may not be implemented"
fi

# Check for concurrent loading protection
if grep -q "model_loading_lock" app/services/audio/music_generation.py; then
    echo "✅ Concurrent loading protection implemented"
else
    echo "⚠️  Concurrent loading protection may not be implemented"
fi

# Check for optimized token calculation
if grep -q "duration \* 32, 160" app/services/audio/music_generation.py; then
    echo "✅ Optimized token calculation implemented"
else
    echo "⚠️  Optimized token calculation may not be implemented"
fi

# Check for health monitoring
if grep -q "health_check" app/services/audio/music_generation.py; then
    echo "✅ Health monitoring implemented"
else
    echo "⚠️  Health monitoring may not be implemented"
fi

# Check preload script enhancements
if grep -q "elapsed_time" scripts/preload_models.py; then
    echo "✅ Enhanced preload script with timing"
else
    echo "⚠️  Enhanced preload script timing may not be implemented"
fi

# Check environment configuration
echo ""
echo "⚙️  Checking environment configuration..."

# Check Docker Compose configuration
if grep -q "TRANSFORMERS_CACHE" docker-compose.yml; then
    echo "✅ TRANSFORMERS_CACHE configured in Docker Compose"
else
    echo "⚠️  TRANSFORMERS_CACHE not found in Docker Compose"
fi

if grep -q "MUSICGEN_MODEL_SIZE" docker-compose.yml; then
    echo "✅ MUSICGEN_MODEL_SIZE configured in Docker Compose"
else
    echo "⚠️  MUSICGEN_MODEL_SIZE not found in Docker Compose"
fi

# Count total optimizations
echo ""
echo "📊 Optimization Summary:"
echo "========================"

total_checks=7
passed_checks=0

# Re-run checks and count
if grep -q "generation_timeout = max(duration \* 60, 300)" app/services/audio/music_generation.py; then
    ((passed_checks++))
fi

if grep -q "_background_warmup" app/services/audio/music_generation.py; then
    ((passed_checks++))
fi

if grep -q "model_loading_lock" app/services/audio/music_generation.py; then
    ((passed_checks++))
fi

if grep -q "duration \* 32, 160" app/services/audio/music_generation.py; then
    ((passed_checks++))
fi

if grep -q "health_check" app/services/audio/music_generation.py; then
    ((passed_checks++))
fi

if grep -q "elapsed_time" scripts/preload_models.py; then
    ((passed_checks++))
fi

if grep -q "TRANSFORMERS_CACHE" docker-compose.yml; then
    ((passed_checks++))
fi

echo "✅ Optimizations implemented: $passed_checks/$total_checks"

if [ $passed_checks -eq $total_checks ]; then
    echo "🎉 All optimizations successfully implemented!"
    echo ""
    echo "📝 Next steps:"
    echo "1. Deploy with Docker Compose to test in production"
    echo "2. Monitor logs for improved performance"
    echo "3. Run the optimization test script inside the container"
    echo ""
    echo "📋 Test command (inside container):"
    echo "   python scripts/test_music_generation_optimizations.py"
    exit 0
else
    echo "⚠️  Some optimizations may need attention."
    echo "   Check the warnings above and review the implementation."
    exit 1
fi