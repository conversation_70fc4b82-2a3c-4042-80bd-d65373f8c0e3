import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  MCPRequest,
  MCPResponse,
  Job,
  CreateVideoParams,
  FootageToVideoRequest,
  AiimageToVideoRequest,
  ApiResponse
} from '../types/ouinhi';

// Create axios instance
export const apiClient: AxiosInstance = axios.create({
  baseURL: '',
  timeout: 30000,
  // Don't set Content-Type here - let axios handle it automatically
});

// Request interceptor to add API key and set content type for non-FormData requests
apiClient.interceptors.request.use(
  (config) => {
    const apiKey = localStorage.getItem('ouinhi_api_key');
    if (apiKey) {
      config.headers['X-API-Key'] = apiKey;
    }
    
    // Set Content-Type to application/json only if it's not FormData
    if (config.data && !(config.data instanceof FormData) && !config.headers['Content-Type']) {
      config.headers['Content-Type'] = 'application/json';
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      // Handle unauthorized access
      localStorage.removeItem('ouinhi_api_key');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// MCP API Functions
export const mcpApi = {
  // Send MCP message
  sendMessage: async (request: MCPRequest): Promise<MCPResponse> => {
    const response = await apiClient.post('/api/mcp/messages', request);
    return response.data;
  },

  // Initialize MCP connection
  initialize: async (): Promise<MCPResponse> => {
    return mcpApi.sendMessage({
      jsonrpc: '2.0',
      id: 'init',
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {},
        clientInfo: {
          name: 'Ouinhi Frontend',
          version: '1.0.0'
        }
      }
    });
  },

  // List available tools
  listTools: async (): Promise<MCPResponse> => {
    return mcpApi.sendMessage({
      jsonrpc: '2.0',
      id: 'list-tools',
      method: 'tools/list'
    });
  },

  // Create short video via MCP
  createShortVideo: async (params: CreateVideoParams): Promise<MCPResponse> => {
    return mcpApi.sendMessage({
      jsonrpc: '2.0',
      id: `create-video-${Date.now()}`,
      method: 'tools/call',
      params: {
        name: 'create-short-video',
        arguments: params
      }
    });
  },

  // Get video status via MCP
  getVideoStatus: async (jobId: string): Promise<MCPResponse> => {
    return mcpApi.sendMessage({
      jsonrpc: '2.0',
      id: `status-${jobId}`,
      method: 'tools/call',
      params: {
        name: 'get-video-status',
        arguments: {
          job_id: jobId
        }
      }
    });
  },

  // List TTS voices via MCP
  listTTSVoices: async (provider?: string, language?: string): Promise<MCPResponse> => {
    const params: Record<string, string> = {};
    if (provider) params.provider = provider;
    if (language) params.language = language;

    return mcpApi.sendMessage({
      jsonrpc: '2.0',
      id: 'list-voices',
      method: 'tools/call',
      params: {
        name: 'list-tts-voices',
        arguments: params
      }
    });
  },

  // Validate voice combination via MCP
  validateVoiceCombination: async (voiceName: string, provider: string): Promise<MCPResponse> => {
    return mcpApi.sendMessage({
      jsonrpc: '2.0',
      id: 'validate-voice',
      method: 'tools/call',
      params: {
        name: 'validate-voice-combination',
        arguments: {
          voice_name: voiceName,
          provider
        }
      }
    });
  }
};

// Direct API Functions (for non-MCP endpoints)
export const directApi = {
  // Generate Script (AI Script Generation) - moved to top for proper function declaration order
  generateScript: async (params: {
    topic?: string;
    auto_topic?: boolean;
    language?: string;
    script_provider?: string;
    script_type?: string;
    max_duration?: number;
    target_words?: number;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    const response = await apiClient.post('/api/v1/ai/script/generate', params);
    
    if (response.data && response.data.job_id) {
      return {
        success: true,
        data: response.data
      };
    }
    
    return {
      success: false,
      error: 'Invalid response format'
    };
  },

  // Generate Script Synchronously (AI Script Generation) - for immediate results
  generateScriptSync: async (params: {
    topic?: string;
    auto_topic?: boolean;
    language?: string;
    script_provider?: string;
    script_type?: string;
    max_duration?: number;
    target_words?: number;
  }): Promise<ApiResponse<{ script: string; word_count: number; estimated_duration: number; provider_used: string }>> => {
    // Increase timeout for script generation as it can take longer
    const response = await apiClient.post('/api/v1/ai/script/generate/sync', params, { timeout: 60000 }); // 60 seconds
    
    // Handle the response format - it's an array with one object
    const responseData = Array.isArray(response.data) ? response.data[0] : response.data;
    
    // The sync endpoint returns the script directly in the response
    if (responseData && responseData.success && responseData.data) {
      return {
        success: true,
        data: {
          script: responseData.data.script || '',
          word_count: responseData.data.word_count || 0,
          estimated_duration: responseData.data.estimated_duration || 0,
          provider_used: responseData.data.provider_used || 'unknown'
        }
      };
    }
    
    return {
      success: false,
      error: responseData?.error || 'Invalid response format'
    };
  },

  // Topic to Video Pipeline (Stock Videos)
  footageToVideo: async (params: FootageToVideoRequest): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/v1/ai/footage-to-video', params);
      
      // Return the job_id in the expected format
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to create video'
        : 'Failed to create video';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Script to Video Pipeline (AI Images)
  aiimageToVideo: async (params: AiimageToVideoRequest): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/v1/ai/aiimage-to-video', params);
      
      // Return the job_id in the expected format
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to create video with AI images'
        : 'Failed to create video with AI images';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Research Topic
  researchTopic: async (searchTerm: string, targetLanguage: string): Promise<ApiResponse<{ title: string, content: string, sources: string[], language: string }>> => {
    try {
      const response = await apiClient.post('/api/v1/ai/research-topic', {
        searchTerm,
        targetLanguage
      });
      
      if (response.data && response.data.job_id) {
        // Poll for completion using direct API
        const jobId = response.data.job_id;
        
        // Poll until completion
        let attempts = 0;
        const maxAttempts = 60; // 5 minutes max
        while (attempts < maxAttempts) {
          const statusResponse = await directApi.getJobStatus(jobId);
          if (!statusResponse.success) {
            throw new Error(statusResponse.error || 'Failed to get job status');
          }
          
          const job = statusResponse.data;
          if (!job) {
            throw new Error('Invalid job status response');
          }
          
          if (job.status === 'completed' && job.result) {
            // Extract research data from job result
            const researchData = {
              title: `Research: ${searchTerm}`,
              content: job.result.summary || '',
              sources: job.result.articles?.map((article: { source?: string }) => article.source || 'Unknown') || [],
              language: targetLanguage
            };
            
            return {
              success: true,
              data: researchData
            };
          } else if (job.status === 'failed') {
            throw new Error(job.error || 'Research failed');
          }
          
          // Wait before next poll
          await new Promise(resolve => setTimeout(resolve, 5000));
          attempts++;
        }
        
        throw new Error('Research job timed out');
      }
      
      throw new Error('Invalid response from research API');
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'message' in error
        ? (error as { message: string }).message
        : 'Failed to research topic';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Video Search Query Generation
  generateVideoSearchQueries: async (params: {
    script: string;
    segment_duration?: number;
    provider?: string;
    language?: string;
  }): Promise<ApiResponse<{
    queries: Array<{
      query: string;
      start_time: number;
      end_time: number;
      duration: number;
      visual_concept: string;
    }>;
    total_duration: number;
    total_segments: number;
    provider_used: string;
  }>> => {
    const response = await apiClient.post('/api/v1/ai/video-search/generate-queries', params);
    
    if (response.data) {
      return {
        success: true,
        data: response.data
      };
    }
    
    return {
      success: false,
      error: 'Invalid response format'
    };
  },

  // Image Search
  searchStockImages: async (params: {
    query: string;
    orientation?: 'landscape' | 'portrait' | 'square';
    quality?: 'standard' | 'high' | 'ultra';
    per_page?: number;
    color?: string;
    size?: 'large' | 'medium' | 'small';
    provider?: 'pexels' | 'pixabay';
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/v1/ai/image-search/stock-images', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to search stock images'
        : 'Failed to search stock images';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Image Search Status
  getImageSearchStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      images: Array<{
        id: string;
        url: string;
        download_url: string;
        width: number;
        height: number;
        photographer?: string;
        photographer_url?: string;
        alt?: string;
        tags?: string;
        source: string;
        aspect_ratio: number;
      }>;
      total_results: number;
      page: number;
      per_page: number;
      query_used: string;
      provider_used: string;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/v1/ai/image-search/stock-images/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get image search status'
        : 'Failed to get image search status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Browse Images (with pagination)
  browseImages: async (params: {
    query: string;
    orientation?: 'landscape' | 'portrait' | 'square';
    quality?: 'standard' | 'high' | 'ultra';
    per_page?: number;
    page?: number;
    color?: string;
    size?: 'large' | 'medium' | 'small';
    provider?: 'pexels' | 'pixabay';
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/v1/ai/image-browse', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to browse images'
        : 'Failed to browse images';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Image Browse Status
  getImageBrowseStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      images: Array<{
        id: string;
        url: string;
        download_url: string;
        width: number;
        height: number;
        photographer?: string;
        photographer_url?: string;
        alt?: string;
        tags?: string;
        source: string;
        aspect_ratio: number;
      }>;
      total_results: number;
      page: number;
      per_page: number;
      query_used: string;
      provider_used: string;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/v1/ai/image-browse/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get image browse status'
        : 'Failed to get image browse status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Image Providers Status
  getImageProvidersStatus: async (): Promise<ApiResponse<{
    providers: {
      [key: string]: {
        available: boolean;
        name: string;
        description: string;
        features: string[];
      };
    };
    supported_orientations: string[];
    supported_qualities: string[];
    supported_colors: string[];
    supported_sizes: string[];
  }>> => {
    try {
      const response = await apiClient.get('/api/v1/ai/image-providers/status');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get image providers status'
        : 'Failed to get image providers status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Job Status (for any job type)
  getJobStatus: async (jobId: string): Promise<ApiResponse<Job>> => {
    try {
      const response = await apiClient.get(`/api/jobs/${jobId}/status`);
      
      // The jobs endpoint returns the response in the expected format already
      if (response.data && response.data.success !== undefined) {
        return response.data;
      }
      
      // For backwards compatibility, wrap if needed
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get job status'
        : 'Failed to get job status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // List user jobs
  listJobs: async (page = 1, limit = 20): Promise<ApiResponse<{ jobs: Job[], total: number }>> => {
    const response = await apiClient.get(`/api/jobs?page=${page}&limit=${limit}`);
    
    // The jobs endpoint returns the response in the expected format already
    if (response.data && response.data.success !== undefined) {
      return response.data;
    }
    
    // For backwards compatibility, wrap if needed
    return {
      success: true,
      data: response.data
    };
  },

  // Delete job
  deleteJob: async (jobId: string): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(`/api/jobs/${jobId}`);
      
      // The jobs endpoint returns the response in the expected format already
      if (response.data && response.data.success) {
        return {
          success: true,
          data: undefined
        };
      }
      
      // If success is false, return the error
      if (response.data && response.data.success === false) {
        return {
          success: false,
          error: response.data.message || 'Failed to delete job'
        };
      }
      
      // For backwards compatibility, assume success if we get a 200 response
      return {
        success: true,
        data: undefined
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to delete job'
        : 'Failed to delete job';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get TTS providers and voices (using correct backend endpoints)
  getTTSProviders: async (): Promise<ApiResponse<Record<string, unknown>>> => {
    const response = await apiClient.get('/api/v1/audio/tts/providers');
    return response.data;
  },

  // Get voices for provider (using correct backend endpoints)
  getVoicesForProvider: async (provider: string): Promise<ApiResponse<Record<string, unknown>>> => {
    const response = await apiClient.get(`/api/v1/audio/tts/${provider}/voices`);
    return response.data;
  },

  // Dashboard APIs
  getDashboardStats: async (): Promise<ApiResponse<{
    total_videos: number;
    active_jobs: number;
    completed_jobs: number;
    failed_jobs: number;
    total_users: number;
    active_api_keys: number;
    storage_used_gb?: number;
    storage_total_gb?: number;
    avg_processing_time_seconds?: number;
  }>> => {
    const response = await apiClient.get('/api/v1/dashboard/stats');
    return {
      success: true,
      data: response.data
    };
  },

  getRecentActivity: async (limit: number = 10): Promise<ApiResponse<Array<{
    id: string;
    type: string;
    title: string;
    timestamp: string;
    status: string;
    details?: string;
    operation?: string;
    progress?: number;
  }>>> => {
    const response = await apiClient.get(`/api/v1/dashboard/recent-activity?limit=${limit}`);
    return {
      success: true,
      data: response.data
    };
  },

  // Retry job
  retryJob: async (jobId: string): Promise<ApiResponse<void>> => {
    const response = await apiClient.post(`/api/jobs/${jobId}/retry`);
    
    if (response.data && response.data.success !== undefined) {
      return response.data;
    }
    
    return {
      success: true,
      data: response.data
    };
  },

  // Video Management APIs
  getVideos: async (params?: {
    page?: number;
    limit?: number;
    video_type?: string;
    search?: string;
  }): Promise<ApiResponse<{
    videos: Array<{
      id: string;
      title: string;
      description?: string;
      video_type: string;
      final_video_url: string;
      video_with_audio_url?: string;
      audio_url?: string;
      srt_url?: string;
      thumbnail_url?: string;
      duration_seconds?: number;
      resolution?: string;
      file_size_mb?: number;
      word_count?: number;
      segments_count?: number;
      script_text?: string;
      voice_provider?: string;
      voice_name?: string;
      language?: string;
      processing_time_seconds?: number;
      background_videos_used?: string[];
      tags?: string[];
      download_count: number;
      last_accessed?: string;
      created_at: string;
      updated_at: string;
    }>;
    total: number;
    page: number;
    limit: number;
  }>> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.video_type) queryParams.append('video_type', params.video_type);
    if (params?.search) queryParams.append('search', params.search);

    const response = await apiClient.get(`/api/v1/videos/?${queryParams.toString()}`);
    return {
      success: true,
      data: response.data
    };
  },

  getVideo: async (videoId: string) => {
    const response = await apiClient.get(`/api/v1/videos/${videoId}/`);
    return {
      success: true,
      data: response.data
    };
  },

  updateVideo: async (videoId: string, updates: {
    title?: string;
    description?: string;
    tags?: string[];
  }) => {
    const response = await apiClient.put(`/api/v1/videos/${videoId}/`, updates);
    return {
      success: true,
      data: response.data
    };
  },

  deleteVideo: async (videoId: string) => {
    const response = await apiClient.delete(`/api/v1/videos/${videoId}/`);
    return {
      success: true,
      data: response.data
    };
  },

  getVideoDownloadUrl: async (videoId: string, format: string = 'mp4') => {
    const response = await apiClient.get(`/api/v1/videos/${videoId}/download/?format=${format}`);
    return {
      success: true,
      data: response.data
    };
  },

  getVideoStats: async () => {
    const response = await apiClient.get('/api/v1/videos/stats/overview/');
    return {
      success: true,
      data: response.data
    };
  },


  // Unified Video Generation
  generateVideo: async (params: {
    prompt: string;
    provider?: string;
    negative_prompt?: string;
    width?: number;
    height?: number;
    num_frames?: number;
    num_inference_steps?: number;
    guidance_scale?: number;
    seed?: number;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/v1/videos/generate', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to generate LTX video'
        : 'Failed to generate video';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  generateVideoFromImage: async (params: {
    prompt: string;
    image: File;
    provider?: string;
    negative_prompt?: string;
    width?: number;
    height?: number;
    num_frames?: number;
    num_inference_steps?: number;
    guidance_scale?: number;
    seed?: number;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const formData = new FormData();
      formData.append('prompt', params.prompt);
      formData.append('image', params.image);
      if (params.provider) formData.append('provider', params.provider);
      if (params.negative_prompt) formData.append('negative_prompt', params.negative_prompt);
      if (params.width) formData.append('width', params.width.toString());
      if (params.height) formData.append('height', params.height.toString());
      if (params.num_frames) formData.append('num_frames', params.num_frames.toString());
      if (params.num_inference_steps) formData.append('num_inference_steps', params.num_inference_steps.toString());
      if (params.guidance_scale) formData.append('guidance_scale', params.guidance_scale.toString());
      if (params.seed) formData.append('seed', params.seed.toString());
      
      const response = await apiClient.post('/api/v1/videos/from_image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to generate LTX video from image'
        : 'Failed to generate LTX video from image';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  getVideoStatus: async (jobId: string): Promise<ApiResponse<Job>> => {
    try {
      const response = await apiClient.get(`/api/v1/videos/generate/${jobId}`);
      
      if (response.data) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get LTX video status'
        : 'Failed to get LTX video status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  getVideoFromImageStatus: async (jobId: string): Promise<ApiResponse<Job>> => {
    try {
      const response = await apiClient.get(`/api/v1/videos/from_image/${jobId}`);
      
      if (response.data) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get LTX video from image status'
        : 'Failed to get LTX video from image status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // WaveSpeed AI Video Generation
  generateWaveSpeedVideo: async (params: {
    prompt: string;
    model?: string;
    size?: string;
    duration?: number;
    seed?: number;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/v1/videos/wavespeed/generate', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to generate WaveSpeed video'
        : 'Failed to generate WaveSpeed video';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  generateWaveSpeedVideoFromImage: async (params: {
    prompt: string;
    image: File;
    seed?: number;
    model?: string;
    resolution?: string;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const formData = new FormData();
      formData.append('prompt', params.prompt);
      formData.append('image', params.image);
      if (params.seed !== undefined) formData.append('seed', params.seed.toString());
      if (params.model) formData.append('model', params.model);
      if (params.resolution) formData.append('resolution', params.resolution);
      
      const response = await apiClient.post('/api/v1/videos/wavespeed/image_to_video', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to generate WaveSpeed video from image'
        : 'Failed to generate WaveSpeed video from image';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  getWaveSpeedVideoStatus: async (jobId: string): Promise<ApiResponse<Job>> => {
    try {
      const response = await apiClient.get(`/api/v1/videos/wavespeed/generate/${jobId}`);
      
      if (response.data) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get WaveSpeed video status'
        : 'Failed to get WaveSpeed video status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  getWaveSpeedVideoFromImageStatus: async (jobId: string): Promise<ApiResponse<Job>> => {
    try {
      const response = await apiClient.get(`/api/v1/videos/wavespeed/image_to_video/${jobId}`);
      
      if (response.data) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { data?: { detail?: string } }; message?: string }).response?.data?.detail ||
          (error as { message?: string }).message ||
          'Failed to get WaveSpeed video from image status'
        : 'Failed to get WaveSpeed video from image status';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // POST method for axios requests
  post: async (url: string, data: Record<string, unknown> | FormData | string | number | boolean | null, config?: Record<string, unknown>): Promise<AxiosResponse> => {
    return await apiClient.post(url, data, config);
  },

  // GET method for axios requests
  get: async (url: string): Promise<AxiosResponse> => {
    return await apiClient.get(url);
  },

  // GET method with extended timeout for long-running operations like polling
  getLongTimeout: async (url: string, timeoutMs: number = 120000): Promise<AxiosResponse> => {
    return await apiClient.get(url, { timeout: timeoutMs });
  },

  // DELETE method for axios requests
  delete: async (url: string): Promise<AxiosResponse> => {
    return await apiClient.delete(url);
  }
};

// Pollinations API Functions
export const pollinationsApi = {
  // Image Generation
  generateImage: async (params: {
    prompt: string;
    model?: string;
    width?: number;
    height?: number;
    seed?: number;
    enhance?: boolean;
    nologo?: boolean;
    safe?: boolean;
    transparent?: boolean;
    image_url?: string;
    referrer?: string;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/pollinations/image/generate', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Image Generation Status
  getImageGenerationStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      content_url: string;
      content_type: string;
      file_size: number;
      generation_time: number;
      model_used: string;
      prompt: string;
      dimensions: string;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/pollinations/image/generate/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Vision Analysis
  analyzeImage: async (params: {
    image_url?: string;
    question?: string;
    model?: string;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/pollinations/vision/analyze', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Vision Analysis with Upload
  analyzeUploadedImage: async (file: File, question: string = "What's in this image?", model: string = "openai"): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('question', question);
      formData.append('model', model);

      const response = await apiClient.post('/api/pollinations/vision/analyze-upload', formData);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Vision Analysis Status
  getVisionAnalysisStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      text: string;
      model_used: string;
      generation_time: number;
      question: string;
      image_url?: string;
      file_name?: string;
      file_size?: number;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/pollinations/vision/analyze/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Text Generation
  generateText: async (params: {
    prompt: string;
    model?: string;
    seed?: number;
    temperature?: number;
    top_p?: number;
    presence_penalty?: number;
    frequency_penalty?: number;
    system?: string;
    json_mode?: boolean;
    referrer?: string;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/pollinations/text/generate', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Chat Completions
  createChatCompletion: async (params: {
    messages: Array<{
      role: string;
      content: string | Array<{ type: string; text?: string; image_url?: { url: string } }>;
    }>;
    model?: string;
    seed?: number;
    temperature?: number;
    top_p?: number;
    presence_penalty?: number;
    frequency_penalty?: number;
    json_mode?: boolean;
    tools?: Array<Record<string, unknown>>;
    tool_choice?: string | Record<string, unknown>;
    referrer?: string;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/pollinations/chat/completions', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Text Generation Status
  getTextGenerationStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      text?: string;
      response?: Record<string, unknown>;
      assistant_message?: string;
      model_used: string;
      generation_time: number;
      prompt?: string;
      character_count?: number;
      message_count?: number;
      has_tool_calls?: boolean;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/pollinations/text/generate/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Chat Completion Status
  getChatCompletionStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      response: Record<string, unknown>;
      assistant_message: string;
      model_used: string;
      generation_time: number;
      message_count: number;
      has_tool_calls: boolean;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/pollinations/chat/completions/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Synchronous Text Generation (for quick responses)
  generateTextSync: async (params: {
    prompt: string;
    model?: string;
    seed?: number;
    temperature?: number;
    top_p?: number;
    presence_penalty?: number;
    frequency_penalty?: number;
    system?: string;
    json_mode?: boolean;
    referrer?: string;
  }): Promise<ApiResponse<{
    text: string;
    model_used: string;
    generation_time: number;
    prompt: string;
    character_count: number;
  }>> => {
    try {
      const response = await apiClient.post('/api/pollinations/text/generate/sync', params);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Synchronous Chat Completion (for quick responses)
  createChatCompletionSync: async (params: {
    messages: Array<{
      role: string;
      content: string | Array<{ type: string; text?: string; image_url?: { url: string } }>;
    }>;
    model?: string;
    seed?: number;
    temperature?: number;
    top_p?: number;
    presence_penalty?: number;
    frequency_penalty?: number;
    json_mode?: boolean;
    tools?: Array<Record<string, unknown>>;
    tool_choice?: string | Record<string, unknown>;
    referrer?: string;
  }): Promise<ApiResponse<Record<string, unknown>>> => {
    try {
      const response = await apiClient.post('/api/pollinations/chat/completions/sync', params);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Audio TTS
  generateTTS: async (params: {
    text: string;
    voice?: string;
    model?: string;
  }): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const response = await apiClient.post('/api/pollinations/audio/tts', params);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get TTS Status
  getTTSStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      content_url: string;
      content_type: string;
      file_size: number;
      generation_time: number;
      model_used: string;
      voice_used: string;
      text: string;
      text_length: number;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/pollinations/audio/tts/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Audio Transcription
  transcribeAudio: async (file: File, question: string = "Transcribe this audio"): Promise<ApiResponse<{ job_id: string }>> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('question', question);

      const response = await apiClient.post('/api/pollinations/audio/transcribe', formData);
      
      if (response.data && response.data.job_id) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return {
        success: false,
        error: 'Invalid response format'
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Get Transcription Status
  getTranscriptionStatus: async (jobId: string): Promise<ApiResponse<{
    job_id: string;
    status: string;
    result?: {
      transcription: string;
      audio_format: string;
      generation_time: number;
      file_name?: string;
      file_size: number;
      character_count: number;
    };
    error?: string;
  }>> => {
    try {
      const response = await apiClient.get(`/api/pollinations/audio/transcribe/${jobId}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // Synchronous TTS (for quick responses)
  generateTTSSync: async (params: {
    text: string;
    voice?: string;
    model?: string;
  }): Promise<ApiResponse<{
    content_url: string;
    content_type: string;
    file_size: number;
    generation_time: number;
    model_used: string;
    voice_used: string;
    text: string;
    text_length: number;
  }>> => {
    try {
      const response = await apiClient.post('/api/pollinations/audio/tts/sync', params);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  // List Available Models
  listImageModels: async (): Promise<ApiResponse<{ models: string[] }>> => {
    try {
      const response = await apiClient.get('/api/pollinations/models/image');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  listTextModels: async (): Promise<ApiResponse<Record<string, unknown>>> => {
    try {
      const response = await apiClient.get('/api/pollinations/models/text');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  },

  listVoices: async (): Promise<ApiResponse<{
    voices: Array<{ name: string; description: string }>;
    model: string;
    total_count: number;
    note?: string;
  }>> => {
    try {
      const response = await apiClient.get('/api/pollinations/voices');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      return {
        success: false,
        error: errorMessage
      };
    }
  }
};

// Utility functions
export const apiUtils = {
  // Poll job status until completion
  pollJobStatus: async (
    jobId: string,
    onUpdate?: Function,
    pollInterval = 5000,
    maxAttempts = 120 // 10 minutes max
  ): Promise<Job> => {
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          
          // Try MCP first, then direct API
          let job: Job;
          try {
            const mcpResponse = await mcpApi.getVideoStatus(jobId);
            if (mcpResponse.error) {
              throw new Error(mcpResponse.error.message);
            }
            job = mcpResponse.result as Job;
          } catch (mcpError) {
            // Fallback to direct API
            const directResponse = await directApi.getJobStatus(jobId);
            if (!directResponse.success || !directResponse.data) {
              throw new Error(directResponse.error || 'Failed to get job status');
            }
            job = directResponse.data;
          }

          onUpdate?.(job);

          if (job.status === 'completed' || job.status === 'failed') {
            resolve(job);
            return;
          }

          if (attempts >= maxAttempts) {
            reject(new Error('Polling timeout - job did not complete'));
            return;
          }

          // Continue polling
          setTimeout(poll, pollInterval);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  },

  // Format error message
  formatError: (error: unknown): string => {
    if (error && typeof error === 'object') {
      if ('response' in error) {
        const axiosError = error as { response?: { data?: { error?: string; message?: string; detail?: string } } };
        if (axiosError.response?.data?.detail) {
          return axiosError.response.data.detail;
        }
        if (axiosError.response?.data?.error) {
          return axiosError.response.data.error;
        }
        if (axiosError.response?.data?.message) {
          return axiosError.response.data.message;
        }
      }
      if ('message' in error) {
        return (error as { message: string }).message;
      }
    }
    return 'An unexpected error occurred';
  },

  // Check if API key is set
  hasApiKey: (): boolean => {
    return !!apiClient.defaults.headers.common['X-API-Key'];
  }
};
