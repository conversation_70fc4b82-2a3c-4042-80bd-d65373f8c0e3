import { useState, useCallback, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';

// Types
interface TextOverlayConfig {
  text: string;
  position: {
    x: number;
    y: number;
    alignment: 'left' | 'center' | 'right';
  };
  typography: {
    fontFamily: string;
    fontSize: number;
    fontWeight: number;
    fontStyle: 'normal' | 'italic';
    letterSpacing: number;
    lineHeight: number;
    textDecoration: 'none' | 'underline' | 'overline' | 'line-through';
  };
  colors: {
    text: string;
    background: string;
    stroke: string;
  };
  effects: {
    shadow: {
      enabled: boolean;
      offsetX: number;
      offsetY: number;
      blur: number;
      color: string;
    };
    gradient: {
      enabled: boolean;
      type: 'linear' | 'radial';
      colors: string[];
      direction: number;
    };
    stroke: {
      enabled: boolean;
      width: number;
    };
    glow: {
      enabled: boolean;
      color: string;
      intensity: number;
    };
  };
  animation: {
    enabled: boolean;
    type: 'fadeIn' | 'slideIn' | 'bounce' | 'typewriter' | 'pulse' | 'rotate';
    duration: number;
    delay: number;
    easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
    loop: boolean;
  };
  timing: {
    startTime: number;
    duration: number;
  };
  responsive: {
    mobile: Partial<TextOverlayConfig>;
    tablet: Partial<TextOverlayConfig>;
  };
}

interface PresetTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  config: Partial<TextOverlayConfig>;
  tags: string[];
  popular: boolean;
}

interface UseTextOverlayOptions {
  apiKey?: string;
  apiBaseUrl?: string;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

interface TextOverlayJob {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: {
    video_url?: string;
    preview_url?: string;
    config?: TextOverlayConfig;
  };
  error?: string;
  progress?: number;
}

export const useTextOverlay = (options: UseTextOverlayOptions = {}) => {
  const {
    apiKey = process.env.REACT_APP_API_KEY,
    apiBaseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000',
    autoSave = false,
    autoSaveDelay = 2000
  } = options;

  // Default configuration
  const defaultConfig: TextOverlayConfig = {
    text: 'Your amazing text here',
    position: { x: 50, y: 50, alignment: 'center' },
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: 32,
      fontWeight: 400,
      fontStyle: 'normal',
      letterSpacing: 0,
      lineHeight: 1.4,
      textDecoration: 'none'
    },
    colors: {
      text: '#ffffff',
      background: 'rgba(0,0,0,0.5)',
      stroke: '#000000'
    },
    effects: {
      shadow: {
        enabled: false,
        offsetX: 2,
        offsetY: 2,
        blur: 4,
        color: 'rgba(0,0,0,0.5)'
      },
      gradient: {
        enabled: false,
        type: 'linear',
        colors: ['#667eea', '#764ba2'],
        direction: 45
      },
      stroke: {
        enabled: false,
        width: 2
      },
      glow: {
        enabled: false,
        color: '#ffffff',
        intensity: 10
      }
    },
    animation: {
      enabled: false,
      type: 'fadeIn',
      duration: 1000,
      delay: 0,
      easing: 'ease',
      loop: false
    },
    timing: {
      startTime: 0,
      duration: 5
    },
    responsive: {
      mobile: {},
      tablet: {}
    }
  };

  // State
  const [config, setConfig] = useState<TextOverlayConfig>(defaultConfig);
  const [savedConfigs, setSavedConfigs] = useState<{ [key: string]: TextOverlayConfig }>({});
  const [currentJob, setCurrentJob] = useState<TextOverlayJob | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [history, setHistory] = useState<TextOverlayConfig[]>([defaultConfig]);
  const [historyIndex, setHistoryIndex] = useState(0);

  // Refs
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const pollTimeoutRef = useRef<NodeJS.Timeout>();

  // API Headers
  const getHeaders = useCallback(() => ({
    'Content-Type': 'application/json',
    'X-API-Key': apiKey || '',
  }), [apiKey]);

  // Update config with history tracking
  const updateConfig = useCallback(<K extends keyof TextOverlayConfig>(
    key: K,
    value: TextOverlayConfig[K] | ((prev: TextOverlayConfig[K]) => TextOverlayConfig[K])
  ) => {
    setConfig(prev => {
      const newConfig = {
        ...prev,
        [key]: typeof value === 'function' ? value(prev[key]) : value
      };

      // Add to history
      setHistory(currentHistory => {
        const newHistory = currentHistory.slice(0, historyIndex + 1);
        newHistory.push(newConfig);
        
        // Limit history to 50 items
        if (newHistory.length > 50) {
          newHistory.shift();
        }
        
        return newHistory;
      });
      
      setHistoryIndex(prev => Math.min(prev + 1, 49));

      // Auto-save if enabled
      if (autoSave) {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
        
        autoSaveTimeoutRef.current = setTimeout(() => {
          saveConfig('autosave', newConfig);
        }, autoSaveDelay);
      }

      return newConfig;
    });
  }, [autoSave, autoSaveDelay, historyIndex]);

  // Undo/Redo functionality
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(prev => prev - 1);
      setConfig(history[historyIndex - 1]);
    }
  }, [history, historyIndex]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(prev => prev + 1);
      setConfig(history[historyIndex + 1]);
    }
  }, [history, historyIndex]);

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  // Save/Load configurations
  const saveConfig = useCallback((name: string, configToSave?: TextOverlayConfig) => {
    const configData = configToSave || config;
    setSavedConfigs(prev => ({
      ...prev,
      [name]: configData
    }));
    
    // Also save to localStorage
    try {
      const savedData = JSON.parse(localStorage.getItem('textOverlayConfigs') || '{}');
      savedData[name] = configData;
      localStorage.setItem('textOverlayConfigs', JSON.stringify(savedData));
      
      if (name !== 'autosave') {
        toast.success(`Configuration "${name}" saved successfully`);
      }
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
      toast.error('Failed to save configuration');
    }
  }, [config]);

  const loadConfig = useCallback((name: string) => {
    const savedConfig = savedConfigs[name];
    if (savedConfig) {
      setConfig(savedConfig);
      toast.success(`Configuration "${name}" loaded`);
    } else {
      toast.error(`Configuration "${name}" not found`);
    }
  }, [savedConfigs]);

  const deleteConfig = useCallback((name: string) => {
    setSavedConfigs(prev => {
      const newConfigs = { ...prev };
      delete newConfigs[name];
      return newConfigs;
    });
    
    try {
      const savedData = JSON.parse(localStorage.getItem('textOverlayConfigs') || '{}');
      delete savedData[name];
      localStorage.setItem('textOverlayConfigs', JSON.stringify(savedData));
      toast.success(`Configuration "${name}" deleted`);
    } catch (error) {
      console.error('Failed to delete from localStorage:', error);
    }
  }, []);

  // Apply preset template
  const applyPreset = useCallback((preset: PresetTemplate) => {
    const mergedConfig = {
      ...config,
      ...preset.config,
      text: config.text // Keep current text
    };
    setConfig(mergedConfig);
    toast.success(`Applied "${preset.name}" preset`);
  }, [config]);

  // Reset to defaults
  const resetConfig = useCallback(() => {
    setConfig({ ...defaultConfig, text: config.text });
    toast.success('Configuration reset to defaults');
  }, [config.text, defaultConfig]);

  // Generate preview
  const generatePreview = useCallback(async (): Promise<string | null> => {
    if (!apiKey) {
      toast.error('API key not configured');
      return null;
    }

    setIsGenerating(true);
    
    try {
      const response = await fetch(`${apiBaseUrl}/api/video/text-overlay/preview`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({
          config,
          width: 1920,
          height: 1080,
          format: 'png'
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.job_id) {
        setCurrentJob({ job_id: result.job_id, status: 'pending' });
        pollJobStatus(result.job_id);
        return result.job_id;
      } else if (result.preview_url) {
        setPreviewUrl(result.preview_url);
        return result.preview_url;
      }
      
      return null;
    } catch (error) {
      console.error('Preview generation failed:', error);
      toast.error('Failed to generate preview');
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, [apiKey, apiBaseUrl, config, getHeaders]);

  // Generate video with text overlay
  const generateVideo = useCallback(async (videoFile: File | string): Promise<string | null> => {
    if (!apiKey) {
      toast.error('API key not configured');
      return null;
    }

    setIsGenerating(true);
    
    try {
      const formData = new FormData();
      
      if (typeof videoFile === 'string') {
        formData.append('video_url', videoFile);
      } else {
        formData.append('video', videoFile);
      }
      
      formData.append('config', JSON.stringify(config));

      const response = await fetch(`${apiBaseUrl}/api/video/text-overlay`, {
        method: 'POST',
        headers: {
          'X-API-Key': apiKey || '',
          // Don't set Content-Type for FormData
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.job_id) {
        setCurrentJob({ job_id: result.job_id, status: 'pending' });
        pollJobStatus(result.job_id);
        toast.success('Video processing started');
        return result.job_id;
      }
      
      return null;
    } catch (error) {
      console.error('Video generation failed:', error);
      toast.error('Failed to generate video');
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, [apiKey, apiBaseUrl, config]);

  // Poll job status
  const pollJobStatus = useCallback(async (jobId: string) => {
    try {
      const response = await fetch(`${apiBaseUrl}/api/video/text-overlay/${jobId}`, {
        headers: getHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const job: TextOverlayJob = await response.json();
      setCurrentJob(job);

      if (job.status === 'processing' || job.status === 'pending') {
        pollTimeoutRef.current = setTimeout(() => pollJobStatus(jobId), 2000);
      } else if (job.status === 'completed') {
        setIsGenerating(false);
        if (job.result?.preview_url) {
          setPreviewUrl(job.result.preview_url);
        }
        toast.success('Processing completed successfully');
      } else if (job.status === 'failed') {
        setIsGenerating(false);
        toast.error(job.error || 'Processing failed');
      }
    } catch (error) {
      console.error('Failed to poll job status:', error);
      setIsGenerating(false);
      toast.error('Failed to check processing status');
    }
  }, [apiBaseUrl, getHeaders]);

  // Get computed styles for preview
  const getPreviewStyles = useCallback((): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      fontFamily: config.typography.fontFamily,
      fontSize: `${config.typography.fontSize}px`,
      fontWeight: config.typography.fontWeight,
      fontStyle: config.typography.fontStyle,
      letterSpacing: `${config.typography.letterSpacing}px`,
      lineHeight: config.typography.lineHeight,
      textDecoration: config.typography.textDecoration,
      color: config.colors.text,
      backgroundColor: config.colors.background,
      position: 'absolute',
      left: `${config.position.x}%`,
      top: `${config.position.y}%`,
      transform: 'translate(-50%, -50%)',
      textAlign: config.position.alignment,
      padding: '8px 16px',
      borderRadius: '8px',
      whiteSpace: 'pre-wrap',
      maxWidth: '80%',
      wordBreak: 'break-word'
    };

    // Apply effects
    if (config.effects.shadow.enabled) {
      baseStyle.textShadow = `${config.effects.shadow.offsetX}px ${config.effects.shadow.offsetY}px ${config.effects.shadow.blur}px ${config.effects.shadow.color}`;
    }

    if (config.effects.stroke.enabled) {
      baseStyle.WebkitTextStroke = `${config.effects.stroke.width}px ${config.colors.stroke}`;
    }

    if (config.effects.glow.enabled) {
      baseStyle.textShadow = `0 0 ${config.effects.glow.intensity}px ${config.effects.glow.color}`;
    }

    if (config.effects.gradient.enabled) {
      const gradient = config.effects.gradient.type === 'linear'
        ? `linear-gradient(${config.effects.gradient.direction}deg, ${config.effects.gradient.colors.join(', ')})`
        : `radial-gradient(${config.effects.gradient.colors.join(', ')})`;
      
      baseStyle.background = gradient;
      baseStyle.WebkitBackgroundClip = 'text';
      baseStyle.WebkitTextFillColor = 'transparent';
      baseStyle.backgroundClip = 'text';
    }

    return baseStyle;
  }, [config]);

  // Export configuration
  const exportConfig = useCallback(() => {
    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `text-overlay-config-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    toast.success('Configuration exported');
  }, [config]);

  // Import configuration
  const importConfig = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedConfig = JSON.parse(e.target?.result as string);
        setConfig({ ...defaultConfig, ...importedConfig });
        toast.success('Configuration imported successfully');
      } catch (error) {
        console.error('Import failed:', error);
        toast.error('Invalid configuration file');
      }
    };
    reader.readAsText(file);
  }, [defaultConfig]);

  // Load saved configurations from localStorage on mount
  useEffect(() => {
    try {
      const savedData = JSON.parse(localStorage.getItem('textOverlayConfigs') || '{}');
      setSavedConfigs(savedData);
      
      // Load autosave if available
      if (savedData.autosave && autoSave) {
        setConfig(savedData.autosave);
      }
    } catch (error) {
      console.error('Failed to load saved configurations:', error);
    }
  }, [autoSave]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    config,
    savedConfigs,
    currentJob,
    isGenerating,
    previewUrl,
    
    // History
    canUndo,
    canRedo,
    
    // Actions
    updateConfig,
    undo,
    redo,
    saveConfig,
    loadConfig,
    deleteConfig,
    applyPreset,
    resetConfig,
    generatePreview,
    generateVideo,
    exportConfig,
    importConfig,
    getPreviewStyles,
    
    // Utilities
    pollJobStatus
  };
};

export type { TextOverlayConfig, PresetTemplate, TextOverlayJob };
export default useTextOverlay;