import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Slider,
  Button,
  ButtonGroup,
  Tabs,
  Tab,
  Grid,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Chip,
  Stack,
  ToggleButtonGroup,
  ToggleButton,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  SwipeableDrawer,
  useTheme,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  AppBar,
  Toolbar,
  useMediaQuery
} from '@mui/material';
import {
  FormatBold,
  FormatItalic,
  FormatAlignLeft,
  FormatAlignCenter,
  FormatAlignRight,
  Palette,
  Animation,
  LocationOn,
  TextFields,
  PlayArrow,
  Pause,
  Save,
  Star,
  Close,
  Settings,
  Preview,
  Tune,
  ColorLens,
  AutoAwesome,
  KeyboardArrowUp,
  KeyboardArrowDown,
  Menu,
  Check
} from '@mui/icons-material';
import { useTextOverlay, TextOverlayConfig, PresetTemplate } from '../hooks/useTextOverlay';

// Mobile-optimized preset templates
const MOBILE_PRESETS: PresetTemplate[] = [
  {
    id: 'tiktok_title',
    name: 'TikTok Title',
    description: 'Perfect for TikTok videos',
    thumbnail: '📱',
    popular: true,
    tags: ['social', 'tiktok', 'viral'],
    config: {
      typography: {
        fontFamily: 'Poppins, sans-serif',
        fontSize: 36,
        fontWeight: 700,
        letterSpacing: -0.5
      },
      colors: { text: '#ffffff', background: 'rgba(0,0,0,0.8)' },
      position: { x: 50, y: 15, alignment: 'center' },
      effects: {
        shadow: { enabled: true, offsetX: 0, offsetY: 2, blur: 8, color: 'rgba(0,0,0,0.5)' }
      }
    }
  },
  {
    id: 'instagram_story',
    name: 'Instagram Story',
    description: 'Clean story text',
    thumbnail: '📸',
    popular: true,
    tags: ['instagram', 'story', 'clean'],
    config: {
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: 28,
        fontWeight: 600
      },
      colors: { text: '#ffffff' },
      position: { x: 50, y: 85, alignment: 'center' }
    }
  },
  {
    id: 'youtube_shorts',
    name: 'YouTube Shorts',
    description: 'Engaging shorts caption',
    thumbnail: '🎬',
    popular: false,
    tags: ['youtube', 'shorts', 'engaging'],
    config: {
      typography: {
        fontFamily: 'Roboto, sans-serif',
        fontSize: 32,
        fontWeight: 500
      },
      colors: { text: '#ffff00', background: 'rgba(255,0,0,0.8)' },
      position: { x: 50, y: 20, alignment: 'center' }
    }
  }
];

// Quick position presets for mobile
const MOBILE_POSITIONS = [
  { id: 'top', label: 'Top', x: 50, y: 15, icon: <KeyboardArrowUp /> },
  { id: 'center', label: 'Center', x: 50, y: 50, icon: <LocationOn /> },
  { id: 'bottom', label: 'Bottom', x: 50, y: 85, icon: <KeyboardArrowDown /> }
];

// Quick font sizes for mobile
const MOBILE_FONT_SIZES = [
  { label: 'Small', value: 24 },
  { label: 'Medium', value: 32 },
  { label: 'Large', value: 48 },
  { label: 'XL', value: 64 }
];

// Popular colors for mobile
const MOBILE_COLORS = [
  '#ffffff', // White
  '#000000', // Black
  '#ff0000', // Red
  '#ffff00', // Yellow
  '#00ff00', // Green
  '#00ffff', // Cyan
  '#ff00ff', // Magenta
  '#ffa500'  // Orange
];

interface MobileTextOverlayEditorProps {
  onApply?: (config: TextOverlayConfig) => void;
  onClose?: () => void;
  initialConfig?: Partial<TextOverlayConfig>;
}

const MobileTextOverlayEditor: React.FC<MobileTextOverlayEditorProps> = ({
  onApply,
  onClose,
  initialConfig
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const {
    config,
    updateConfig,
    applyPreset,
    getPreviewStyles,
    generatePreview,
    isGenerating
  } = useTextOverlay();

  // Mobile-specific state
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [presetsOpen, setPresetsOpen] = useState(false);
  const [activeSection, setActiveSection] = useState<'text' | 'style' | 'position' | 'effects'>('text');
  const [previewMode, setPreviewMode] = useState(false);

  // Initialize with any provided config
  React.useEffect(() => {
    if (initialConfig) {
      Object.entries(initialConfig).forEach(([key, value]) => {
        updateConfig(key as keyof TextOverlayConfig, value);
      });
    }
  }, [initialConfig, updateConfig]);

  // Mobile-optimized sections
  const sections = [
    { id: 'text', label: 'Text', icon: <TextFields /> },
    { id: 'style', label: 'Style', icon: <FormatBold /> },
    { id: 'position', label: 'Position', icon: <LocationOn /> },
    { id: 'effects', label: 'Effects', icon: <AutoAwesome /> }
  ];

  const handleApply = () => {
    onApply?.(config);
    onClose?.();
  };

  // Text Section Component
  const TextSection = () => (
    <Stack spacing={3} sx={{ p: 2 }}>
      <TextField
        fullWidth
        multiline
        rows={4}
        label="Your Text"
        value={config.text}
        onChange={(e) => updateConfig('text', e.target.value)}
        variant="outlined"
        placeholder="Enter your amazing text here..."
        sx={{ fontSize: '1.1rem' }}
      />
      
      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Quick Presets</Typography>
        <Grid container spacing={1}>
          {MOBILE_PRESETS.map(preset => (
            <Grid item xs={4} key={preset.id}>
              <Card 
                sx={{ 
                  cursor: 'pointer',
                  textAlign: 'center',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => applyPreset(preset)}
              >
                <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                  <Typography variant="h6">{preset.thumbnail}</Typography>
                  <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                    {preset.name}
                  </Typography>
                  {preset.popular && (
                    <Star sx={{ fontSize: 12, color: 'warning.main', ml: 0.5 }} />
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Stack>
  );

  // Style Section Component
  const StyleSection = () => (
    <Stack spacing={3} sx={{ p: 2 }}>
      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Font Size</Typography>
        <Grid container spacing={1}>
          {MOBILE_FONT_SIZES.map(size => (
            <Grid item xs={3} key={size.label}>
              <Button
                variant={config.typography.fontSize === size.value ? 'contained' : 'outlined'}
                fullWidth
                size="small"
                onClick={() => updateConfig('typography', { ...config.typography, fontSize: size.value })}
              >
                {size.label}
              </Button>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Font Weight</Typography>
        <Slider
          value={config.typography.fontWeight}
          onChange={(_, value) => updateConfig('typography', { ...config.typography, fontWeight: value as number })}
          min={100}
          max={900}
          step={100}
          marks={[
            { value: 400, label: 'Normal' },
            { value: 700, label: 'Bold' }
          ]}
          valueLabelDisplay="auto"
        />
      </Box>

      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Text Style</Typography>
        <ToggleButtonGroup
          value={[
            config.typography.fontStyle === 'italic' && 'italic',
            config.typography.textDecoration !== 'none' && config.typography.textDecoration
          ].filter(Boolean)}
          onChange={(_, values) => {
            updateConfig('typography', {
              ...config.typography,
              fontStyle: values.includes('italic') ? 'italic' : 'normal',
              textDecoration: values.find(v => v !== 'italic') || 'none'
            });
          }}
          size="small"
          fullWidth
        >
          <ToggleButton value="italic">
            <FormatItalic />
          </ToggleButton>
          <ToggleButton value="underline">
            <Typography variant="caption" sx={{ textDecoration: 'underline' }}>U</Typography>
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Text Color</Typography>
        <Grid container spacing={1}>
          {MOBILE_COLORS.map(color => (
            <Grid item xs={3} key={color}>
              <Box
                sx={{
                  width: '100%',
                  height: 40,
                  bgcolor: color,
                  borderRadius: 1,
                  border: config.colors.text === color ? 3 : 1,
                  borderColor: config.colors.text === color ? 'primary.main' : 'divider',
                  cursor: 'pointer',
                  position: 'relative',
                  '&:hover': { transform: 'scale(1.05)' }
                }}
                onClick={() => updateConfig('colors', { ...config.colors, text: color })}
              >
                {config.colors.text === color && (
                  <Check 
                    sx={{ 
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: color === '#ffffff' ? '#000000' : '#ffffff'
                    }}
                  />
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Stack>
  );

  // Position Section Component
  const PositionSection = () => (
    <Stack spacing={3} sx={{ p: 2 }}>
      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Quick Positions</Typography>
        <Grid container spacing={1}>
          {MOBILE_POSITIONS.map(pos => (
            <Grid item xs={4} key={pos.id}>
              <Button
                variant={
                  config.position.x === pos.x && config.position.y === pos.y 
                    ? 'contained' 
                    : 'outlined'
                }
                fullWidth
                startIcon={pos.icon}
                onClick={() => updateConfig('position', { ...config.position, x: pos.x, y: pos.y })}
              >
                {pos.label}
              </Button>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Horizontal Position</Typography>
        <Slider
          value={config.position.x}
          onChange={(_, value) => updateConfig('position', { ...config.position, x: value as number })}
          min={0}
          max={100}
          valueLabelDisplay="auto"
          valueLabelFormat={(value) => `${value}%`}
        />
      </Box>

      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Vertical Position</Typography>
        <Slider
          value={config.position.y}
          onChange={(_, value) => updateConfig('position', { ...config.position, y: value as number })}
          min={0}
          max={100}
          valueLabelDisplay="auto"
          valueLabelFormat={(value) => `${value}%`}
        />
      </Box>

      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Text Alignment</Typography>
        <ToggleButtonGroup
          value={config.position.alignment}
          exclusive
          onChange={(_, value) => value && updateConfig('position', { ...config.position, alignment: value })}
          fullWidth
        >
          <ToggleButton value="left"><FormatAlignLeft /></ToggleButton>
          <ToggleButton value="center"><FormatAlignCenter /></ToggleButton>
          <ToggleButton value="right"><FormatAlignRight /></ToggleButton>
        </ToggleButtonGroup>
      </Box>
    </Stack>
  );

  // Effects Section Component
  const EffectsSection = () => (
    <Stack spacing={3} sx={{ p: 2 }}>
      <FormControlLabel
        control={
          <Switch
            checked={config.effects.shadow.enabled}
            onChange={(e) => updateConfig('effects', {
              ...config.effects,
              shadow: { ...config.effects.shadow, enabled: e.target.checked }
            })}
          />
        }
        label="Drop Shadow"
      />

      <FormControlLabel
        control={
          <Switch
            checked={config.effects.stroke.enabled}
            onChange={(e) => updateConfig('effects', {
              ...config.effects,
              stroke: { ...config.effects.stroke, enabled: e.target.checked }
            })}
          />
        }
        label="Text Outline"
      />

      {config.effects.stroke.enabled && (
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>Outline Width</Typography>
          <Slider
            value={config.effects.stroke.width}
            onChange={(_, value) => updateConfig('effects', {
              ...config.effects,
              stroke: { ...config.effects.stroke, width: value as number }
            })}
            min={1}
            max={10}
            valueLabelDisplay="auto"
          />
        </Box>
      )}

      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>Background Color</Typography>
        <input
          type="color"
          value={config.colors.background.includes('rgba') ? '#000000' : config.colors.background}
          onChange={(e) => updateConfig('colors', { ...config.colors, background: e.target.value })}
          style={{
            width: '100%',
            height: 40,
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer'
          }}
        />
      </Box>

      <FormControlLabel
        control={
          <Switch
            checked={config.colors.background !== 'transparent'}
            onChange={(e) => updateConfig('colors', {
              ...config.colors,
              background: e.target.checked ? 'rgba(0,0,0,0.7)' : 'transparent'
            })}
          />
        }
        label="Background Fill"
      />
    </Stack>
  );

  // Render current section
  const renderCurrentSection = () => {
    switch (activeSection) {
      case 'text': return <TextSection />;
      case 'style': return <StyleSection />;
      case 'position': return <PositionSection />;
      case 'effects': return <EffectsSection />;
      default: return <TextSection />;
    }
  };

  if (previewMode) {
    return (
      <Box sx={{ height: '100vh', bgcolor: 'grey.900', position: 'relative' }}>
        {/* Preview Area */}
        <Box 
          sx={{ 
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: isMobile ? '95%' : '80%',
            aspectRatio: '9/16',
            bgcolor: 'grey.800',
            borderRadius: 2,
            overflow: 'hidden'
          }}
        >
          {/* Text Overlay Preview */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Box
              component="div"
              sx={getPreviewStyles()}
            >
              {config.text}
            </Box>
          </Box>
        </Box>

        {/* Preview Controls */}
        <Box sx={{ position: 'absolute', bottom: 16, left: '50%', transform: 'translateX(-50%)' }}>
          <Stack direction="row" spacing={1}>
            <Button
              variant="contained"
              onClick={() => setPreviewMode(false)}
              startIcon={<Settings />}
            >
              Edit
            </Button>
            <Button
              variant="contained"
              color="success"
              onClick={handleApply}
              startIcon={<Check />}
            >
              Apply
            </Button>
          </Stack>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: 'background.default' }}>
      {/* Header */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <IconButton 
            edge="start" 
            color="inherit" 
            onClick={onClose}
            sx={{ mr: 2 }}
          >
            <Close />
          </IconButton>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            Text Overlay
          </Typography>
          <IconButton color="inherit" onClick={() => setPreviewMode(true)}>
            <Preview />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Section Navigation */}
      <Paper elevation={1} sx={{ borderRadius: 0 }}>
        <Tabs
          value={activeSection}
          onChange={(_, value) => setActiveSection(value)}
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
        >
          {sections.map(section => (
            <Tab
              key={section.id}
              value={section.id}
              icon={section.icon}
              label={section.label}
              iconPosition="top"
              sx={{ minHeight: 64, fontSize: '0.75rem' }}
            />
          ))}
        </Tabs>
      </Paper>

      {/* Main Content */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {renderCurrentSection()}
      </Box>

      {/* Mini Preview */}
      <Paper 
        elevation={3}
        sx={{ 
          position: 'fixed',
          top: 80,
          right: 16,
          width: 120,
          height: 200,
          borderRadius: 2,
          overflow: 'hidden',
          bgcolor: 'grey.900',
          zIndex: 1000
        }}
      >
        <Box
          sx={{
            position: 'relative',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Box
            sx={{
              ...getPreviewStyles(),
              fontSize: '8px',
              padding: '2px 4px',
              position: 'static',
              transform: 'none',
              maxWidth: '90%',
              textAlign: 'center'
            }}
          >
            {config.text.substring(0, 20) + (config.text.length > 20 ? '...' : '')}
          </Box>
        </Box>
      </Paper>

      {/* Bottom Action Buttons */}
      <Paper elevation={3} sx={{ p: 2 }}>
        <Stack direction="row" spacing={2} justifyContent="center">
          <Button
            variant="outlined"
            onClick={() => setPresetsOpen(true)}
            startIcon={<Star />}
            size="large"
          >
            Templates
          </Button>
          <Button
            variant="contained"
            onClick={handleApply}
            startIcon={<Check />}
            size="large"
            sx={{ minWidth: 120 }}
          >
            Apply
          </Button>
        </Stack>
      </Paper>

      {/* Presets Dialog */}
      <Dialog open={presetsOpen} onClose={() => setPresetsOpen(false)} fullWidth maxWidth="sm">
        <DialogTitle>Choose Template</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {MOBILE_PRESETS.map(preset => (
              <Grid item xs={6} key={preset.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                  onClick={() => {
                    applyPreset(preset);
                    setPresetsOpen(false);
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h4" sx={{ mb: 1 }}>
                      {preset.thumbnail}
                    </Typography>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {preset.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                      {preset.description}
                    </Typography>
                    <Stack direction="row" spacing={0.5} justifyContent="center" flexWrap="wrap">
                      {preset.tags.slice(0, 2).map(tag => (
                        <Chip key={tag} label={tag} size="small" variant="outlined" />
                      ))}
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPresetsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MobileTextOverlayEditor;