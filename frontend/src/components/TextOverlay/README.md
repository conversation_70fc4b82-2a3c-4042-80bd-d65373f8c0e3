# Modern Text Overlay System

A comprehensive, modern text overlay system built with React, TypeScript, and Material-UI. This system provides both desktop and mobile interfaces for creating professional text overlays for videos and images.

## 🌟 Features

### Core Functionality
- **Real-time Preview**: Live preview of text styling changes
- **Advanced Typography**: Font family, size, weight, spacing, and line height controls
- **Visual Positioning**: Interactive positioning tool with grid and quick position buttons
- **Professional Effects**: Drop shadows, gradients, strokes, and glow effects
- **Animation System**: Multiple animation types with timeline controls
- **Template Gallery**: Pre-built templates for different use cases
- **Responsive Design**: Mobile-first approach with device-specific optimizations

### Visual Effects
- **Drop Shadow**: Customizable offset, blur, and color
- **Text Gradients**: Linear and radial gradients with multiple colors
- **Text Stroke**: Outline text with adjustable width and color
- **Glow Effects**: Neon-style glow with intensity control
- **Background Colors**: Solid colors and transparent backgrounds

### Animation Types
- Fade In
- Slide In
- Bounce
- Typewriter
- Pulse
- Rotate

### Professional Templates
- **Modern Title**: Clean, bold titles with subtle shadows
- **Neon Glow**: Cyberpunk-inspired glowing text
- **Gradient Hero**: Eye-catching gradient text for hero sections
- **Vintage Badge**: Retro-style text with decorative elements
- **Minimal Subtitle**: Clean subtitles with perfect typography
- **Social Caption**: Optimized for Instagram and TikTok

## 📱 Responsive Design

### Desktop Experience
- Full-featured editor with tabbed interface
- Visual positioning grid
- Advanced controls for all parameters
- Real-time preview with multiple aspect ratios
- Speed dial for quick actions

### Mobile Experience
- Touch-optimized interface
- Simplified controls for mobile usage
- Quick presets for social media
- Swipe-based navigation
- Mini preview window
- One-handed operation support

## 🛠️ Technical Architecture

### Components
```
TextOverlayEditor.tsx          # Main desktop editor
MobileTextOverlayEditor.tsx    # Mobile-optimized editor
TextOverlayDemo.tsx           # Integration example and demo
useTextOverlay.ts             # Custom hook for state management
types/textOverlay.ts          # TypeScript definitions
```

### State Management
The system uses a custom React hook (`useTextOverlay`) that provides:
- Configuration state management
- History tracking (undo/redo)
- Auto-save functionality
- API integration
- Local storage persistence

### Type Safety
Complete TypeScript coverage with:
- Strongly typed configuration objects
- Interface definitions for all components
- API response types
- Event handling types

## 🚀 Getting Started

### Installation

```bash
# Install dependencies
npm install @mui/material @mui/icons-material @emotion/react @emotion/styled react-hot-toast

# For React 18+
npm install @mui/material @mui/icons-material @emotion/react @emotion/styled react-hot-toast
```

### Basic Usage

```tsx
import React from 'react';
import TextOverlayEditor from './components/TextOverlayEditor';
import { useTextOverlay } from './hooks/useTextOverlay';

function MyApp() {
  const { config, updateConfig } = useTextOverlay();

  return (
    <TextOverlayEditor />
  );
}
```

### Mobile Usage

```tsx
import React from 'react';
import MobileTextOverlayEditor from './components/MobileTextOverlayEditor';

function MobileApp() {
  const handleApply = (config) => {
    console.log('Applied config:', config);
  };

  return (
    <MobileTextOverlayEditor
      onApply={handleApply}
      onClose={() => console.log('Editor closed')}
    />
  );
}
```

### Full Demo Integration

```tsx
import React from 'react';
import TextOverlayDemo from './components/TextOverlayDemo';

function App() {
  return <TextOverlayDemo />;
}
```

## 🎨 Customization

### Custom Presets

```tsx
const customPresets: PresetTemplate[] = [
  {
    id: 'my_brand',
    name: 'My Brand Style',
    description: 'Corporate brand styling',
    thumbnail: '🏢',
    popular: true,
    tags: ['brand', 'corporate'],
    config: {
      typography: {
        fontFamily: 'Corporate Sans, sans-serif',
        fontSize: 40,
        fontWeight: 600,
        letterSpacing: 1
      },
      colors: { 
        text: '#2563eb', 
        background: 'rgba(255,255,255,0.9)' 
      },
      effects: {
        shadow: { 
          enabled: true, 
          offsetX: 2, 
          offsetY: 2, 
          blur: 8, 
          color: 'rgba(37,99,235,0.3)' 
        }
      }
    }
  }
];
```

### Custom Fonts

```tsx
const CUSTOM_FONTS = [
  'Inter, sans-serif',
  'Roboto, sans-serif',
  'Your Custom Font, sans-serif',
  // Add your font families here
];
```

### Custom Color Palettes

```tsx
const CUSTOM_PALETTES = [
  { 
    name: 'Brand Colors', 
    colors: ['#1a365d', '#2d3748', '#4a5568', '#718096', '#a0aec0'] 
  },
  { 
    name: 'Accent Colors', 
    colors: ['#e53e3e', '#dd6b20', '#d69e2e', '#38a169', '#3182ce'] 
  }
];
```

## 📚 API Integration

### Configuration Object Structure

```typescript
interface TextOverlayConfig {
  text: string;
  position: {
    x: number; // 0-100 percentage
    y: number; // 0-100 percentage
    alignment: 'left' | 'center' | 'right';
  };
  typography: {
    fontFamily: string;
    fontSize: number;
    fontWeight: number;
    fontStyle: 'normal' | 'italic';
    letterSpacing: number;
    lineHeight: number;
    textDecoration: 'none' | 'underline' | 'overline' | 'line-through';
  };
  colors: {
    text: string;
    background: string;
    stroke: string;
  };
  effects: {
    shadow: { enabled: boolean; offsetX: number; offsetY: number; blur: number; color: string; };
    gradient: { enabled: boolean; type: 'linear' | 'radial'; colors: string[]; direction: number; };
    stroke: { enabled: boolean; width: number; };
    glow: { enabled: boolean; color: string; intensity: number; };
  };
  animation: {
    enabled: boolean;
    type: 'fadeIn' | 'slideIn' | 'bounce' | 'typewriter' | 'pulse' | 'rotate';
    duration: number;
    delay: number;
    easing: string;
    loop: boolean;
  };
  timing: {
    startTime: number;
    duration: number;
  };
}
```

### Hook API

```typescript
const {
  // State
  config,
  savedConfigs,
  isGenerating,
  previewUrl,
  canUndo,
  canRedo,
  
  // Actions
  updateConfig,
  undo,
  redo,
  saveConfig,
  loadConfig,
  applyPreset,
  resetConfig,
  generatePreview,
  generateVideo,
  exportConfig,
  importConfig,
  
  // Utilities
  getPreviewStyles
} = useTextOverlay({
  apiKey: 'your-api-key',
  apiBaseUrl: 'http://localhost:8000',
  autoSave: true,
  autoSaveDelay: 2000
});
```

## 🎯 Best Practices

### Performance
- Use `React.memo()` for expensive preview components
- Debounce rapid updates with the auto-save feature
- Implement virtual scrolling for large preset libraries
- Cache generated previews

### Accessibility
- All controls have proper ARIA labels
- Keyboard navigation support
- High contrast mode compatibility
- Screen reader optimization

### Mobile Optimization
- Touch-friendly controls (minimum 44px tap targets)
- Swipe gestures for navigation
- Simplified UI for small screens
- Hardware acceleration for smooth animations

### User Experience
- Real-time preview updates
- Undo/redo functionality
- Auto-save with visual feedback
- Loading states for all async operations
- Error handling with user-friendly messages

## 🔧 Development

### Project Structure
```
src/
├── components/
│   ├── TextOverlayEditor.tsx
│   ├── MobileTextOverlayEditor.tsx
│   └── TextOverlayDemo.tsx
├── hooks/
│   └── useTextOverlay.ts
├── types/
│   └── textOverlay.ts
└── styles/
    └── textOverlay.css
```

### Development Commands

```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Type checking
npm run type-check

# Lint code
npm run lint
```

### Contributing

1. Follow TypeScript strict mode
2. Use Material-UI design system
3. Maintain mobile-first responsive design
4. Write comprehensive tests
5. Document all public APIs
6. Follow accessibility guidelines

## 📄 License

MIT License - feel free to use in your projects!

## 🆘 Support

For issues and feature requests, please create an issue in the repository.

---

**Built with ❤️ for the modern web**