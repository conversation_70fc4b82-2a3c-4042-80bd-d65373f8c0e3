import React from 'react';
import {
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Chip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  VideoLibrary as VideoIcon,
  Shield as SafetyIcon,
  HighQuality as QualityIcon
} from '@mui/icons-material';
import {
  VIDEO_EFFECTS
} from '../constants/videoSettings';
import {
  FOOTAGE_PROVIDERS,
  SEARCH_SAFETY_LEVELS,
  FOOTAGE_QUALITIES
} from '../types/contentCreation';
import type { AdvancedVideoSettingsProps } from '../types/videoSettings';
import ImageProviderSettings from './settings/ImageProviderSettings';
import BackgroundMusicSettings from './settings/BackgroundMusicSettings';
import CaptionSettings from './settings/CaptionSettings';
import MediaSettings from './settings/MediaSettings';

const AdvancedVideoSettings: React.FC<AdvancedVideoSettingsProps> = ({
  video_orientation,
  segment_duration,
  frame_rate,
  output_width,
  output_height,
  add_captions,
  caption_style,
  caption_color,
  background_music,
  background_music_volume,
  music_duration,
  image_width,
  image_height,
  inference_steps,
  guidance_scale,
  video_effect,
  image_provider,
  footage_provider,
  search_safety,
  footage_quality,
  search_terms_per_scene,
  onChange,
  showImageSettings = false,
  showVideoEffects = false,
  showImageProviderSettings = false
}) => {
  // Background Video Settings helper functions
  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'pexels': return '📹';
      case 'pixabay': return '🎬';
      case 'ai_generated': return '🤖';
      default: return '📺';
    }
  };

  const getSafetyIcon = (level: string) => {
    switch (level) {
      case 'strict': return '🔒';
      case 'moderate': return '⚖️';
      case 'off': return '🔓';
      default: return '🔒';
    }
  };

  const getQualityIcon = (quality: string) => {
    switch (quality) {
      case 'standard': return '📺';
      case 'high': return '🎥';
      case 'ultra': return '💎';
      default: return '🎥';
    }
  };

  const getProviderCategoryColor = (provider: string) => {
    switch (provider) {
      case 'ai_generated': return '#2563eb';
      case 'pexels': return '#05A081';
      case 'pixabay': return '#2ec66d';
      default: return '#6b7280';
    }
  };

  return (
    <Accordion defaultExpanded>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SettingsIcon color="primary" />
          <Typography variant="h6">Advanced Video Settings</Typography>
          <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
            {showImageSettings ? 'AI Images Mode' : 'Stock Footage Mode'}
          </Typography>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          
          {/* Media Settings - Video Dimensions & Format */}
          <Grid item xs={12}>
            <MediaSettings
              settings={{
                imageWidth: output_width || 1080,
                imageHeight: output_height || 1920,
                aspectRatio: video_orientation === 'vertical' ? '9:16' : video_orientation === 'square' ? '1:1' : '16:9',
                captionStyle: caption_style,
                captionColor: caption_color,
                enableCaptions: add_captions,
                backgroundMusic: background_music,
                backgroundMusicVolume: background_music_volume,
                musicDuration: music_duration,
                footageProvider: footage_provider || 'pexels',
                searchSafety: 'moderate'
              }}
              onChange={(settings) => {
                onChange('output_width', settings.imageWidth);
                onChange('output_height', settings.imageHeight);
                // Map aspect ratio back to video orientation
                if (settings.aspectRatio === '9:16') onChange('video_orientation', 'vertical');
                else if (settings.aspectRatio === '1:1') onChange('video_orientation', 'square');
                else onChange('video_orientation', 'horizontal');
              }}
            />
          </Grid>

          {/* Additional Video Settings (Frame Rate, Segment Duration) */}
          <Grid item xs={12}>
            <Box sx={{ 
              p: { xs: 1.5, sm: 2 }, 
              bgcolor: '#f8fafc', 
              borderRadius: 2, 
              border: '1px solid #e2e8f0', 
              mb: { xs: 1.5, sm: 2 }
            }}>
              <Typography 
                variant="subtitle1" 
                sx={{ 
                  fontWeight: 600, 
                  mb: 1, 
                  color: 'primary.main', 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1,
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  flexWrap: 'wrap'
                }}
              >
                ⚙️ Video Processing Settings
              </Typography>
              <Typography 
                variant="caption" 
                color="text.secondary"
                sx={{
                  fontSize: { xs: '0.7rem', sm: '0.75rem' },
                  lineHeight: 1.4
                }}
              >
                Configure frame rate and segment duration for video processing
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography 
              gutterBottom
              sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            >
              Segment Duration: {segment_duration}s
            </Typography>
            <Slider
              value={segment_duration}
              onChange={(_e, value) => onChange('segment_duration', Array.isArray(value) ? value[0] : value)}
              min={2}
              max={15}
              step={0.5}
              marks={[
                { value: 3, label: '3s' },
                { value: 6, label: '6s' },
                { value: 10, label: '10s' }
              ]}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="Frame Rate (FPS)"
              value={frame_rate}
              onChange={(e) => onChange('frame_rate', parseInt(e.target.value))}
              inputProps={{ min: 24, max: 60 }}
            />
          </Grid>

          {/* Caption Settings */}
          <Grid item xs={12}>
            <CaptionSettings
              enableCaptions={add_captions}
              captionStyle={caption_style}
              captionColor={caption_color}
              onEnableCaptionsChange={(enabled) => onChange('add_captions', enabled)}
              onCaptionStyleChange={(style) => onChange('caption_style', style)}
              onCaptionColorChange={caption_color !== undefined ? (color) => onChange('caption_color', color) : undefined}
            />
          </Grid>

          {/* Background Music Settings Section */}
          <Grid item xs={12}>
            <BackgroundMusicSettings
              backgroundMusic={background_music}
              backgroundMusicVolume={background_music_volume}
              musicDuration={music_duration}
              onBackgroundMusicChange={(value) => onChange('background_music', value)}
              onBackgroundMusicVolumeChange={(value) => onChange('background_music_volume', value)}
              onMusicDurationChange={music_duration !== undefined ? (value) => onChange('music_duration', value) : undefined}
            />
          </Grid>

          {/* Background Video Settings Section - Only show for footage-based workflows */}
          {!showImageSettings && (
            <Grid item xs={12}>
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <VideoIcon color="primary" />
                  Background Video Settings
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Choose how to source background videos for your content. Stock providers (Pexels, Pixabay) offer real footage from creators,
                  while AI generation creates custom videos from text prompts using the LTX-Video model via <code>/api/v1/videos/generate</code>.
                </Typography>

                <Grid container spacing={3}>
                  {/* Footage Provider */}
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel>Footage Provider</InputLabel>
                      <Select
                        value={footage_provider || 'pexels'}
                        onChange={(e) => onChange('footage_provider', e.target.value)}
                        label="Footage Provider"
                      >
                        {FOOTAGE_PROVIDERS.map((provider) => (
                          <MenuItem key={provider} value={provider}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                              <span style={{ fontSize: '1.2em' }}>{getProviderIcon(provider)}</span>
                              <Box sx={{ flexGrow: 1 }}>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {provider === 'ai_generated' ? 'AI Generated Videos' :
                                   provider.charAt(0).toUpperCase() + provider.slice(1)}
                                </Typography>
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                  {provider === 'pexels' && 'High-quality stock footage from professional creators'}
                                  {provider === 'pixabay' && 'Free stock videos with diverse content library'}
                                  {provider === 'ai_generated' && 'Custom videos generated from text prompts using LTX-Video (/api/v1/videos/generate)'}
                                </Typography>
                              </Box>
                              {provider === 'pexels' && <Chip label="Recommended" size="small" variant="outlined" />}
                              {provider === 'ai_generated' && <Chip label="Custom" size="small" variant="outlined" />}
                              <Chip
                                size="small"
                                label={provider === 'ai_generated' ? 'AI' : 'Stock'}
                                sx={{
                                  backgroundColor: getProviderCategoryColor(provider),
                                  color: 'white',
                                  fontSize: '0.7rem'
                                }}
                              />
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Search Safety */}
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel>Content Safety</InputLabel>
                      <Select
                        value={search_safety || 'moderate'}
                        onChange={(e) => onChange('search_safety', e.target.value)}
                        label="Content Safety"
                      >
                        {SEARCH_SAFETY_LEVELS.map((level) => (
                          <MenuItem key={level} value={level}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <span style={{ fontSize: '1.2em' }}>{getSafetyIcon(level)}</span>
                              <SafetyIcon fontSize="small" />
                              <Box sx={{ flexGrow: 1 }}>
                                {level.charAt(0).toUpperCase() + level.slice(1)}
                              </Box>
                              {level === 'moderate' && <Chip label="Recommended" size="small" variant="outlined" />}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Footage Quality */}
                  {footage_quality !== undefined && (
                    <Grid item xs={12} sm={6} md={4}>
                      <FormControl fullWidth variant="outlined">
                        <InputLabel>Footage Quality</InputLabel>
                        <Select
                          value={footage_quality || 'high'}
                          onChange={(e) => onChange('footage_quality', e.target.value)}
                          label="Footage Quality"
                        >
                          {FOOTAGE_QUALITIES.map((quality) => (
                            <MenuItem key={quality} value={quality}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <span style={{ fontSize: '1.2em' }}>{getQualityIcon(quality)}</span>
                                <QualityIcon fontSize="small" />
                                <Box sx={{ flexGrow: 1 }}>
                                  {quality.charAt(0).toUpperCase() + quality.slice(1)} Quality
                                </Box>
                                {quality === 'high' && <Chip label="Recommended" size="small" variant="outlined" />}
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  )}

                  {/* Search Terms Per Scene */}
                  {search_terms_per_scene !== undefined && (
                    <Grid item xs={12} sm={6} md={4}>
                      <FormControl fullWidth variant="outlined">
                        <InputLabel>Search Terms Per Scene</InputLabel>
                        <Select
                          value={search_terms_per_scene || 3}
                          onChange={(e) => onChange('search_terms_per_scene', Number(e.target.value))}
                          label="Search Terms Per Scene"
                        >
                          {[1, 2, 3, 4, 5].map((count) => (
                            <MenuItem key={count} value={count}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <span>{count} search term{count !== 1 ? 's' : ''}</span>
                                {count === 3 && <Chip label="Optimal" size="small" variant="outlined" />}
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  )}

                  {/* Current Settings Summary */}
                  <Grid item xs={12}>
                    <Box sx={{
                      p: 2,
                      backgroundColor: '#f8f9fa',
                      borderRadius: 1,
                      border: '1px solid #e2e8f0'
                    }}>
                      <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                        <VideoIcon />
                        Current Background Video Configuration
                      </Typography>
                      
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                        <Chip
                          label={(footage_provider || 'pexels').charAt(0).toUpperCase() + (footage_provider || 'pexels').slice(1).replace('_', ' ')}
                          size="small"
                          color="primary"
                        />
                        <Chip
                          label={`${(search_safety || 'moderate').charAt(0).toUpperCase() + (search_safety || 'moderate').slice(1)} Safety`}
                          size="small"
                          variant="outlined"
                        />
                        {footage_quality && (
                          <Chip
                            label={`${footage_quality.charAt(0).toUpperCase() + footage_quality.slice(1)} Quality`}
                            size="small"
                            variant="outlined"
                          />
                        )}
                        {search_terms_per_scene && (
                          <Chip
                            label={`${search_terms_per_scene} search terms`}
                            size="small"
                            variant="outlined"
                          />
                        )}
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary">
                        Background videos will be sourced from {(footage_provider || 'pexels').replace('_', ' ')} with {search_safety || 'moderate'} content filtering
                        {footage_quality && ` in ${footage_quality} quality`}
                        {search_terms_per_scene && `. Using ${search_terms_per_scene} search terms per scene for better relevance.`}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                {/* Provider Statistics */}
                <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    🎬 <strong>Available:</strong> {FOOTAGE_PROVIDERS.length} footage providers, {SEARCH_SAFETY_LEVELS.length} safety levels, {FOOTAGE_QUALITIES.length} quality options
                    • Current: {(footage_provider || 'pexels').replace('_', ' ')} with {search_safety || 'moderate'} safety
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    💡 <strong>Tip:</strong> Pexels offers the best variety and quality for most content. AI Generated creates unique videos but requires more processing time. Use "moderate" safety for balanced results.
                  </Typography>
                </Box>
              </Box>
            </Grid>
          )}

          {/* AI Image Generation & Provider Settings */}
          {showImageSettings && (
            <>
              <Grid item xs={12}>
                <Box sx={{ p: 2, bgcolor: '#f0f9ff', borderRadius: 2, border: '1px solid #0ea5e9', mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                    🎨 AI Image Generation & Provider Settings
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Configure AI image generation parameters, provider selection, and quality settings for custom visuals
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  type="number"
                  label="Image Width"
                  value={image_width || 1024}
                  onChange={(e) => onChange('image_width', parseInt(e.target.value))}
                  inputProps={{ min: 512, max: 2048, step: 64 }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  type="number"
                  label="Image Height"
                  value={image_height || 1024}
                  onChange={(e) => onChange('image_height', parseInt(e.target.value))}
                  inputProps={{ min: 512, max: 2048, step: 64 }}
                />
              </Grid>

              {/* Image Provider Settings - Now integrated within AI Image Generation section */}
              {showImageProviderSettings && (
                <Grid item xs={12}>
                  <Box sx={{ mt: 1 }}>
                    <ImageProviderSettings
                      imageProvider={image_provider || 'together'}
                      searchSafety="moderate"
                      guidanceScale={guidance_scale}
                      inferenceSteps={inference_steps}
                      onImageProviderChange={(value) => onChange('image_provider', value)}
                      onSearchSafetyChange={() => {}} // eslint-disable-line @typescript-eslint/no-empty-function
                      onGuidanceScaleChange={(value) => onChange('guidance_scale', value)}
                      onInferenceStepsChange={(value) => onChange('inference_steps', value)}
                    />
                  </Box>
                </Grid>
              )}
            </>
          )}

          {/* Visual Effects & Animations */}
          {showVideoEffects && video_effect !== undefined && (
            <>
              <Grid item xs={12}>
                <Box sx={{ p: 2, bgcolor: '#fef3c7', borderRadius: 2, border: '1px solid #f59e0b', mb: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1, color: 'primary.main', display: 'flex', alignItems: 'center', gap: 1 }}>
                    ✨ Visual Effects & Animations
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Apply motion effects and animations to enhance visual appeal of AI-generated images
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Video Effect</InputLabel>
                  <Select
                    value={video_effect}
                    label="Video Effect"
                    onChange={(e) => onChange('video_effect', e.target.value)}
                  >
                    {VIDEO_EFFECTS.map((effect) => (
                      <MenuItem key={effect.value} value={effect.value}>
                        {effect.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </>
          )}

        </Grid>
      </AccordionDetails>
    </Accordion>
  );
};

export default AdvancedVideoSettings;