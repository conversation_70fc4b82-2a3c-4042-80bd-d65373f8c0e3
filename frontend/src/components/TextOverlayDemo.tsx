import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  Stack,
  useMediaQuery,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import {
  Add,
  Edit,
  Preview,
  Download,
  Share,
  VideoLibrary,
  TextFields,
  Smartphone,
  Computer
} from '@mui/icons-material';
import TextOverlayEditor from './TextOverlayEditor';
import MobileTextOverlayEditor from './MobileTextOverlayEditor';
import { useTextOverlay, TextOverlayConfig } from '../hooks/useTextOverlay';

const TextOverlayDemo: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Component state
  const [editorOpen, setEditorOpen] = useState(false);
  const [forceMobileEditor, setForceMobileEditor] = useState(false);
  const [notification, setNotification] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Text overlay hook
  const {
    config,
    savedConfigs,
    isGenerating,
    previewUrl,
    updateConfig,
    saveConfig,
    loadConfig,
    generatePreview,
    generateVideo,
    exportConfig,
    getPreviewStyles
  } = useTextOverlay({
    autoSave: true,
    autoSaveDelay: 1000
  });

  // Notification helper
  const showNotification = (message: string, severity: 'success' | 'error' | 'info' = 'success') => {
    setNotification({ open: true, message, severity });
  };

  // Handle text overlay application
  const handleApplyOverlay = (newConfig: TextOverlayConfig) => {
    // Update the configuration
    Object.entries(newConfig).forEach(([key, value]) => {
      updateConfig(key as keyof TextOverlayConfig, value);
    });
    
    setEditorOpen(false);
    showNotification('Text overlay applied successfully!', 'success');
    
    // Auto-generate preview
    generatePreview();
  };

  // Handle video generation
  const handleGenerateVideo = async () => {
    // This would typically open a file picker or URL input dialog
    // For demo purposes, we'll simulate with a placeholder
    const dummyVideoFile = new File(['dummy'], 'sample-video.mp4', { type: 'video/mp4' });
    
    try {
      const jobId = await generateVideo(dummyVideoFile);
      if (jobId) {
        showNotification('Video processing started! Check the job status for updates.', 'info');
      }
    } catch (error) {
      showNotification('Failed to start video processing', 'error');
    }
  };

  // Sample video thumbnails for demo
  const sampleVideos = [
    { id: 1, title: 'Product Demo', duration: '0:30', thumbnail: '🎥' },
    { id: 2, title: 'Tutorial Intro', duration: '0:15', thumbnail: '📚' },
    { id: 3, title: 'Social Media Ad', duration: '0:10', thumbnail: '📱' },
    { id: 4, title: 'Brand Story', duration: '1:00', thumbnail: '🏢' }
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 4 }}>
        <Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Text Overlay Studio
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create professional text overlays for your videos with ease
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={1}>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => {
              setForceMobileEditor(false);
              setEditorOpen(true);
            }}
            size="large"
          >
            Create Overlay
          </Button>
          
          {!isMobile && (
            <Button
              variant="outlined"
              startIcon={<Smartphone />}
              onClick={() => {
                setForceMobileEditor(true);
                setEditorOpen(true);
              }}
            >
              Mobile Editor
            </Button>
          )}
        </Stack>
      </Stack>

      <Grid container spacing={4}>
        {/* Left Panel - Current Project */}
        <Grid item xs={12} lg={8}>
          {/* Current Text Overlay Preview */}
          <Paper elevation={2} sx={{ mb: 3, overflow: 'hidden' }}>
            <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'primary.contrastText' }}>
              <Typography variant="h6" fontWeight="bold">
                Live Preview
              </Typography>
            </Box>
            
            <Box 
              sx={{ 
                height: 400,
                bgcolor: 'grey.900',
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              {/* Background pattern */}
              <Box 
                sx={{ 
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundImage: `
                    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2%, transparent 0%),
                    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2%, transparent 0%)
                  `,
                  backgroundSize: '50px 50px'
                }}
              />
              
              {/* Text Overlay */}
              <Box
                component="div"
                sx={{
                  ...getPreviewStyles(),
                  maxWidth: '80%',
                  wordBreak: 'break-word'
                }}
              >
                {config.text || 'Click "Create Overlay" to get started'}
              </Box>
            </Box>
            
            <Box sx={{ p: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                startIcon={<Edit />}
                onClick={() => setEditorOpen(true)}
              >
                Edit Text
              </Button>
              <Button
                variant="outlined"
                startIcon={<Preview />}
                onClick={generatePreview}
                disabled={isGenerating}
              >
                {isGenerating ? 'Generating...' : 'Generate Preview'}
              </Button>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={exportConfig}
              >
                Export Config
              </Button>
              <Button
                variant="contained"
                startIcon={<VideoLibrary />}
                onClick={handleGenerateVideo}
                disabled={isGenerating}
              >
                Apply to Video
              </Button>
            </Box>
          </Paper>

          {/* Quick Actions */}
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              Quick Actions
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                  <TextFields fontSize="large" color="primary" />
                  <Typography variant="subtitle2" sx={{ mt: 1 }}>
                    Social Media
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Instagram, TikTok ready
                  </Typography>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                  <VideoLibrary fontSize="large" color="primary" />
                  <Typography variant="subtitle2" sx={{ mt: 1 }}>
                    Video Titles
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    YouTube, shorts
                  </Typography>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                  <Share fontSize="large" color="primary" />
                  <Typography variant="subtitle2" sx={{ mt: 1 }}>
                    Brand Content
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Professional overlays
                  </Typography>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                  <Computer fontSize="large" color="primary" />
                  <Typography variant="subtitle2" sx={{ mt: 1 }}>
                    Presentations
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Clean, readable text
                  </Typography>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Right Panel - Saved Configs & Sample Videos */}
        <Grid item xs={12} lg={4}>
          {/* Saved Configurations */}
          <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              Saved Configurations
            </Typography>
            
            {Object.keys(savedConfigs).length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                No saved configurations yet. Create and save your first overlay!
              </Typography>
            ) : (
              <Stack spacing={1}>
                {Object.entries(savedConfigs).map(([name, savedConfig]) => (
                  <Card key={name} variant="outlined" sx={{ p: 1 }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="subtitle2">{name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {savedConfig.text?.substring(0, 30) || 'No text'}
                          {(savedConfig.text?.length || 0) > 30 ? '...' : ''}
                        </Typography>
                      </Box>
                      <Button
                        size="small"
                        onClick={() => loadConfig(name)}
                      >
                        Load
                      </Button>
                    </Stack>
                  </Card>
                ))}
              </Stack>
            )}
          </Paper>

          {/* Sample Videos */}
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              Sample Videos
            </Typography>
            
            <Stack spacing={1}>
              {sampleVideos.map(video => (
                <Card key={video.id} variant="outlined" sx={{ p: 2 }}>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Typography variant="h6">{video.thumbnail}</Typography>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle2">{video.title}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Duration: {video.duration}
                      </Typography>
                    </Box>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => showNotification('Video selected! Apply your text overlay.', 'info')}
                    >
                      Select
                    </Button>
                  </Stack>
                </Card>
              ))}
            </Stack>
            
            <Button
              fullWidth
              variant="outlined"
              sx={{ mt: 2 }}
              onClick={() => showNotification('Upload feature coming soon!', 'info')}
            >
              Upload Your Video
            </Button>
          </Paper>
        </Grid>
      </Grid>

      {/* Text Overlay Editor Dialog */}
      <Dialog 
        open={editorOpen} 
        onClose={() => setEditorOpen(false)}
        maxWidth={false}
        fullWidth
        fullScreen={isMobile || forceMobileEditor}
        sx={{ 
          '& .MuiDialog-paper': { 
            width: isMobile || forceMobileEditor ? '100%' : '95vw',
            height: isMobile || forceMobileEditor ? '100vh' : '90vh',
            maxWidth: 'none',
            maxHeight: 'none'
          }
        }}
      >
        {(isMobile || forceMobileEditor) ? (
          <MobileTextOverlayEditor
            onApply={handleApplyOverlay}
            onClose={() => setEditorOpen(false)}
            initialConfig={config}
          />
        ) : (
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <DialogTitle sx={{ pb: 1 }}>
              <Stack direction="row" alignItems="center" justifyContent="space-between">
                <Typography variant="h6">Text Overlay Editor</Typography>
                <Stack direction="row" spacing={1}>
                  <Button
                    variant="outlined"
                    onClick={() => setForceMobileEditor(true)}
                    startIcon={<Smartphone />}
                  >
                    Mobile View
                  </Button>
                  <Button onClick={() => setEditorOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => handleApplyOverlay(config)}
                  >
                    Apply
                  </Button>
                </Stack>
              </Stack>
            </DialogTitle>
            <DialogContent sx={{ p: 0, flexGrow: 1, overflow: 'hidden' }}>
              <TextOverlayEditor />
            </DialogContent>
          </Box>
        )}
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={4000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          severity={notification.severity}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TextOverlayDemo;