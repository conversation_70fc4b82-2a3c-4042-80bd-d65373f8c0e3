import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Paper,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
} from '@mui/icons-material';

import { useVideoCreation, useVoices } from '../../../hooks/useContentCreation';
import { directApi } from '../../../utils/api';
import {
  VideoCreatorFormState,
  VideoCreationRequest,
  MediaSettings,
} from '../../../types/contentCreation';

import { JobStatusDisplay, CaptionSettings } from '../index';
import ScriptEditor from '../ScriptEditor';
import VoiceSelector from '../../settings/VoiceSelectorSettings';
import MediaSettingsComponent from '../../settings/MediaSettings';
import UnifiedMediaProviderSettings from '../../settings/UnifiedMediaProviderSettings';
import ImageToVideoSettings from '../../settings/ImageToVideoSettings';
import BackgroundMusicSettings from '../../settings/BackgroundMusicSettings';

const VideoCreatorTab: React.FC = () => {
  const {
    result,
    jobStatus,
    jobProgress,
    loading,
    error,
    isResumedJob,
    createVideo,
    resetState,
    setError
  } = useVideoCreation();

  const {
    voices,
    fetchVoices,
    loading: voicesLoading
  } = useVoices();

  const [formState, setFormState] = useState<VideoCreatorFormState & {
    mediaType: 'video' | 'image';
    aiImageProvider: string;
    guidanceScale: number;
    inferenceSteps: number;
    // Image-to-video motion settings
    effectType: string;
    zoomSpeed: number;
    panDirection: string;
    kenBurnsKeypoints: Array<{
      time: number;
      x: number;
      y: number;
      zoom: number;
    }>;
    // Simplified script editor settings
    autoDiscovery: boolean;
    // Additional research properties
    researchDepth: 'basic' | 'detailed' | 'comprehensive';
    targetAudience: string;
    contentStyle: 'educational' | 'entertaining' | 'viral' | 'informative';
  }>({
    topic: '',
    script: '',
    useCustomScript: true, // Default to custom script mode
    voiceProvider: 'kokoro',
    voiceName: 'af_bella',
    language: 'en',
    imageWidth: 1080,
    imageHeight: 1920,
    aspectRatio: '9:16',
    captionStyle: 'viral_bounce',
    captionColor: '#FFFFFF',
    captionPosition: 'bottom',
    fontSize: 48,
    fontFamily: 'Arial-Bold',
    wordsPerLine: 6,
    footageProvider: 'pexels',
    aiVideoProvider: 'wavespeed', // Default AI video provider (WaveSpeed AI, LTX-Video, ComfyUI)
    searchSafety: 'moderate',
    enableCaptions: true,
    backgroundMusic: 'chill',
    backgroundMusicVolume: 0.3,
    musicDuration: 60,
    footageQuality: 'high',
    // Add missing properties
    scriptType: 'facts',
    maxDuration: 60,
    ttsSpeed: 1.0,
    videoEffect: 'ken_burns',
    generateBackgroundMusic: false,
    // New advanced video settings
    frameRate: 30,
    segmentDuration: 3.0,
    searchTermsPerScene: 3,
    // New unified media provider settings
    mediaType: 'video', // Default to video mode
    aiImageProvider: 'together', // For when mediaType='image' and footageProvider='ai_generated'
    guidanceScale: 3.5,
    inferenceSteps: 4,
    // Image-to-video motion settings
    effectType: 'ken_burns', // Default to ken_burns effect
    zoomSpeed: 10, // Medium zoom speed
    panDirection: 'right', // Default pan direction
    kenBurnsKeypoints: [
      { time: 0, x: 0.2, y: 0.2, zoom: 1.2 },
      { time: 3, x: 0.8, y: 0.8, zoom: 1.0 }
    ], // Default Ken Burns keypoints
    // Simplified script editor settings - auto-discovery toggle
    autoDiscovery: false, // Default to manual mode
    // Additional research properties
    researchDepth: 'basic',
    targetAudience: 'general',
    contentStyle: 'entertaining',
  });

  const [isGeneratingScript, setIsGeneratingScript] = useState(false);
  const [isResearchingTopic, setIsResearchingTopic] = useState(false);
  const [researchResults, setResearchResults] = useState<{ title: string, content: string, sources: string[], language: string } | null>(null);

  useEffect(() => {
    fetchVoices();
  }, [fetchVoices]);

  const handleFormChange = <K extends keyof (VideoCreatorFormState & {
    mediaType: 'video' | 'image';
    aiImageProvider: string;
    guidanceScale: number;
    inferenceSteps: number;
    effectType: string;
    zoomSpeed: number;
    panDirection: string;
    kenBurnsKeypoints: Array<{
      time: number;
      x: number;
      y: number;
      zoom: number;
    }>;
    autoDiscovery: boolean;
    researchDepth: 'basic' | 'detailed' | 'comprehensive';
    targetAudience: string;
    contentStyle: 'educational' | 'entertaining' | 'viral' | 'informative';
  })>(
    field: K,
    value: (VideoCreatorFormState & {
      mediaType: 'video' | 'image';
      aiImageProvider: string;
      guidanceScale: number;
      inferenceSteps: number;
      effectType: string;
      zoomSpeed: number;
      panDirection: string;
      kenBurnsKeypoints: Array<{
        time: number;
        x: number;
        y: number;
        zoom: number;
      }>;
      autoDiscovery: boolean;
      researchDepth: 'basic' | 'detailed' | 'comprehensive';
      targetAudience: string;
      contentStyle: 'educational' | 'entertaining' | 'viral' | 'informative';
    })[K]
  ) => {
    // Clear research results when topic changes
    if (field === 'topic') {
      setResearchResults(null);
    }
    setFormState(prev => ({ ...prev, [field]: value }));
  };

  const handleGenerateScript = async () => {
    if (!formState.topic.trim()) {
      setError('Please enter a topic for script generation.');
      return;
    }

    setIsGeneratingScript(true);
    setError(null);

    try {
      const response = await directApi.generateScriptSync({
        topic: formState.topic,
        script_type: formState.scriptType,
        language: formState.language,
        max_duration: formState.maxDuration,
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to generate script');
      }

      const generatedScript = response.data?.script || '';
      
      if (!generatedScript?.trim()) {
        throw new Error('No script content received from API');
      }
      
      handleFormChange('script', generatedScript);
    } catch (err: unknown) {
      // Handle different types of errors
      let errorMsg = 'Failed to generate script';
      if (err instanceof Error) {
        errorMsg = err.message;
      } else if (err && typeof err === 'object' && 'response' in err) {
        const axiosError = err as { response?: { data?: { error?: string; detail?: string; message?: string } } };
        if (axiosError.response?.data?.error) {
          errorMsg = axiosError.response.data.error;
        } else if (axiosError.response?.data?.detail) {
          errorMsg = axiosError.response.data.detail;
        } else if (axiosError.response?.data?.message) {
          errorMsg = axiosError.response.data.message;
        }
      } else if (err && typeof err === 'object' && 'message' in err) {
        errorMsg = (err as { message: string }).message;
      }
      setError(`Script generation failed: ${errorMsg}`);
    } finally {
      setIsGeneratingScript(false);
    }
  };

  const handleGenerateFromResearch = async () => {
    if (!researchResults) {
      setError('No research results available. Please research the topic first.');
      return;
    }

    setIsGeneratingScript(true);
    setError(null);

    try {
      // Create an enhanced topic that includes research insights
      const enhancedTopic = `${formState.topic}\n\nResearch insights: ${researchResults.content.substring(0, 500)}...`;
      
      // Generate script using enhanced topic
      const response = await directApi.generateScriptSync({
        topic: enhancedTopic,
        script_type: formState.scriptType,
        language: formState.language,
        max_duration: formState.maxDuration,
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to generate script from research');
      }

      const generatedScript = response.data?.script || '';
      
      if (!generatedScript?.trim()) {
        throw new Error('No script content received from API');
      }
      
      handleFormChange('script', generatedScript);
      // Clear research results after successful script generation
      setResearchResults(null);
    } catch (err: unknown) {
      // Handle different types of errors
      let errorMsg = 'Failed to generate script from research';
      if (err instanceof Error) {
        errorMsg = err.message;
      } else if (err && typeof err === 'object' && 'response' in err) {
        const axiosError = err as { response?: { data?: { error?: string; detail?: string; message?: string } } };
        if (axiosError.response?.data?.error) {
          errorMsg = axiosError.response.data.error;
        } else if (axiosError.response?.data?.detail) {
          errorMsg = axiosError.response.data.detail;
        } else if (axiosError.response?.data?.message) {
          errorMsg = axiosError.response.data.message;
        }
      } else if (err && typeof err === 'object' && 'message' in err) {
        errorMsg = (err as { message: string }).message;
      }
      setError(`Script generation from research failed: ${errorMsg}`);
    } finally {
      setIsGeneratingScript(false);
    }
  };

  const handleResearchTopic = async () => {
    if (!formState.topic.trim()) {
      setError('Please enter a topic for research.');
      return;
    }

    setIsResearchingTopic(true);
    setError(null);
    setResearchResults(null); // Clear previous research

    try {
      // Use the topic research API endpoint
      const response = await directApi.researchTopic(formState.topic, formState.language);

      if (!response.success) {
        throw new Error(response.error || 'Failed to research topic');
      }

      const researchData = response.data;
      
      if (!researchData?.content?.trim()) {
        throw new Error('No content received from research');
      }
      
      // Store research results instead of directly setting script
      setResearchResults(researchData);
    } catch (err: unknown) {
      let errorMsg = 'Failed to research topic';
      if (err instanceof Error) {
        errorMsg = err.message;
      }
      setError(`Topic research failed: ${errorMsg}`);
    } finally {
      setIsResearchingTopic(false);
    }
  };

  const handleCreateVideo = async () => {
    // Validate input based on mode
    if (formState.autoDiscovery) {
      // Auto discovery mode - no script validation needed, pipeline will handle everything
      // Just ensure we have basic settings
    } else if (formState.mediaType === 'image') {
      // Image mode - script is required but topic can be optional 
      // (images can be used for content generation)
      if (!formState.script.trim()) {
        setError('Please enter or generate a script for your video.');
        return;
      }
    } else {
      // Video mode - requires script
      if (!formState.script.trim()) {
        setError('Please enter or generate a script for your video.');
        return;
      }
    }

    const request: VideoCreationRequest = {
      // Core content - handle different modes
      ...(formState.autoDiscovery ? {
        auto_topic: true,
        language: formState.language, // Include language for auto topic mode
      } : {
        // For image mode, we always need a script but topic is optional
        // The pipeline will extract image search terms from the script content automatically
        ...(formState.topic.trim() && { topic: formState.topic }),
        custom_script: formState.script || undefined,
        auto_topic: false,
        language: formState.language, // Include language for manual mode
      }),
      
      // Script generation options
      script_type: formState.scriptType,
      max_duration: formState.maxDuration,
      
      // Voice/TTS options
      voice: formState.voiceName,
      tts_provider: formState.voiceProvider,
      tts_speed: formState.ttsSpeed,
      
      // Media type selection (IMPORTANT: this parameter was missing!)
      media_type: formState.mediaType, // 'video' or 'image' - tells backend what media to fetch
      
      // Image generation options (for AI images)
      image_provider: formState.mediaType === 'image' && formState.footageProvider === 'ai_generated' ? formState.aiImageProvider : 'together',
      image_width: formState.imageWidth,
      image_height: formState.imageHeight,
      image_steps: formState.inferenceSteps,
      guidance_scale: formState.guidanceScale,
      
      // Video options
      video_effect: formState.videoEffect,
      resolution: `${formState.imageWidth}x${formState.imageHeight}`,
      aspect_ratio: formState.aspectRatio,
      
      // Image-to-video motion settings (only when mediaType is 'image')
      ...(formState.mediaType === 'image' && {
        effect_type: formState.effectType,
        zoom_speed: formState.zoomSpeed,
        pan_direction: formState.panDirection,
        ken_burns_keypoints: formState.kenBurnsKeypoints,
      }),
      
      // Video orientation mapping (for footage-to-video mode)
      orientation: formState.aspectRatio === '16:9' ? 'landscape' : 
                  formState.aspectRatio === '1:1' ? 'square' : 'portrait',
      
      // Audio options - fix background music mapping
      generate_background_music: formState.backgroundMusic === 'ai_generate',
      background_music: formState.backgroundMusic,
      background_music_volume: formState.backgroundMusicVolume,
      
      // Caption options - fix parameter name mapping
      add_captions: formState.enableCaptions,
      caption_style: formState.captionStyle,
      caption_color: formState.captionColor,
      caption_position: formState.captionPosition,
      font_size: formState.fontSize,
      font_family: formState.fontFamily,
      words_per_line: formState.wordsPerLine,
      
      // Advanced video settings
      frame_rate: formState.frameRate,
      segment_duration: formState.segmentDuration,
      search_terms_per_scene: formState.searchTermsPerScene,
      
      // Footage provider settings
      footage_provider: formState.footageProvider,
      ...(formState.footageProvider === 'ai_generated' && {
        ai_video_provider: formState.aiVideoProvider,
      }),
      search_safety: formState.searchSafety,
      footage_quality: formState.footageQuality,
      music_duration: formState.musicDuration,
    };

    try {
      await createVideo(request);
    } catch (err) {
      console.error('Failed to create video:', err);
    }
  };

  const handleReset = () => {
    resetState();
    setFormState({
      topic: '',
      script: '',
      useCustomScript: true,
      voiceProvider: 'kokoro',
      voiceName: 'af_bella',
      language: 'en',
      imageWidth: 1080,
      imageHeight: 1920,
      aspectRatio: '9:16',
      captionStyle: 'viral_bounce',
      captionColor: '#FFFFFF',
      captionPosition: 'bottom',
      fontSize: 48,
      fontFamily: 'Arial-Bold',
      wordsPerLine: 6,
      footageProvider: 'pexels',
      aiVideoProvider: 'wavespeed', // Default AI video provider (WaveSpeed AI, LTX-Video, ComfyUI)
      searchSafety: 'moderate',
      enableCaptions: true,
      backgroundMusic: 'chill',
      backgroundMusicVolume: 0.3,
      musicDuration: 60,
      footageQuality: 'high',
      // Add missing properties
      scriptType: 'facts',
      maxDuration: 60,
      ttsSpeed: 1.0,
      videoEffect: 'ken_burns',
      generateBackgroundMusic: false,
      // New advanced video settings
      frameRate: 30,
      segmentDuration: 3.0,
      searchTermsPerScene: 3,
      // New unified media provider settings
      mediaType: 'video',
      aiImageProvider: 'together',
      guidanceScale: 3.5,
      inferenceSteps: 4,
      // Image-to-video motion settings
      effectType: 'ken_burns',
      zoomSpeed: 10,
      panDirection: 'right',
      kenBurnsKeypoints: [
        { time: 0, x: 0.2, y: 0.2, zoom: 1.2 },
        { time: 3, x: 0.8, y: 0.8, zoom: 1.0 }
      ],
      // Simplified script editor settings - auto-discovery toggle
      autoDiscovery: false, // Default to manual mode
      // Additional research properties
      researchDepth: 'basic',
      targetAudience: 'general',
      contentStyle: 'entertaining',
    });
  };

  if (voicesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading voices...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      p: { xs: 2, sm: 3 }, 
      height: '100%', 
      overflow: 'auto',
      maxWidth: '100%'
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant="h5" 
          sx={{ 
            fontWeight: 600, 
            mb: 1, 
            display: 'flex', 
            alignItems: 'center',
            fontSize: { xs: '1.25rem', sm: '1.5rem' },
            flexWrap: 'wrap',
            gap: 1
          }}
        >
          <PlayIcon sx={{ color: '#3b82f6', fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
          Unified Video Creator
        </Typography>
        <Typography 
          variant="body2" 
          color="text.secondary"
          sx={{ 
            fontSize: { xs: '0.875rem', sm: '0.875rem' },
            lineHeight: 1.5
          }}
        >
          Create custom videos from scripts using stock footage, stock images, or AI-generated content. 
          Choose videos for traditional stock footage, or choose images to automatically convert stock photos 
          into video clips with motion effects (Ken Burns, zoom, pan). The system automatically finds relevant 
          visuals based on your script content.
        </Typography>
      </Box>

      {/* LTX Video Generator Promotion */}
      <Paper elevation={0} sx={{ 
        border: '1px solid #e2e8f0', 
        borderRadius: 2, 
        p: { xs: 2, sm: 2 }, 
        mb: 3, 
        backgroundColor: '#f0f9ff' 
      }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: { xs: 'flex-start', sm: 'center' }, 
          justifyContent: 'space-between',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 2, sm: 0 }
        }}>
          <Box sx={{ flex: 1 }}>
            <Typography 
              variant="h6" 
              sx={{ 
                fontWeight: 600, 
                color: '#0369a1',
                fontSize: { xs: '1rem', sm: '1.25rem' }
              }}
            >
              Try AI Video Generator
            </Typography>
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ 
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                lineHeight: 1.4
              }}
            >
              Generate high-quality AI videos directly from text prompts or animate your images using LTX-Video, WaveSpeed AI, or ComfyUI.
            </Typography>
          </Box>
          <Button
            variant="contained"
            href="/dashboard/ai-video"
            sx={{
              backgroundColor: '#0ea5e9',
              '&:hover': { backgroundColor: '#0284c7' },
              borderRadius: 2,
              px: { xs: 2, sm: 3 },
              py: { xs: 0.75, sm: 1 },
              fontSize: { xs: '0.8rem', sm: '0.875rem' },
              minWidth: { xs: '100%', sm: 'auto' },
              textAlign: 'center',
              whiteSpace: { xs: 'normal', sm: 'nowrap' }
            }}
          >
            Go to AI Video Generator
          </Button>
        </Box>
      </Paper>

      {/* Error Display */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Job Status */}
      {(loading || jobStatus) && (
        <JobStatusDisplay
          loading={loading}
          jobStatus={jobStatus}
          jobProgress={jobProgress}
          result={result}
          isResumedJob={isResumedJob}
          onReset={handleReset}
        />
      )}

      {/* Form Content */}
      {!loading && !jobStatus && (
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          gap: { xs: 2.5, sm: 3 },
          maxWidth: '100%'
        }}>
          {/* Script Editor */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2,
            overflow: 'hidden'
          }}>
            <ScriptEditor
              script={formState.script}
              scriptType={formState.scriptType}
              maxDuration={formState.maxDuration}
              topic={formState.topic}
              language={formState.language}
              isGeneratingScript={isGeneratingScript}
              isResearchingTopic={isResearchingTopic}
              hasResearchResults={!!researchResults}
              error={error}
              autoDiscovery={formState.autoDiscovery}
              // Callbacks
              onScriptChange={(script) => handleFormChange('script', script)}
              onScriptTypeChange={(type) => handleFormChange('scriptType', type)}
              onMaxDurationChange={(duration) => handleFormChange('maxDuration', duration)}
              onTopicChange={(topic) => handleFormChange('topic', topic)}
              onLanguageChange={(language) => handleFormChange('language', language)}
              onAutoDiscoveryChange={(enabled) => handleFormChange('autoDiscovery', enabled)}
              // Generation callbacks
              onGenerateScript={handleGenerateScript}
              onResearchTopic={handleResearchTopic}
              onGenerateFromResearch={handleGenerateFromResearch}
              onClearError={() => setError(null)}
            />
          </Paper>

          {/* Voice Settings */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2,
            overflow: 'hidden'
          }}>
            <VoiceSelector
              voiceProvider={formState.voiceProvider}
              voiceName={formState.voiceName}
              language={formState.language}
              ttsSpeed={formState.ttsSpeed}
              voices={voices}
              onVoiceProviderChange={(provider) => handleFormChange('voiceProvider', provider)}
              onVoiceNameChange={(name) => handleFormChange('voiceName', name)}
              onLanguageChange={(lang) => handleFormChange('language', lang)}
              onTtsSpeedChange={(speed) => handleFormChange('ttsSpeed', speed)}
            />
          </Paper>

          {/* Media Settings (Video Dimensions & Format) */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2,
            overflow: 'hidden'
          }}>
            <MediaSettingsComponent
              settings={{
                imageWidth: formState.imageWidth,
                imageHeight: formState.imageHeight,
                aspectRatio: formState.aspectRatio,
                frameRate: formState.frameRate,
                segmentDuration: formState.segmentDuration,
                captionStyle: formState.captionStyle,
                captionColor: formState.captionColor,
                captionPosition: formState.captionPosition,
                enableCaptions: formState.enableCaptions,
                backgroundMusic: formState.backgroundMusic,
                backgroundMusicVolume: formState.backgroundMusicVolume,
                musicDuration: formState.musicDuration,
                footageProvider: formState.footageProvider,
                searchSafety: formState.searchSafety,
                footageQuality: formState.footageQuality,
                searchTermsPerScene: formState.searchTermsPerScene,
              }}
              onChange={(settings: MediaSettings) => {
                handleFormChange('imageWidth', settings.imageWidth);
                handleFormChange('imageHeight', settings.imageHeight);
                handleFormChange('aspectRatio', settings.aspectRatio);
                if (settings.frameRate !== undefined) handleFormChange('frameRate', settings.frameRate);
                if (settings.segmentDuration !== undefined) handleFormChange('segmentDuration', settings.segmentDuration);
                if (settings.captionPosition !== undefined) handleFormChange('captionPosition', settings.captionPosition);
                if (settings.footageProvider !== undefined) handleFormChange('footageProvider', settings.footageProvider);
                if (settings.searchSafety !== undefined) handleFormChange('searchSafety', settings.searchSafety);
                if (settings.footageQuality !== undefined) handleFormChange('footageQuality', settings.footageQuality);
                if (settings.searchTermsPerScene !== undefined) handleFormChange('searchTermsPerScene', settings.searchTermsPerScene);
                if (settings.musicDuration !== undefined) handleFormChange('musicDuration', settings.musicDuration);
              }}
            />
          </Paper>

          {/* Unified Media Provider Settings */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2,
            overflow: 'hidden'
          }}>
            <UnifiedMediaProviderSettings
              mediaType={formState.mediaType}
              provider={formState.footageProvider as 'pexels' | 'pixabay' | 'ai_generated'}
              aiVideoProvider={formState.aiVideoProvider}
              aiImageProvider={formState.aiImageProvider}
              searchSafety={formState.searchSafety}
              quality={formState.footageQuality}
              searchTermsPerScene={formState.searchTermsPerScene}
              guidanceScale={formState.guidanceScale}
              inferenceSteps={formState.inferenceSteps}
              showAdvancedSettings={true}
              onMediaTypeChange={(type) => handleFormChange('mediaType', type)}
              onProviderChange={(provider) => handleFormChange('footageProvider', provider)}
              onAiVideoProviderChange={(provider) => handleFormChange('aiVideoProvider', provider)}
              onAiImageProviderChange={(provider) => handleFormChange('aiImageProvider', provider)}
              onSearchSafetyChange={(safety) => handleFormChange('searchSafety', safety)}
              onQualityChange={(quality) => handleFormChange('footageQuality', quality)}
              onSearchTermsPerSceneChange={(terms) => handleFormChange('searchTermsPerScene', terms)}
              onGuidanceScaleChange={(scale) => handleFormChange('guidanceScale', scale)}
              onInferenceStepsChange={(steps) => handleFormChange('inferenceSteps', steps)}
            />
          </Paper>

          {/* Image-to-Video Motion Settings (only shown when creating videos from images) */}
          {formState.mediaType === 'image' && (
            <Paper elevation={0} sx={{ 
              border: '1px solid #e2e8f0', 
              borderRadius: 2,
              overflow: 'hidden'
            }}>
              <ImageToVideoSettings
                effectType={formState.effectType}
                zoomSpeed={formState.zoomSpeed}
                panDirection={formState.panDirection}
                kenBurnsKeypoints={formState.kenBurnsKeypoints}
                videoLength={formState.maxDuration}
                onEffectTypeChange={(type) => handleFormChange('effectType', type)}
                onZoomSpeedChange={(speed) => handleFormChange('zoomSpeed', speed)}
                onPanDirectionChange={(direction) => handleFormChange('panDirection', direction)}
                onKenBurnsKeypointsChange={(keypoints) => handleFormChange('kenBurnsKeypoints', keypoints)}
              />
            </Paper>
          )}

          {/* Background Music Settings */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2,
            overflow: 'hidden'
          }}>
            <BackgroundMusicSettings
              backgroundMusic={formState.backgroundMusic}
              backgroundMusicVolume={formState.backgroundMusicVolume}
              musicDuration={formState.musicDuration}
              onBackgroundMusicChange={(music) => handleFormChange('backgroundMusic', music)}
              onBackgroundMusicVolumeChange={(volume) => handleFormChange('backgroundMusicVolume', volume)}
              onMusicDurationChange={(duration) => handleFormChange('musicDuration', duration)}
            />
          </Paper>

          {/* Caption Settings */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2,
            overflow: 'hidden'
          }}>
            <CaptionSettings
              enableCaptions={formState.enableCaptions}
              captionStyle={formState.captionStyle}
              captionColor={formState.captionColor}
              captionPosition={formState.captionPosition}
              fontSize={formState.fontSize}
              fontFamily={formState.fontFamily}
              wordsPerLine={formState.wordsPerLine}
              onEnableCaptionsChange={(enabled) => handleFormChange('enableCaptions', enabled)}
              onCaptionStyleChange={(style) => handleFormChange('captionStyle', style)}
              onCaptionColorChange={(color) => handleFormChange('captionColor', color)}
              onCaptionPositionChange={(position) => handleFormChange('captionPosition', position)}
              onFontSizeChange={(size) => handleFormChange('fontSize', size)}
              onFontFamilyChange={(family) => handleFormChange('fontFamily', family)}
              onWordsPerLineChange={(words) => handleFormChange('wordsPerLine', words)}
            />
          </Paper>

          {/* Create Button */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            pt: { xs: 1, sm: 2 },
            px: { xs: 0, sm: 0 }
          }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<PlayIcon />}
              onClick={handleCreateVideo}
              disabled={loading || (!formState.autoDiscovery && !formState.script.trim())}
              sx={{
                backgroundColor: '#3b82f6',
                '&:hover': { backgroundColor: '#2563eb' },
                borderRadius: 2,
                px: { xs: 3, sm: 4 },
                py: { xs: 1.25, sm: 1.5 },
                fontSize: { xs: '1rem', sm: '1.1rem' },
                fontWeight: 600,
                minWidth: { xs: '250px', sm: 'auto' },
                width: { xs: '100%', sm: 'auto' },
                maxWidth: { xs: '100%', sm: '300px' }
              }}
            >
              Create Video
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default VideoCreatorTab;