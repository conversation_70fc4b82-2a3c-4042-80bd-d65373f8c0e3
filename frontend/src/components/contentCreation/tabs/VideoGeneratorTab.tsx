import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Paper,
  Slider,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  AutoAwesome as AIIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { directApi } from '../../../utils/api';
import { useVideoCreation } from '../../../hooks/useContentCreation';
import { JobStatusDisplay } from '../index';
import { PostizScheduleDialog } from '../../PostizScheduleDialog';
import { ContentCreationJobResult, ContentCreationJobStatus } from '../../../types/contentCreation';
import { Job, JobStatus } from '../../../types/ouinhi';

interface VideoGeneratorFormState {
  prompt: string;
  provider: string;
  negativePrompt: string;
  width: number;
  height: number;
  dimensions: string; // New field to store selected dimensions
  numFrames: number;
  numInferenceSteps: number;
  guidanceScale: number;
  seed: number | null;
  imageFile: File | null;
  imageUrl: string;
  duration: number; // Duration in seconds for WaveSpeed (5 or 8)
}

// Common video resolution options
const VIDEO_DIMENSIONS = [
  { label: 'Landscape HD (832×480)', value: '832x480', width: 832, height: 480 },
  { label: 'Portrait HD (480×832)', value: '480x832', width: 480, height: 832 },
  { label: 'Square HD (704×704)', value: '704x704', width: 704, height: 704 },
  { label: 'Standard HD (704×480)', value: '704x480', width: 704, height: 480 },
  { label: 'Widescreen (1024×576)', value: '1024x576', width: 1024, height: 576 },
  { label: 'Square Standard (512×512)', value: '512x512', width: 512, height: 512 },
  { label: 'Vertical Story (480×854)', value: '480x854', width: 480, height: 854 },
  { label: 'Custom', value: 'custom', width: 832, height: 480 }
];

interface VideoGeneratorResult {
  video_url: string;
  prompt_used: string;
  negative_prompt_used: string;
  dimensions: {
    width: number;
    height: number;
  };
  num_frames: number;
  processing_time: number;
  provider_used: string;
  original_image_url?: string; // Only present for image-to-video results
}

const VideoGeneratorTab: React.FC = () => {
  const [formState, setFormState] = useState<VideoGeneratorFormState>({
    prompt: '',
    provider: 'wavespeed', // Default to WaveSpeed
    negativePrompt: '',
    width: 832,
    height: 480,
    dimensions: '832x480', // Default to Landscape HD
    numFrames: 150,
    numInferenceSteps: 200,
    guidanceScale: 4.5,
    seed: null,
    imageFile: null,
    imageUrl: '',
    duration: 5, // Default to 5 seconds for WaveSpeed
  });

  const [useImageInput, setUseImageInput] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [jobId, setJobId] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<string | null>(null);
  const [jobResult, setJobResult] = useState<VideoGeneratorResult | null>(null);
  
  // Schedule dialog state
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [generatedJob, setGeneratedJob] = useState<Job | null>(null);

  const {
    result: hookResult,
    jobStatus: hookJobStatus,
    jobProgress: hookJobProgress,
    loading: hookLoading,
    error: hookError,
    isResumedJob,
    resetState
  } = useVideoCreation();

  const handleFormChange = <K extends keyof VideoGeneratorFormState>(
    field: K,
    value: VideoGeneratorFormState[K]
  ) => {
    setFormState(prev => {
      const newState = { ...prev, [field]: value };
      
      // If provider changes to LTX-Video and dimensions are custom, ensure divisibility by 32
      if (field === 'provider' && value === 'ltx_video' && prev.dimensions === 'custom') {
        newState.width = Math.round(prev.width / 32) * 32;
        newState.height = Math.round(prev.height / 32) * 32;
      }
      
      return newState;
    });
  };

  const handleDimensionsChange = (dimensionValue: string) => {
    const selectedDimension = VIDEO_DIMENSIONS.find(d => d.value === dimensionValue);
    if (selectedDimension && selectedDimension.value !== 'custom') {
      setFormState(prev => ({
        ...prev,
        dimensions: dimensionValue,
        width: selectedDimension.width,
        height: selectedDimension.height
      }));
    } else {
      setFormState(prev => ({
        ...prev,
        dimensions: dimensionValue
      }));
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      handleFormChange('imageFile', file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setImagePreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCreateVideo = async () => {
    if (!formState.prompt.trim()) {
      setError('Please enter a prompt for the video.');
      return;
    }

    if (useImageInput && !formState.imageFile) {
      setError('Please select an image file.');
      return;
    }

    // Validate dimensions based on provider
    if (formState.provider === 'ltx_video') {
      if (formState.width % 32 !== 0 || formState.height % 32 !== 0) {
        setError('For LTX-Video, width and height must be divisible by 32.');
        return;
      }
    }

    setLoading(true);
    setError(null);
    setJobId(null);
    setJobStatus(null);
    setJobResult(null);

    try {
      if (useImageInput && formState.imageFile) {
        // Image-to-video generation
        const params = {
          prompt: formState.prompt,
          provider: formState.provider,
          image: formState.imageFile,
          negative_prompt: formState.negativePrompt,
          width: formState.width,
          height: formState.height,
          num_frames: formState.numFrames,
          num_inference_steps: formState.numInferenceSteps,
          guidance_scale: formState.guidanceScale,
          seed: formState.seed || undefined,
          duration: formState.provider === 'wavespeed' ? formState.duration : undefined
        };

        const response = await directApi.generateVideoFromImage(params);
        if (response.success && response.data?.job_id) {
          setJobId(response.data.job_id);
          pollJobStatus(response.data.job_id, 'image-to-video');
        } else {
          setError(response.error || 'Failed to generate video from image');
          setLoading(false);
        }
      } else {
        // Text-to-video generation
        const params = {
          prompt: formState.prompt,
          provider: formState.provider,
          negative_prompt: formState.negativePrompt,
          width: formState.width,
          height: formState.height,
          num_frames: formState.numFrames,
          num_inference_steps: formState.numInferenceSteps,
          guidance_scale: formState.guidanceScale,
          seed: formState.seed || undefined,
          duration: formState.provider === 'wavespeed' ? formState.duration : undefined
        };

        const response = await directApi.generateVideo(params);
        if (response.success && response.data?.job_id) {
          setJobId(response.data.job_id);
          pollJobStatus(response.data.job_id, 'text-to-video');
        } else {
          setError(response.error || 'Failed to generate video');
          setLoading(false);
        }
      }
    } catch (err) {
      console.error('Failed to create video:', err);
      setError('An error occurred while generating the video');
      setLoading(false);
    }
  };

  const pollJobStatus = async (jobId: string, jobType: string) => {
    setJobStatus('pending');
    
    try {
      let attempts = 0;
      const maxAttempts = 120; // 10 minutes max
      const pollInterval = 5000; // 5 seconds

      const poll = async () => {
        attempts++;
        
        try {
          let response;
          if (jobType === 'image-to-video') {
            response = await directApi.getVideoFromImageStatus(jobId);
          } else {
            response = await directApi.getVideoStatus(jobId);
          }
          
          if (response.success && response.data) {
            const job = response.data;
            setJobStatus(job.status);
            
            if (job.status === 'completed') {
              setJobResult(job.result);
              setLoading(false);
              return;
            } else if (job.status === 'failed') {
              setError(job.error || 'Video generation failed');
              setLoading(false);
              return;
            }
          }
          
          if (attempts >= maxAttempts) {
            setError('Video generation timed out');
            setLoading(false);
            return;
          }
          
          // Continue polling
          setTimeout(poll, pollInterval);
        } catch (err) {
          console.error('Error polling job status:', err);
          if (attempts >= maxAttempts) {
            setError('Failed to get job status');
            setLoading(false);
          } else {
            setTimeout(poll, pollInterval);
          }
        }
      };
      
      poll();
    } catch (err) {
      console.error('Error starting job polling:', err);
      setError('Failed to start job monitoring');
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFormState({
      prompt: '',
      provider: 'wavespeed',
      negativePrompt: '',
      width: 832,
      height: 480,
      dimensions: '832x480',
      numFrames: 150,
      numInferenceSteps: 200,
      guidanceScale: 4.5,
      seed: null,
      imageFile: null,
      imageUrl: '',
      duration: 5,
    });
    setImagePreview(null);
    setUseImageInput(false);
    setError(null);
    setLoading(false);
    setJobId(null);
    setJobStatus(null);
    setJobResult(null);
    resetState();
  };

  // Schedule dialog handlers
  const handleScheduleVideo = () => {
    // Check if we have a result from either the local state or the hook
    const currentResult = hookResult || (jobResult ? {
      status: 'completed',
      result: jobResult
    } : null);
    
    const currentJobId = jobId || (hookResult as ContentCreationJobResult)?.job_id;
    
    if (currentResult && currentResult.status === 'completed' && currentJobId) {
      const resultData = currentResult.result as VideoGeneratorResult;
      
      // Transform result to match expected job format
      setGeneratedJob({
        job_id: currentJobId,
        status: JobStatus.COMPLETED,
        result: {
          scheduling: {
            suggested_content: `🎬 Check out my AI-generated video! Created using ${formState.provider} with the prompt: "${formState.prompt}"`
          },
          final_video_url: resultData.video_url,
          video_url: resultData.video_url,
          file_url: resultData.video_url,
          prompt_used: resultData.prompt_used || formState.prompt,
          dimensions: resultData.dimensions,
          num_frames: resultData.num_frames,
          processing_time: resultData.processing_time,
          provider_used: resultData.provider_used || formState.provider,
          negative_prompt_used: resultData.negative_prompt_used,
          original_image_url: resultData.original_image_url
        }
      });
      setScheduleDialogOpen(true);
    }
  };

  const handleScheduleSubmit = async (data: {
    jobId: string;
    content: string;
    integrations: string[];
    postType: 'now' | 'schedule' | 'draft';
    scheduleDate?: Date;
    tags: string[];
  }) => {
    if (!generatedJob) return;
    
    try {
      const apiKey = localStorage.getItem('ouinhi_api_key');
      if (!apiKey) {
        throw new Error('API key not found');
      }

      const payload = {
        content: data.content,
        integrations: data.integrations,
        post_type: data.postType,
        schedule_date: data.scheduleDate?.toISOString(),
        tags: data.tags
      };

      const response = await directApi.post(
        `/api/v1/library/content/${jobId}/schedule`,
        payload
      );

      if (response.status === 200) {
        // Content scheduled successfully - could add user notification here
      }
    } catch (error) {
      console.error('Failed to schedule content:', error);
      throw error;
    }
  };

  const closeScheduleDialog = () => {
    setScheduleDialogOpen(false);
    setGeneratedJob(null);
  };

  const estimatedDuration = Math.round(formState.numFrames / 24);

  return (
    <Box sx={{ 
      p: { xs: 2, sm: 3 }, 
      height: '100%', 
      overflow: 'auto',
      maxWidth: '100%'
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant="h5" 
          sx={{ 
            fontWeight: 600, 
            mb: 1, 
            display: 'flex', 
            alignItems: 'center',
            fontSize: { xs: '1.25rem', sm: '1.5rem' },
            flexWrap: 'wrap',
            gap: 1
          }}
        >
          <AIIcon sx={{ 
            mr: { xs: 0, sm: 1 }, 
            color: '#8b5cf6',
            fontSize: { xs: '1.25rem', sm: '1.5rem' }
          }} />
          AI Video Generator
        </Typography>
        <Typography 
          variant="body2" 
          color="text.secondary"
          sx={{
            fontSize: { xs: '0.875rem', sm: '0.875rem' },
            lineHeight: 1.5
          }}
        >
          Generate high-quality videos from text prompts or animate images using AI models (LTX-Video, WaveSpeed, ComfyUI).
        </Typography>
      </Box>

      {/* Error Display */}
      {(error || hookError) && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          onClose={() => {
            setError(null);
            // Note: hookError is managed by the useVideoCreation hook
          }}
        >
          {error || hookError}
        </Alert>
      )}

      {/* Job Status with enhanced display like VideoCreatorTab */}
      {(loading || jobStatus || hookLoading || hookJobStatus) && (
        <JobStatusDisplay
          loading={loading || hookLoading}
          jobStatus={(jobStatus || hookJobStatus) as ContentCreationJobStatus}
          jobProgress={hookJobProgress || 'Processing your AI video generation...'}
          result={hookResult || (jobResult && jobId ? {
            job_id: jobId,
            status: 'completed' as const,
            result: {
              final_video_url: jobResult.video_url,
              video_url: jobResult.video_url,
              prompt_used: jobResult.prompt_used,
              dimensions: jobResult.dimensions,
              num_frames: jobResult.num_frames,
              processing_time: jobResult.processing_time,
              provider_used: jobResult.provider_used,
              negative_prompt_used: jobResult.negative_prompt_used,
              original_image_url: jobResult.original_image_url
            }
          } : null)}
          isResumedJob={isResumedJob}
          onReset={handleReset}
          onSchedule={(jobResult && jobId) || (hookResult && hookResult.status === 'completed') ? handleScheduleVideo : undefined}
        />
      )}

      {/* Form Content */}
      {!loading && !jobStatus && !hookLoading && !hookJobStatus && (
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          gap: { xs: 2.5, sm: 3 },
          maxWidth: '100%'
        }}>
          {/* Provider Selection */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2, 
            p: { xs: 2, sm: 3 },
            overflow: 'hidden'
          }}>
            <Typography 
              variant="h6" 
              sx={{ 
                mb: 2, 
                fontWeight: 600,
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              AI Video Provider
            </Typography>
            
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Provider</InputLabel>
                  <Select
                    value={formState.provider}
                    onChange={(e) => handleFormChange('provider', e.target.value)}
                    label="Provider"
                  >
                    <MenuItem value="wavespeed">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>🌊</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            WaveSpeed AI
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            Fast video generation with multiple models
                          </Typography>
                        </Box>
                        <Chip label="Fast" size="small" variant="outlined" />
                      </Box>
                    </MenuItem>
                    <MenuItem value="ltx_video">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>⚡</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            LTX-Video
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            High-quality video generation with fine control
                          </Typography>
                        </Box>
                        <Chip label="High Quality" size="small" variant="outlined" />
                      </Box>
                    </MenuItem>
                    <MenuItem value="comfyui">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>🎛️</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            ComfyUI
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            Custom video generation using ComfyUI workflows
                          </Typography>
                        </Box>
                        <Chip label="Custom" size="small" variant="outlined" />
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box sx={{ 
                  p: 2, 
                  backgroundColor: '#f8f9fa', 
                  borderRadius: 1,
                  border: '1px solid #e2e8f0'
                }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    {formState.provider === 'wavespeed' ? '🌊 WaveSpeed Features' : 
                     formState.provider === 'comfyui' ? '🎛️ ComfyUI Features' : '⚡ LTX-Video Features'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formState.provider === 'wavespeed' 
                      ? 'Duration: 1-8 seconds • Models: WAN-2.2, MiniMax • Fast processing'
                      : formState.provider === 'comfyui'
                      ? 'Custom workflows • WAN-2.2 model • Variable processing time'
                      : 'Frames: 1-257 • Dimensions: divisible by 32 • Fine control'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Input Mode Toggle */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2, 
            p: { xs: 2, sm: 3 },
            overflow: 'hidden'
          }}>
            <FormControlLabel
              control={
                <Switch
                  checked={useImageInput}
                  onChange={(e) => setUseImageInput(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Typography 
                  variant="h6" 
                  sx={{ 
                    fontWeight: 600,
                    fontSize: { xs: '1.1rem', sm: '1.25rem' }
                  }}
                >
                  {useImageInput ? 'Image-to-Video Mode' : 'Text-to-Video Mode'}
                </Typography>
              }
            />
            <Typography 
              variant="body2" 
              color="text.secondary" 
              sx={{ 
                mt: 1,
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                lineHeight: 1.4
              }}
            >
              {useImageInput
                ? 'Animate an existing image with a text prompt'
                : 'Generate a video from a text prompt'}
            </Typography>
          </Paper>

          {/* Prompt Input */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2, 
            p: { xs: 2, sm: 3 },
            overflow: 'hidden'
          }}>
            <Typography 
              variant="h6" 
              sx={{ 
                mb: 2, 
                fontWeight: 600,
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              {useImageInput ? 'Animation Prompt' : 'Video Prompt'}
            </Typography>
            
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={useImageInput ? "Describe how you want the image to animate" : "Describe the video you want to generate"}
                  multiline
                  rows={4}
                  value={formState.prompt}
                  onChange={(e) => handleFormChange('prompt', e.target.value)}
                  placeholder={useImageInput
                    ? "e.g., make the flowers bloom and butterflies fly around"
                    : "e.g., A cat walking through a garden with flowers blooming"}
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Negative Prompt (What to avoid)"
                  multiline
                  rows={2}
                  value={formState.negativePrompt}
                  onChange={(e) => handleFormChange('negativePrompt', e.target.value)}
                  placeholder="e.g., blurry, low quality, distorted"
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Image Input (only shown in image-to-video mode) */}
          {useImageInput && (
            <Paper elevation={0} sx={{ 
              border: '1px solid #e2e8f0', 
              borderRadius: 2, 
              p: { xs: 2, sm: 3 },
              overflow: 'hidden'
            }}>
              <Typography 
                variant="h6" 
                sx={{ 
                  mb: 2, 
                  fontWeight: 600,
                  fontSize: { xs: '1.1rem', sm: '1.25rem' }
                }}
              >
                Input Image
              </Typography>
              
              <Grid container spacing={{ xs: 2, sm: 3 }}>
                <Grid item xs={12} sm={6}>
                  <Button
                    variant="outlined"
                    component="label"
                    fullWidth
                    sx={{ height: '100%' }}
                  >
                    <ImageIcon sx={{ mr: 1 }} />
                    {formState.imageFile ? 'Change Image' : 'Select Image'}
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </Button>
                  {formState.imageFile && (
                    <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                      {formState.imageFile.name}
                    </Typography>
                  )}
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  {imagePreview ? (
                    <Box sx={{ textAlign: 'center' }}>
                      <img
                        src={imagePreview}
                        alt="Preview"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '200px',
                          borderRadius: '8px',
                          border: '1px solid #e2e8f0'
                        }}
                      />
                    </Box>
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '200px',
                      border: '2px dashed #e2e8f0',
                      borderRadius: '8px',
                      color: '#94a3b8'
                    }}>
                      <Typography>No image selected</Typography>
                    </Box>
                  )}
                </Grid>
              </Grid>
            </Paper>
          )}

          {/* Video Settings */}
          <Paper elevation={0} sx={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: 2, 
            p: { xs: 2, sm: 3 },
            overflow: 'hidden'
          }}>
            <Typography 
              variant="h6" 
              sx={{ 
                mb: 2, 
                fontWeight: 600,
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              Video Settings
            </Typography>
            
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} sm={formState.dimensions === 'custom' ? 6 : 12} md={formState.dimensions === 'custom' ? 4 : 6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Video Dimensions</InputLabel>
                  <Select
                    value={formState.dimensions}
                    onChange={(e) => handleDimensionsChange(e.target.value)}
                    label="Video Dimensions"
                  >
                    {VIDEO_DIMENSIONS.map((dimension) => (
                      <MenuItem key={dimension.value} value={dimension.value}>
                        {dimension.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {formState.dimensions === 'custom' && (
                <>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label={formState.provider === 'ltx_video' ? 'Width (div by 32)' : 'Width'}
                      type="number"
                      value={formState.width}
                      onChange={(e) => handleFormChange('width', parseInt(e.target.value) || (formState.provider === 'wavespeed' ? 832 : 704))}
                      InputProps={{ 
                        inputProps: formState.provider === 'ltx_video' 
                          ? { min: 32, max: 1024, step: 32 }
                          : { min: 256, max: 1024 }
                      }}
                      variant="outlined"
                      size="medium"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label={formState.provider === 'ltx_video' ? 'Height (div by 32)' : 'Height'}
                      type="number"
                      value={formState.height}
                      onChange={(e) => handleFormChange('height', parseInt(e.target.value) || 480)}
                      InputProps={{ 
                        inputProps: formState.provider === 'ltx_video' 
                          ? { min: 32, max: 1024, step: 32 }
                          : { min: 256, max: 1024 }
                      }}
                      variant="outlined"
                      size="medium"
                    />
                  </Grid>
                </>
              )}

              {formState.dimensions !== 'custom' && (
                <Grid item xs={12} sm={6}>
                  <Box sx={{ pt: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Selected: {formState.width} × {formState.height} pixels
                    </Typography>
                  </Box>
                </Grid>
              )}
              
              <Grid item xs={12} md={6}>
                <Box>
                  {formState.provider === 'ltx_video' ? (
                    <>
                      <Typography gutterBottom>Number of Frames (~{estimatedDuration}s)</Typography>
                      <Slider
                        value={formState.numFrames}
                        onChange={(_, value) => handleFormChange('numFrames', value as number)}
                        min={1}
                        max={257}
                        step={1}
                        marks={[
                          { value: 19, label: '19 (~0.8s)' },
                          { value: 75, label: '75 (~3s)' },
                          { value: 150, label: '150 (~6s)' },
                          { value: 257, label: '257 (~11s)' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </>
                  ) : formState.provider === 'wavespeed' ? (
                    <>
                      <Typography gutterBottom>Duration (WaveSpeed supports 5 or 8 seconds)</Typography>
                      <FormControl fullWidth variant="outlined">
                        <InputLabel>Duration</InputLabel>
                        <Select
                          value={formState.duration}
                          onChange={(e) => handleFormChange('duration', e.target.value as number)}
                          label="Duration"
                        >
                          <MenuItem value={5}>5 seconds</MenuItem>
                          <MenuItem value={8}>8 seconds</MenuItem>
                        </Select>
                      </FormControl>
                    </>
                  ) : (
                    <>
                      <Typography gutterBottom>Duration (seconds)</Typography>
                      <Slider
                        value={Math.min(Math.round(formState.numFrames / 15), 8)}
                        onChange={(_, value) => handleFormChange('numFrames', (value as number) * 15)}
                        min={1}
                        max={8}
                        step={1}
                        marks={[
                          { value: 1, label: '1s' },
                          { value: 3, label: '3s' },
                          { value: 5, label: '5s' },
                          { value: 8, label: '8s' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </>
                  )}
                </Box>
              </Grid>
              
              {formState.provider === 'ltx_video' && (
                <>
                  <Grid item xs={12} sm={6}>
                    <Box>
                      <Typography gutterBottom>Inference Steps</Typography>
                      <Slider
                        value={formState.numInferenceSteps}
                        onChange={(_, value) => handleFormChange('numInferenceSteps', value as number)}
                        min={1}
                        max={500}
                        step={1}
                        marks={[
                          { value: 20, label: '20' },
                          { value: 100, label: '100' },
                          { value: 200, label: '200' },
                          { value: 500, label: '500' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Box>
                      <Typography gutterBottom>Guidance Scale</Typography>
                      <Slider
                        value={formState.guidanceScale}
                        onChange={(_, value) => handleFormChange('guidanceScale', value as number)}
                        min={1}
                        max={20}
                        step={0.5}
                        marks={[
                          { value: 1, label: '1' },
                          { value: 4.5, label: '4.5' },
                          { value: 10, label: '10' },
                          { value: 20, label: '20' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </Box>
                  </Grid>
                </>
              )}
              
              {formState.provider === 'wavespeed' && (
                <Grid item xs={12} sm={6}>
                  <Box sx={{ 
                    p: 2, 
                    backgroundColor: '#f0f9ff', 
                    borderRadius: 1,
                    border: '1px solid #e2e8f0'
                  }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      🌊 WaveSpeed Settings
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      • Model: WAN-2.2 (ultra-fast)
                      <br />• Auto-optimized inference steps
                      <br />• Smart quality balancing
                    </Typography>
                  </Box>
                </Grid>
              )}
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Seed (optional)"
                  type="number"
                  value={formState.seed || ''}
                  onChange={(e) => handleFormChange('seed', e.target.value ? parseInt(e.target.value) : null)}
                  placeholder="Leave blank for random"
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Create Button */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            pt: { xs: 1, sm: 2 },
            px: { xs: 0, sm: 0 }
          }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<PlayIcon />}
              onClick={handleCreateVideo}
              disabled={loading || !formState.prompt.trim() || (useImageInput && !formState.imageFile)}
              sx={{
                backgroundColor: '#8b5cf6',
                '&:hover': { backgroundColor: '#7c3aed' },
                borderRadius: 2,
                px: { xs: 3, sm: 4 },
                py: { xs: 1.25, sm: 1.5 },
                fontSize: { xs: '1rem', sm: '1.1rem' },
                fontWeight: 600,
                minWidth: { xs: '250px', sm: 'auto' },
                width: { xs: '100%', sm: 'auto' },
                maxWidth: { xs: '100%', sm: '300px' }
              }}
            >
              {useImageInput ? 'Generate Video from Image' : 'Generate Video from Text'}
            </Button>
          </Box>
        </Box>
      )}

      {/* Schedule Dialog */}
      {generatedJob && (
        <PostizScheduleDialog
          open={scheduleDialogOpen}
          onClose={closeScheduleDialog}
          job={generatedJob}
          onSchedule={handleScheduleSubmit}
        />
      )}
    </Box>
  );
};

export default VideoGeneratorTab;