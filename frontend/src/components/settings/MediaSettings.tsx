import React from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  TextField,
  Slider,
  Tooltip,
} from '@mui/material';
import {
  AspectRatio as DimensionsIcon,
  Speed as FrameRateIcon,
  Timer as DurationIcon,
  Settings as AdvancedIcon,
} from '@mui/icons-material';

import type { MediaSettings as MediaSettingsType } from '../../types/contentCreation';
import {
  IMAGE_PRESETS,
  ASPECT_RATIOS,
  FRAME_RATES,
  SEGMENT_DURATIONS,
} from '../../types/contentCreation';

interface MediaSettingsProps {
  settings: MediaSettingsType;
  // eslint-disable-next-line no-unused-vars
  onChange: (settings: MediaSettingsType) => void;
}

const MediaSettings: React.FC<MediaSettingsProps> = ({
  settings,
  onChange,
}) => {
  const handleSettingChange = <K extends keyof MediaSettingsType>(
    field: K,
    value: MediaSettingsType[K]
  ) => {
    onChange({ ...settings, [field]: value });
  };

  const handlePresetChange = (presetName: string) => {
    const preset = IMAGE_PRESETS.find(p => p.name === presetName);
    if (preset) {
      onChange({
        ...settings,
        imageWidth: preset.width,
        imageHeight: preset.height,
        aspectRatio: preset.aspectRatio
      });
    }
  };

  const getCurrentPresetName = () => {
    const preset = IMAGE_PRESETS.find(p => 
      p.width === settings.imageWidth && 
      p.height === settings.imageHeight
    );
    return preset?.name || 'Custom';
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: { xs: 2, sm: 3 }, 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          fontSize: { xs: '1.1rem', sm: '1.25rem' },
          flexWrap: 'wrap'
        }}
      >
        <DimensionsIcon color="primary" sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }} />
        Video Dimensions & Format
      </Typography>

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        
        {/* Preset Selector */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Dimension Preset</InputLabel>
            <Select
              value={getCurrentPresetName()}
              onChange={(e) => handlePresetChange(e.target.value)}
              label="Dimension Preset"
            >
              {IMAGE_PRESETS.map((preset) => (
                <MenuItem key={preset.name} value={preset.name}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {preset.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {preset.width} × {preset.height}
                      </Typography>
                    </Box>
                    <Chip label={preset.aspectRatio} size="small" variant="outlined" />
                  </Box>
                </MenuItem>
              ))}
              <MenuItem value="Custom">
                <Box>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    Custom Dimensions
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Set custom width and height
                  </Typography>
                </Box>
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Aspect Ratio */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Aspect Ratio</InputLabel>
            <Select
              value={settings.aspectRatio}
              onChange={(e) => handleSettingChange('aspectRatio', e.target.value)}
              label="Aspect Ratio"
            >
              {ASPECT_RATIOS.map((ratio) => (
                <MenuItem key={ratio} value={ratio}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {ratio}
                    {ratio === '9:16' && <Chip label="TikTok/Reels" size="small" variant="outlined" />}
                    {ratio === '16:9' && <Chip label="YouTube" size="small" variant="outlined" />}
                    {ratio === '1:1' && <Chip label="Instagram" size="small" variant="outlined" />}
                    {ratio === '4:5' && <Chip label="Stories" size="small" variant="outlined" />}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Custom Dimensions */}
        <Grid item xs={12} sm={6}>
          <TextField
            label="Video Width (px)"
            type="number"
            fullWidth
            variant="outlined"
            value={settings.imageWidth}
            onChange={(e) => handleSettingChange('imageWidth', parseInt(e.target.value) || 1080)}
            inputProps={{ min: 240, max: 4096 }}
            helperText="240-4096 pixels recommended"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Video Height (px)"
            type="number"
            fullWidth
            variant="outlined"
            value={settings.imageHeight}
            onChange={(e) => handleSettingChange('imageHeight', parseInt(e.target.value) || 1920)}
            inputProps={{ min: 240, max: 4096 }}
            helperText="240-4096 pixels recommended"
          />
        </Grid>

        {/* Advanced Video Settings */}
        <Grid item xs={12}>
          <Typography 
            variant="h6" 
            sx={{ 
              mb: { xs: 1.5, sm: 2 }, 
              display: 'flex', 
              alignItems: 'center', 
              gap: 1,
              fontSize: { xs: '1rem', sm: '1.25rem' },
              flexWrap: 'wrap'
            }}
          >
            <AdvancedIcon color="primary" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />
            Advanced Video Settings
          </Typography>
        </Grid>

        {/* Frame Rate */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Frame Rate (FPS)</InputLabel>
            <Select
              value={settings.frameRate || 30}
              onChange={(e) => handleSettingChange('frameRate', Number(e.target.value))}
              label="Frame Rate (FPS)"
            >
              {FRAME_RATES.map((fps) => (
                <MenuItem key={fps} value={fps}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <FrameRateIcon fontSize="small" />
                    {fps} FPS
                    {fps === 30 && <Chip label="Standard" size="small" variant="outlined" />}
                    {fps === 60 && <Chip label="Smooth" size="small" variant="outlined" />}
                    {fps === 24 && <Chip label="Cinematic" size="small" variant="outlined" />}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Segment Duration */}
        <Grid item xs={12} sm={6} md={4}>
          <Typography 
            variant="body2" 
            sx={{ 
              mb: 1, 
              display: 'flex', 
              alignItems: 'center', 
              gap: 1,
              fontSize: { xs: '0.875rem', sm: '0.875rem' },
              flexWrap: 'wrap'
            }}
          >
            <DurationIcon fontSize="small" color="primary" />
            Video Clip Duration: {settings.segmentDuration || 3.0}s
            <Tooltip title="Duration of each background video clip. Shorter clips create more dynamic videos.">
              <AdvancedIcon fontSize="small" color="action" />
            </Tooltip>
          </Typography>
          <Slider
            value={settings.segmentDuration || 3.0}
            onChange={(_, value) => handleSettingChange('segmentDuration', Array.isArray(value) ? value[0] : value)}
            min={2.0}
            max={8.0}
            step={0.5}
            marks={SEGMENT_DURATIONS.map(duration => ({ value: duration, label: `${duration}s` }))}
            sx={{ mt: 1 }}
            valueLabelDisplay="auto"
            valueLabelFormat={(value) => `${value}s`}
          />
        </Grid>



        {/* Current Settings Summary */}
        <Grid item xs={12}>
          <Box sx={{ 
            p: { xs: 1.5, sm: 2 }, 
            backgroundColor: '#f8f9fa', 
            borderRadius: 1,
            border: '1px solid #e2e8f0'
          }}>
            <Typography 
              variant="subtitle2" 
              sx={{ 
                mb: 1,
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}
            >
              Current Video Configuration
            </Typography>
            
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: { xs: 0.75, sm: 1 }, 
              mb: 1 
            }}>
              <Chip 
                label={`${settings.imageWidth}×${settings.imageHeight}`}
                size="small" 
                color="primary"
              />
              <Chip 
                label={settings.aspectRatio}
                size="small" 
                variant="outlined"
              />
              <Chip 
                label={`${settings.frameRate || 30} FPS`}
                size="small" 
                variant="outlined"
              />
              <Chip 
                label={`${settings.segmentDuration || 3.0}s clips`}
                size="small" 
                variant="outlined"
              />
              <Chip 
                label={getCurrentPresetName()}
                size="small" 
                variant="outlined"
              />
            </Box>
            
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                lineHeight: 1.4
              }}
            >
              Video will be rendered at these dimensions with {settings.frameRate || 30} FPS. 
              Background footage clips will be {settings.segmentDuration || 3.0} seconds each.
            </Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Complete Summary */}
      <Box sx={{ 
        mt: { xs: 2, sm: 3 }, 
        p: { xs: 1.5, sm: 2 }, 
        backgroundColor: '#f0f9ff', 
        borderRadius: 1 
      }}>
        <Typography 
          variant="body2" 
          color="text.secondary"
          sx={{
            fontSize: { xs: '0.8rem', sm: '0.875rem' },
            lineHeight: 1.4
          }}
        >
          📐 <strong>Video:</strong> {settings.imageWidth}×{settings.imageHeight} at {settings.frameRate || 30} FPS ({settings.aspectRatio})
          <br />
          <strong>Clips:</strong> {settings.segmentDuration || 3.0}s duration each
        </Typography>
      </Box>
    </Box>
  );
};

export default MediaSettings;