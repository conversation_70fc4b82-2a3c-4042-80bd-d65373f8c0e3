import React, { useState, useCallback, useMemo } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Slider,
  Button,
  ButtonGroup,
  Tabs,
  Tab,
  Grid,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Chip,
  Stack,
  ToggleButtonGroup,
  ToggleButton,
  Divider,
  Switch,
  FormControlLabel,
  Tooltip,
  Avatar,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  FormatItalic,
  FormatUnderlined,
  FormatAlignLeft,
  FormatAlignCenter,
  FormatAlignRight,
  Palette,
  Animation,
  Timeline,
  LocationOn,
  TextFields,
  PlayArrow,
  Pause,
  Refresh,
  Save,
  Download,
  Upload,
  Star,
  StarOutline,
  AutoAwesome,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';

// Types
interface TextOverlayConfig {
  text: string;
  position: {
    x: number;
    y: number;
    alignment: 'left' | 'center' | 'right';
  };
  typography: {
    fontFamily: string;
    fontSize: number;
    fontWeight: number;
    fontStyle: 'normal' | 'italic';
    letterSpacing: number;
    lineHeight: number;
    textDecoration: 'none' | 'underline' | 'overline' | 'line-through';
  };
  colors: {
    text: string;
    background: string;
    stroke: string;
  };
  effects: {
    shadow: {
      enabled: boolean;
      offsetX: number;
      offsetY: number;
      blur: number;
      color: string;
    };
    gradient: {
      enabled: boolean;
      type: 'linear' | 'radial';
      colors: string[];
      direction: number;
    };
    stroke: {
      enabled: boolean;
      width: number;
    };
    glow: {
      enabled: boolean;
      color: string;
      intensity: number;
    };
  };
  animation: {
    enabled: boolean;
    type: 'fadeIn' | 'slideIn' | 'bounce' | 'typewriter' | 'pulse' | 'rotate';
    duration: number;
    delay: number;
    easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
    loop: boolean;
  };
  timing: {
    startTime: number;
    duration: number;
  };
  responsive: {
    mobile: Partial<TextOverlayConfig>;
    tablet: Partial<TextOverlayConfig>;
  };
}

interface PresetTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  config: Partial<TextOverlayConfig>;
  tags: string[];
  popular: boolean;
}

// Sample presets with modern styling
const PRESET_TEMPLATES: PresetTemplate[] = [
  {
    id: 'modern_title',
    name: 'Modern Title',
    description: 'Clean, bold title with subtle shadow',
    thumbnail: '🎬',
    popular: true,
    tags: ['title', 'modern', 'clean'],
    config: {
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: 48,
        fontWeight: 700,
        fontStyle: 'normal' as const,
        letterSpacing: -1,
        lineHeight: 1.2,
        textDecoration: 'none' as const
      },
      colors: { text: '#ffffff', background: 'transparent', stroke: '#000000' },
      effects: {
        shadow: { enabled: true, offsetX: 2, offsetY: 2, blur: 8, color: 'rgba(0,0,0,0.3)' },
        gradient: { enabled: false, type: 'linear' as const, colors: [], direction: 0 },
        stroke: { enabled: false, width: 0 },
        glow: { enabled: false, color: '#ffffff', intensity: 0 }
      },
      animation: { enabled: true, type: 'fadeIn' as const, duration: 1000, delay: 0, easing: 'ease' as const, loop: false },
      timing: { startTime: 0, duration: 5 },
      position: { x: 50, y: 20, alignment: 'center' as const },
      responsive: { mobile: {}, tablet: {} }
    }
  },
  {
    id: 'neon_glow',
    name: 'Neon Glow',
    description: 'Cyberpunk-inspired glowing text',
    thumbnail: '⚡',
    popular: true,
    tags: ['neon', 'glow', 'cyberpunk'],
    config: {
      typography: {
        fontFamily: 'Roboto Mono, monospace',
        fontSize: 36,
        fontWeight: 400,
        fontStyle: 'normal' as const,
        letterSpacing: 2,
        lineHeight: 1.2,
        textDecoration: 'none' as const
      },
      colors: { text: '#00ffff', background: 'transparent', stroke: '#000000' },
      effects: {
        shadow: { enabled: false, offsetX: 0, offsetY: 0, blur: 0, color: 'rgba(0,0,0,0.5)' },
        gradient: { enabled: false, type: 'linear' as const, colors: [], direction: 0 },
        glow: { enabled: true, color: '#00ffff', intensity: 20 },
        stroke: { enabled: true, width: 1 }
      },
      animation: { enabled: false, type: 'fadeIn' as const, duration: 1000, delay: 0, easing: 'ease' as const, loop: false },
      timing: { startTime: 0, duration: 5 },
      position: { x: 50, y: 50, alignment: 'center' as const },
      responsive: { mobile: {}, tablet: {} }
    }
  },
  {
    id: 'gradient_hero',
    name: 'Gradient Hero',
    description: 'Eye-catching gradient text for hero sections',
    thumbnail: '🌈',
    popular: false,
    tags: ['gradient', 'hero', 'colorful'],
    config: {
      typography: { 
        fontFamily: 'Inter, sans-serif',
        fontSize: 56, 
        fontWeight: 800,
        fontStyle: 'normal' as const,
        letterSpacing: 0,
        lineHeight: 1.2,
        textDecoration: 'none' as const
      },
      colors: { text: '#ffffff', background: 'transparent', stroke: '#000000' },
      effects: {
        shadow: { enabled: false, offsetX: 0, offsetY: 0, blur: 0, color: 'rgba(0,0,0,0.5)' },
        gradient: {
          enabled: true,
          type: 'linear' as const,
          colors: ['#667eea', '#764ba2'],
          direction: 45
        },
        stroke: { enabled: false, width: 0 },
        glow: { enabled: false, color: '#ffffff', intensity: 0 }
      },
      animation: { enabled: false, type: 'fadeIn' as const, duration: 1000, delay: 0, easing: 'ease' as const, loop: false },
      timing: { startTime: 0, duration: 5 },
      position: { x: 50, y: 50, alignment: 'center' as const },
      responsive: { mobile: {}, tablet: {} }
    }
  },
  {
    id: 'vintage_badge',
    name: 'Vintage Badge',
    description: 'Retro-style text with decorative elements',
    thumbnail: '🏆',
    popular: false,
    tags: ['vintage', 'retro', 'badge'],
    config: {
      typography: {
        fontFamily: 'Playfair Display, serif',
        fontSize: 32,
        fontWeight: 600,
        fontStyle: 'normal' as const,
        letterSpacing: 0,
        lineHeight: 1.2,
        textDecoration: 'none' as const
      },
      colors: { text: '#8b4513', background: '#f5deb3', stroke: '#000000' },
      effects: {
        shadow: { enabled: false, offsetX: 0, offsetY: 0, blur: 0, color: 'rgba(0,0,0,0.5)' },
        gradient: { enabled: false, type: 'linear' as const, colors: [], direction: 0 },
        stroke: { enabled: true, width: 2 },
        glow: { enabled: false, color: '#ffffff', intensity: 0 }
      },
      animation: { enabled: false, type: 'fadeIn' as const, duration: 1000, delay: 0, easing: 'ease' as const, loop: false },
      timing: { startTime: 0, duration: 5 },
      position: { x: 50, y: 50, alignment: 'center' as const },
      responsive: { mobile: {}, tablet: {} }
    }
  },
  {
    id: 'minimal_subtitle',
    name: 'Minimal Subtitle',
    description: 'Clean subtitle with perfect typography',
    thumbnail: '📝',
    popular: true,
    tags: ['minimal', 'subtitle', 'clean'],
    config: {
      typography: {
        fontFamily: 'Source Sans Pro, sans-serif',
        fontSize: 24,
        fontWeight: 300,
        fontStyle: 'normal' as const,
        letterSpacing: 0.5,
        lineHeight: 1.2,
        textDecoration: 'none' as const
      },
      colors: { text: '#666666', background: 'transparent', stroke: '#000000' },
      effects: {
        shadow: { enabled: false, offsetX: 0, offsetY: 0, blur: 0, color: 'rgba(0,0,0,0.5)' },
        gradient: { enabled: false, type: 'linear' as const, colors: [], direction: 0 },
        stroke: { enabled: false, width: 0 },
        glow: { enabled: false, color: '#ffffff', intensity: 0 }
      },
      animation: { enabled: false, type: 'fadeIn' as const, duration: 1000, delay: 0, easing: 'ease' as const, loop: false },
      timing: { startTime: 0, duration: 5 },
      position: { x: 50, y: 50, alignment: 'center' as const },
      responsive: { mobile: {}, tablet: {} }
    }
  },
  {
    id: 'social_caption',
    name: 'Social Caption',
    description: 'Perfect for Instagram and TikTok',
    thumbnail: '📱',
    popular: true,
    tags: ['social', 'instagram', 'tiktok'],
    config: {
      typography: {
        fontFamily: 'Poppins, sans-serif',
        fontSize: 28,
        fontWeight: 600,
        fontStyle: 'normal' as const,
        letterSpacing: 0,
        lineHeight: 1.2,
        textDecoration: 'none' as const
      },
      colors: { text: '#ffffff', background: 'rgba(0,0,0,0.7)', stroke: '#000000' },
      effects: {
        shadow: { enabled: false, offsetX: 0, offsetY: 0, blur: 0, color: 'rgba(0,0,0,0.5)' },
        gradient: { enabled: false, type: 'linear' as const, colors: [], direction: 0 },
        stroke: { enabled: false, width: 0 },
        glow: { enabled: false, color: '#ffffff', intensity: 0 }
      },
      animation: { enabled: false, type: 'fadeIn' as const, duration: 1000, delay: 0, easing: 'ease' as const, loop: false },
      timing: { startTime: 0, duration: 5 },
      position: { x: 50, y: 80, alignment: 'center' as const }
    }
  }
];

const FONT_FAMILIES = [
  'Inter, sans-serif',
  'Roboto, sans-serif',
  'Poppins, sans-serif',
  'Montserrat, sans-serif',
  'Source Sans Pro, sans-serif',
  'Playfair Display, serif',
  'Merriweather, serif',
  'Roboto Mono, monospace',
  'JetBrains Mono, monospace'
];

const ANIMATION_TYPES = [
  { value: 'fadeIn', label: 'Fade In' },
  { value: 'slideIn', label: 'Slide In' },
  { value: 'bounce', label: 'Bounce' },
  { value: 'typewriter', label: 'Typewriter' },
  { value: 'pulse', label: 'Pulse' },
  { value: 'rotate', label: 'Rotate' }
];

const COLOR_PALETTES = [
  { name: 'Classic', colors: ['#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff'] },
  { name: 'Modern', colors: ['#2563eb', '#7c3aed', '#dc2626', '#059669', '#ea580c'] },
  { name: 'Pastel', colors: ['#fbbf24', '#34d399', '#60a5fa', '#a78bfa', '#fb7185'] },
  { name: 'Neon', colors: ['#00ffff', '#ff00ff', '#ffff00', '#00ff00', '#ff6600'] }
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} style={{ width: '100%' }}>
    {value === index && <Box sx={{ py: 2 }}>{children}</Box>}
  </div>
);

const TextOverlayEditor: React.FC = () => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  // Default configuration
  const [config, setConfig] = useState<TextOverlayConfig>({
    text: 'Your amazing text here',
    position: { x: 50, y: 50, alignment: 'center' },
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: 32,
      fontWeight: 400,
      fontStyle: 'normal',
      letterSpacing: 0,
      lineHeight: 1.4,
      textDecoration: 'none'
    },
    colors: {
      text: '#ffffff',
      background: 'rgba(0,0,0,0.5)',
      stroke: '#000000'
    },
    effects: {
      shadow: {
        enabled: false,
        offsetX: 2,
        offsetY: 2,
        blur: 4,
        color: 'rgba(0,0,0,0.5)'
      },
      gradient: {
        enabled: false,
        type: 'linear',
        colors: ['#667eea', '#764ba2'],
        direction: 45
      },
      stroke: {
        enabled: false,
        width: 2
      },
      glow: {
        enabled: false,
        color: '#ffffff',
        intensity: 10
      }
    },
    animation: {
      enabled: false,
      type: 'fadeIn',
      duration: 1000,
      delay: 0,
      easing: 'ease',
      loop: false
    },
    timing: {
      startTime: 0,
      duration: 5
    },
    responsive: {
      mobile: {},
      tablet: {}
    }
  });

  // Computed styles for preview
  const previewStyles = useMemo(() => {
    const baseStyle: React.CSSProperties = {
      fontFamily: config.typography.fontFamily,
      fontSize: `${config.typography.fontSize}px`,
      fontWeight: config.typography.fontWeight,
      fontStyle: config.typography.fontStyle,
      letterSpacing: `${config.typography.letterSpacing}px`,
      lineHeight: config.typography.lineHeight,
      textDecoration: config.typography.textDecoration,
      color: config.colors.text,
      backgroundColor: config.colors.background,
      position: 'absolute',
      left: `${config.position.x}%`,
      top: `${config.position.y}%`,
      transform: 'translate(-50%, -50%)',
      textAlign: config.position.alignment,
      padding: '8px 16px',
      borderRadius: '8px',
      whiteSpace: 'nowrap',
      maxWidth: '80%',
      wordBreak: 'break-word'
    };

    // Add effects
    if (config.effects.shadow.enabled) {
      baseStyle.textShadow = `${config.effects.shadow.offsetX}px ${config.effects.shadow.offsetY}px ${config.effects.shadow.blur}px ${config.effects.shadow.color}`;
    }

    if (config.effects.stroke.enabled) {
      baseStyle.WebkitTextStroke = `${config.effects.stroke.width}px ${config.colors.stroke}`;
    }

    if (config.effects.glow.enabled) {
      baseStyle.textShadow = `0 0 ${config.effects.glow.intensity}px ${config.effects.glow.color}`;
    }

    if (config.effects.gradient.enabled) {
      const gradient = config.effects.gradient.type === 'linear'
        ? `linear-gradient(${config.effects.gradient.direction}deg, ${config.effects.gradient.colors.join(', ')})`
        : `radial-gradient(${config.effects.gradient.colors.join(', ')})`;
      
      baseStyle.background = gradient;
      baseStyle.WebkitBackgroundClip = 'text';
      baseStyle.WebkitTextFillColor = 'transparent';
      baseStyle.backgroundClip = 'text';
    }

    return baseStyle;
  }, [config]);

  // Update config helper
  const updateConfig = useCallback(<K extends keyof TextOverlayConfig>(
    key: K,
    // eslint-disable-next-line no-unused-vars
    value: TextOverlayConfig[K] | ((prev: TextOverlayConfig[K]) => TextOverlayConfig[K])
  ) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      [key]: typeof value === 'function' ? value(prevConfig[key]) : value
    }));
  }, []);

  // Apply preset
  const applyPreset = useCallback((preset: PresetTemplate) => {
    setConfig(prev => ({
      ...prev,
      ...preset.config,
      text: prev.text // Keep current text
    }));
    setShowTemplates(false);
  }, []);

  // Position grid component
  const PositionGrid: React.FC = () => (
    <Box sx={{ width: 200, height: 120, position: 'relative', border: 2, borderColor: 'primary.main', borderRadius: 1 }}>
      {/* Grid overlay */}
      <svg width="100%" height="100%" style={{ position: 'absolute', top: 0, left: 0, opacity: 0.3 }}>
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke={theme.palette.divider} strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
      
      {/* Position indicator */}
      <Box
        sx={{
          position: 'absolute',
          left: `${config.position.x}%`,
          top: `${config.position.y}%`,
          transform: 'translate(-50%, -50%)',
          width: 12,
          height: 12,
          bgcolor: 'primary.main',
          borderRadius: '50%',
          border: 2,
          borderColor: 'background.paper',
          cursor: 'pointer',
          '&:hover': { bgcolor: 'primary.dark' }
        }}
        onClick={(e) => {
          const rect = e.currentTarget.parentElement!.getBoundingClientRect();
          const x = ((e.clientX - rect.left) / rect.width) * 100;
          const y = ((e.clientY - rect.top) / rect.height) * 100;
          updateConfig('position', { ...config.position, x, y });
        }}
      />
      
      {/* Quick position buttons */}
      {[
        { x: 10, y: 10, label: 'TL' },
        { x: 50, y: 10, label: 'TC' },
        { x: 90, y: 10, label: 'TR' },
        { x: 10, y: 50, label: 'ML' },
        { x: 50, y: 50, label: 'MC' },
        { x: 90, y: 50, label: 'MR' },
        { x: 10, y: 90, label: 'BL' },
        { x: 50, y: 90, label: 'BC' },
        { x: 90, y: 90, label: 'BR' }
      ].map(pos => (
        <Tooltip key={pos.label} title={pos.label}>
          <IconButton
            size="small"
            sx={{
              position: 'absolute',
              left: `${pos.x}%`,
              top: `${pos.y}%`,
              transform: 'translate(-50%, -50%)',
              width: 16,
              height: 16,
              minWidth: 16,
              bgcolor: config.position.x === pos.x && config.position.y === pos.y ? 'primary.main' : 'background.paper',
              color: config.position.x === pos.x && config.position.y === pos.y ? 'primary.contrastText' : 'text.primary',
              fontSize: 10,
              '&:hover': { bgcolor: 'primary.light' }
            }}
            onClick={() => updateConfig('position', { ...config.position, x: pos.x, y: pos.y })}
          >
            {pos.label}
          </IconButton>
        </Tooltip>
      ))}
    </Box>
  );

  // Color picker component
  // eslint-disable-next-line no-unused-vars
  const ColorPicker: React.FC<{ label: string; value: string; onChange: (newColor: string) => void }> = ({ 
    label, 
    value, 
    onChange 
  }) => (
    <Box>
      <Typography variant="caption" sx={{ mb: 1, display: 'block' }}>{label}</Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          style={{
            width: 40,
            height: 40,
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer'
          }}
        />
        <TextField
          size="small"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="#000000"
          sx={{ flexGrow: 1 }}
        />
      </Box>
      {/* Quick color palette */}
      <Stack direction="row" spacing={0.5} flexWrap="wrap">
        {COLOR_PALETTES[0].colors.map(color => (
          <Box
            key={color}
            sx={{
              width: 20,
              height: 20,
              bgcolor: color,
              borderRadius: 0.5,
              cursor: 'pointer',
              border: value === color ? 2 : 1,
              borderColor: value === color ? 'primary.main' : 'divider',
              '&:hover': { transform: 'scale(1.1)' }
            }}
            onClick={() => onChange(color)}
          />
        ))}
      </Stack>
    </Box>
  );

  return (
    <Box sx={{ height: '100vh', display: 'flex' }}>
      {/* Left Panel - Controls */}
      <Paper 
        elevation={3}
        sx={{ 
          width: 400, 
          height: '100%', 
          overflow: 'auto',
          borderRadius: 0,
          borderRight: 1,
          borderColor: 'divider'
        }}
      >
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6" fontWeight="bold">
              Text Overlay Editor
            </Typography>
            <Stack direction="row" spacing={1}>
              <Tooltip title="Templates">
                <IconButton onClick={() => setShowTemplates(true)}>
                  <Star />
                </IconButton>
              </Tooltip>
              <Tooltip title="Preview Mode">
                <IconButton onClick={() => setIsPreviewMode(!isPreviewMode)}>
                  {isPreviewMode ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </Tooltip>
            </Stack>
          </Stack>
        </Box>

        {/* Main text input */}
        <Box sx={{ p: 2 }}>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Text Content"
            value={config.text}
            onChange={(e) => updateConfig('text', e.target.value)}
            variant="outlined"
            placeholder="Enter your text here..."
            sx={{ mb: 2 }}
          />
        </Box>

        {/* Tabs */}
        <Tabs 
          value={activeTab} 
          onChange={(_, newValue) => setActiveTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ px: 1, borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab icon={<LocationOn />} label="Position" />
          <Tab icon={<TextFields />} label="Typography" />
          <Tab icon={<Palette />} label="Colors" />
          <Tab icon={<AutoAwesome />} label="Effects" />
          <Tab icon={<Animation />} label="Animation" />
          <Tab icon={<Timeline />} label="Timing" />
        </Tabs>

        {/* Tab Panels */}
        <Box sx={{ p: 2 }}>
          {/* Position Tab */}
          <TabPanel value={activeTab} index={0}>
            <Stack spacing={3}>
              <Box>
                <Typography variant="subtitle2" gutterBottom>Visual Position</Typography>
                <PositionGrid />
              </Box>
              
              <Box>
                <Typography variant="subtitle2" gutterBottom>Precise Position</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="caption">X Position (%)</Typography>
                    <Slider
                      value={config.position.x}
                      onChange={(_, value) => updateConfig('position', { ...config.position, x: value as number })}
                      min={0}
                      max={100}
                      valueLabelDisplay="auto"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="caption">Y Position (%)</Typography>
                    <Slider
                      value={config.position.y}
                      onChange={(_, value) => updateConfig('position', { ...config.position, y: value as number })}
                      min={0}
                      max={100}
                      valueLabelDisplay="auto"
                    />
                  </Grid>
                </Grid>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>Text Alignment</Typography>
                <ToggleButtonGroup
                  value={config.position.alignment}
                  exclusive
                  onChange={(_, value) => value && updateConfig('position', { ...config.position, alignment: value })}
                  size="small"
                  fullWidth
                >
                  <ToggleButton value="left"><FormatAlignLeft /></ToggleButton>
                  <ToggleButton value="center"><FormatAlignCenter /></ToggleButton>
                  <ToggleButton value="right"><FormatAlignRight /></ToggleButton>
                </ToggleButtonGroup>
              </Box>
            </Stack>
          </TabPanel>

          {/* Typography Tab */}
          <TabPanel value={activeTab} index={1}>
            <Stack spacing={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Font Family</InputLabel>
                <Select
                  value={config.typography.fontFamily}
                  onChange={(e) => updateConfig('typography', { ...config.typography, fontFamily: e.target.value })}
                >
                  {FONT_FAMILIES.map(font => (
                    <MenuItem key={font} value={font} style={{ fontFamily: font }}>
                      {font.split(',')[0]}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Box>
                <Typography variant="subtitle2" gutterBottom>Font Size: {config.typography.fontSize}px</Typography>
                <Slider
                  value={config.typography.fontSize}
                  onChange={(_, value) => updateConfig('typography', { ...config.typography, fontSize: value as number })}
                  min={12}
                  max={200}
                  valueLabelDisplay="auto"
                />
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>Font Weight: {config.typography.fontWeight}</Typography>
                <Slider
                  value={config.typography.fontWeight}
                  onChange={(_, value) => updateConfig('typography', { ...config.typography, fontWeight: value as number })}
                  min={100}
                  max={900}
                  step={100}
                  marks={[
                    { value: 100, label: 'Thin' },
                    { value: 400, label: 'Regular' },
                    { value: 700, label: 'Bold' },
                    { value: 900, label: 'Black' }
                  ]}
                  valueLabelDisplay="auto"
                />
              </Box>

              <Stack direction="row" spacing={1}>
                <ToggleButtonGroup
                  value={[
                    config.typography.fontStyle === 'italic' && 'italic',
                    config.typography.textDecoration !== 'none' && config.typography.textDecoration
                  ].filter(Boolean)}
                  onChange={(_, values) => {
                    updateConfig('typography', {
                      ...config.typography,
                      fontStyle: values.includes('italic') ? 'italic' : 'normal',
                      textDecoration: values.find((v: string) => v !== 'italic') || 'none'
                    });
                  }}
                  size="small"
                >
                  <ToggleButton value="italic"><FormatItalic /></ToggleButton>
                  <ToggleButton value="underline"><FormatUnderlined /></ToggleButton>
                </ToggleButtonGroup>
              </Stack>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="caption">Letter Spacing</Typography>
                  <Slider
                    value={config.typography.letterSpacing}
                    onChange={(_, value) => updateConfig('typography', { ...config.typography, letterSpacing: value as number })}
                    min={-5}
                    max={10}
                    step={0.1}
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="caption">Line Height</Typography>
                  <Slider
                    value={config.typography.lineHeight}
                    onChange={(_, value) => updateConfig('typography', { ...config.typography, lineHeight: value as number })}
                    min={0.8}
                    max={3}
                    step={0.1}
                    valueLabelDisplay="auto"
                  />
                </Grid>
              </Grid>
            </Stack>
          </TabPanel>

          {/* Colors Tab */}
          <TabPanel value={activeTab} index={2}>
            <Stack spacing={3}>
              <ColorPicker
                label="Text Color"
                value={config.colors.text}
                onChange={(color) => updateConfig('colors', { ...config.colors, text: color })}
              />
              
              <ColorPicker
                label="Background Color"
                value={config.colors.background}
                onChange={(color) => updateConfig('colors', { ...config.colors, background: color })}
              />
              
              <ColorPicker
                label="Stroke Color"
                value={config.colors.stroke}
                onChange={(color) => updateConfig('colors', { ...config.colors, stroke: color })}
              />

              <Box>
                <Typography variant="subtitle2" gutterBottom>Color Palettes</Typography>
                {COLOR_PALETTES.map(palette => (
                  <Box key={palette.name} sx={{ mb: 2 }}>
                    <Typography variant="caption" sx={{ mb: 1, display: 'block' }}>
                      {palette.name}
                    </Typography>
                    <Stack direction="row" spacing={0.5}>
                      {palette.colors.map(color => (
                        <Tooltip key={color} title={color}>
                          <Box
                            sx={{
                              width: 32,
                              height: 32,
                              bgcolor: color,
                              borderRadius: 1,
                              cursor: 'pointer',
                              border: 1,
                              borderColor: 'divider',
                              '&:hover': { transform: 'scale(1.1)' }
                            }}
                            onClick={() => updateConfig('colors', { ...config.colors, text: color })}
                          />
                        </Tooltip>
                      ))}
                    </Stack>
                  </Box>
                ))}
              </Box>
            </Stack>
          </TabPanel>

          {/* Effects Tab */}
          <TabPanel value={activeTab} index={3}>
            <Stack spacing={2}>
              {/* Shadow */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.effects.shadow.enabled}
                        onChange={(e) => updateConfig('effects', {
                          ...config.effects,
                          shadow: { ...config.effects.shadow, enabled: e.target.checked }
                        })}
                      />
                    }
                    label="Drop Shadow"
                    onClick={(e) => e.stopPropagation()}
                  />
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="caption">Offset X</Typography>
                        <Slider
                          value={config.effects.shadow.offsetX}
                          onChange={(_, value) => updateConfig('effects', {
                            ...config.effects,
                            shadow: { ...config.effects.shadow, offsetX: value as number }
                          })}
                          min={-20}
                          max={20}
                          valueLabelDisplay="auto"
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption">Offset Y</Typography>
                        <Slider
                          value={config.effects.shadow.offsetY}
                          onChange={(_, value) => updateConfig('effects', {
                            ...config.effects,
                            shadow: { ...config.effects.shadow, offsetY: value as number }
                          })}
                          min={-20}
                          max={20}
                          valueLabelDisplay="auto"
                        />
                      </Grid>
                    </Grid>
                    <Box>
                      <Typography variant="caption">Blur</Typography>
                      <Slider
                        value={config.effects.shadow.blur}
                        onChange={(_, value) => updateConfig('effects', {
                          ...config.effects,
                          shadow: { ...config.effects.shadow, blur: value as number }
                        })}
                        min={0}
                        max={50}
                        valueLabelDisplay="auto"
                      />
                    </Box>
                    <ColorPicker
                      label="Shadow Color"
                      value={config.effects.shadow.color}
                      onChange={(color) => updateConfig('effects', {
                        ...config.effects,
                        shadow: { ...config.effects.shadow, color }
                      })}
                    />
                  </Stack>
                </AccordionDetails>
              </Accordion>

              {/* Gradient */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.effects.gradient.enabled}
                        onChange={(e) => updateConfig('effects', {
                          ...config.effects,
                          gradient: { ...config.effects.gradient, enabled: e.target.checked }
                        })}
                      />
                    }
                    label="Text Gradient"
                    onClick={(e) => e.stopPropagation()}
                  />
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Gradient Type</InputLabel>
                      <Select
                        value={config.effects.gradient.type}
                        onChange={(e) => updateConfig('effects', {
                          ...config.effects,
                          gradient: { ...config.effects.gradient, type: e.target.value as 'linear' | 'radial' }
                        })}
                      >
                        <MenuItem value="linear">Linear</MenuItem>
                        <MenuItem value="radial">Radial</MenuItem>
                      </Select>
                    </FormControl>
                    
                    {config.effects.gradient.type === 'linear' && (
                      <Box>
                        <Typography variant="caption">Direction (degrees)</Typography>
                        <Slider
                          value={config.effects.gradient.direction}
                          onChange={(_, value) => updateConfig('effects', {
                            ...config.effects,
                            gradient: { ...config.effects.gradient, direction: value as number }
                          })}
                          min={0}
                          max={360}
                          valueLabelDisplay="auto"
                        />
                      </Box>
                    )}
                    
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>Gradient Colors</Typography>
                      {config.effects.gradient.colors.map((color, index) => (
                        <Box key={index} sx={{ mb: 1 }}>
                          <ColorPicker
                            label={`Color ${index + 1}`}
                            value={color}
                            onChange={(newColor) => {
                              const newColors = [...config.effects.gradient.colors];
                              newColors[index] = newColor;
                              updateConfig('effects', {
                                ...config.effects,
                                gradient: { ...config.effects.gradient, colors: newColors }
                              });
                            }}
                          />
                        </Box>
                      ))}
                      <Button
                        size="small"
                        onClick={() => updateConfig('effects', {
                          ...config.effects,
                          gradient: {
                            ...config.effects.gradient,
                            colors: [...config.effects.gradient.colors, '#000000']
                          }
                        })}
                      >
                        Add Color
                      </Button>
                    </Box>
                  </Stack>
                </AccordionDetails>
              </Accordion>

              {/* Stroke */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.effects.stroke.enabled}
                        onChange={(e) => updateConfig('effects', {
                          ...config.effects,
                          stroke: { ...config.effects.stroke, enabled: e.target.checked }
                        })}
                      />
                    }
                    label="Text Stroke"
                    onClick={(e) => e.stopPropagation()}
                  />
                </AccordionSummary>
                <AccordionDetails>
                  <Box>
                    <Typography variant="caption">Stroke Width</Typography>
                    <Slider
                      value={config.effects.stroke.width}
                      onChange={(_, value) => updateConfig('effects', {
                        ...config.effects,
                        stroke: { ...config.effects.stroke, width: value as number }
                      })}
                      min={0}
                      max={10}
                      valueLabelDisplay="auto"
                    />
                  </Box>
                </AccordionDetails>
              </Accordion>

              {/* Glow */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.effects.glow.enabled}
                        onChange={(e) => updateConfig('effects', {
                          ...config.effects,
                          glow: { ...config.effects.glow, enabled: e.target.checked }
                        })}
                      />
                    }
                    label="Text Glow"
                    onClick={(e) => e.stopPropagation()}
                  />
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="caption">Glow Intensity</Typography>
                      <Slider
                        value={config.effects.glow.intensity}
                        onChange={(_, value) => updateConfig('effects', {
                          ...config.effects,
                          glow: { ...config.effects.glow, intensity: value as number }
                        })}
                        min={0}
                        max={50}
                        valueLabelDisplay="auto"
                      />
                    </Box>
                    <ColorPicker
                      label="Glow Color"
                      value={config.effects.glow.color}
                      onChange={(color) => updateConfig('effects', {
                        ...config.effects,
                        glow: { ...config.effects.glow, color }
                      })}
                    />
                  </Stack>
                </AccordionDetails>
              </Accordion>
            </Stack>
          </TabPanel>

          {/* Animation Tab */}
          <TabPanel value={activeTab} index={4}>
            <Stack spacing={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={config.animation.enabled}
                    onChange={(e) => updateConfig('animation', { ...config.animation, enabled: e.target.checked })}
                  />
                }
                label="Enable Animation"
              />

              {config.animation.enabled && (
                <>
                  <FormControl fullWidth size="small">
                    <InputLabel>Animation Type</InputLabel>
                    <Select
                      value={config.animation.type}
                      onChange={(e) => updateConfig('animation', { ...config.animation, type: e.target.value as 'fadeIn' | 'slideIn' | 'bounce' | 'typewriter' | 'pulse' | 'rotate' })}
                    >
                      {ANIMATION_TYPES.map(type => (
                        <MenuItem key={type.value} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <Box>
                    <Typography variant="subtitle2" gutterBottom>Duration: {config.animation.duration}ms</Typography>
                    <Slider
                      value={config.animation.duration}
                      onChange={(_, value) => updateConfig('animation', { ...config.animation, duration: value as number })}
                      min={100}
                      max={5000}
                      step={100}
                      valueLabelDisplay="auto"
                    />
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" gutterBottom>Delay: {config.animation.delay}ms</Typography>
                    <Slider
                      value={config.animation.delay}
                      onChange={(_, value) => updateConfig('animation', { ...config.animation, delay: value as number })}
                      min={0}
                      max={3000}
                      step={100}
                      valueLabelDisplay="auto"
                    />
                  </Box>

                  <FormControl fullWidth size="small">
                    <InputLabel>Easing</InputLabel>
                    <Select
                      value={config.animation.easing}
                      onChange={(e) => updateConfig('animation', { ...config.animation, easing: e.target.value as 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' })}
                    >
                      <MenuItem value="linear">Linear</MenuItem>
                      <MenuItem value="ease">Ease</MenuItem>
                      <MenuItem value="ease-in">Ease In</MenuItem>
                      <MenuItem value="ease-out">Ease Out</MenuItem>
                      <MenuItem value="ease-in-out">Ease In Out</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.animation.loop}
                        onChange={(e) => updateConfig('animation', { ...config.animation, loop: e.target.checked })}
                      />
                    }
                    label="Loop Animation"
                  />

                  <Box>
                    <Button
                      variant="contained"
                      startIcon={isPlaying ? <Pause /> : <PlayArrow />}
                      onClick={() => setIsPlaying(!isPlaying)}
                      sx={{ mr: 1 }}
                    >
                      {isPlaying ? 'Pause' : 'Preview'}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Refresh />}
                      onClick={() => setIsPlaying(false)}
                    >
                      Reset
                    </Button>
                  </Box>
                </>
              )}
            </Stack>
          </TabPanel>

          {/* Timing Tab */}
          <TabPanel value={activeTab} index={5}>
            <Stack spacing={3}>
              <Box>
                <Typography variant="subtitle2" gutterBottom>Start Time: {config.timing.startTime}s</Typography>
                <Slider
                  value={config.timing.startTime}
                  onChange={(_, value) => updateConfig('timing', { ...config.timing, startTime: value as number })}
                  min={0}
                  max={60}
                  step={0.1}
                  valueLabelDisplay="auto"
                />
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>Duration: {config.timing.duration}s</Typography>
                <Slider
                  value={config.timing.duration}
                  onChange={(_, value) => updateConfig('timing', { ...config.timing, duration: value as number })}
                  min={0.1}
                  max={60}
                  step={0.1}
                  valueLabelDisplay="auto"
                />
              </Box>

              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>Timeline</Typography>
                <Box sx={{ height: 40, position: 'relative', bgcolor: 'grey.100', borderRadius: 1 }}>
                  {/* Timeline bar */}
                  <Box
                    sx={{
                      position: 'absolute',
                      left: `${(config.timing.startTime / 60) * 100}%`,
                      width: `${(config.timing.duration / 60) * 100}%`,
                      height: '100%',
                      bgcolor: 'primary.main',
                      borderRadius: 1,
                      minWidth: '2px'
                    }}
                  />
                  {/* Time markers */}
                  {[0, 10, 20, 30, 40, 50, 60].map(time => (
                    <Box
                      key={time}
                      sx={{
                        position: 'absolute',
                        left: `${(time / 60) * 100}%`,
                        top: 0,
                        bottom: 0,
                        width: 1,
                        bgcolor: 'divider'
                      }}
                    />
                  ))}
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, fontSize: 12 }}>
                  <span>0s</span>
                  <span>30s</span>
                  <span>60s</span>
                </Box>
              </Paper>
            </Stack>
          </TabPanel>
        </Box>

        {/* Action Buttons */}
        <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
          <Stack direction="row" spacing={1} justifyContent="space-between">
            <Button variant="outlined" startIcon={<Upload />} size="small">
              Import
            </Button>
            <Button variant="outlined" startIcon={<Download />} size="small">
              Export
            </Button>
            <Button variant="contained" startIcon={<Save />} size="small">
              Apply
            </Button>
          </Stack>
        </Box>
      </Paper>

      {/* Right Panel - Preview */}
      <Box sx={{ flexGrow: 1, position: 'relative', bgcolor: 'grey.900' }}>
        {/* Preview Header */}
        <Paper 
          elevation={1}
          sx={{ 
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 10,
            p: 1
          }}
        >
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="caption" color="text.secondary">
              Preview (1920×1080)
            </Typography>
            <Divider orientation="vertical" flexItem />
            <ButtonGroup size="small">
              <Button>16:9</Button>
              <Button>9:16</Button>
              <Button>1:1</Button>
            </ButtonGroup>
          </Stack>
        </Paper>

        {/* Video Preview Area */}
        <Box 
          sx={{ 
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '80%',
            aspectRatio: '16/9',
            bgcolor: 'grey.800',
            borderRadius: 2,
            border: 2,
            borderColor: 'grey.700',
            overflow: 'hidden'
          }}
        >
          {/* Background pattern */}
          <Box 
            sx={{ 
              width: '100%',
              height: '100%',
              backgroundImage: `
                radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2%, transparent 0%),
                radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 2%, transparent 0%)
              `,
              backgroundSize: '100px 100px'
            }}
          />
          
          {/* Text Overlay Preview */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Box
              component="div"
              sx={{
                ...previewStyles,
                animation: config.animation.enabled && isPlaying ? 
                  `${config.animation.type} ${config.animation.duration}ms ${config.animation.easing} ${config.animation.delay}ms ${config.animation.loop ? 'infinite' : '1'}` 
                  : 'none'
              }}
            >
              {config.text}
            </Box>
          </Box>
        </Box>

        {/* Quick Actions FAB */}
        <SpeedDial
          ariaLabel="Quick Actions"
          sx={{ position: 'absolute', bottom: 16, right: 16 }}
          icon={<SpeedDialIcon />}
        >
          <SpeedDialAction
            icon={<Star />}
            tooltipTitle="Templates"
            onClick={() => setShowTemplates(true)}
          />
          <SpeedDialAction
            icon={<Refresh />}
            tooltipTitle="Reset"
            onClick={() => {
              // Reset to defaults
              setConfig(prev => ({
                ...prev,
                position: { x: 50, y: 50, alignment: 'center' },
                typography: {
                  fontFamily: 'Inter, sans-serif',
                  fontSize: 32,
                  fontWeight: 400,
                  fontStyle: 'normal',
                  letterSpacing: 0,
                  lineHeight: 1.4,
                  textDecoration: 'none'
                }
              }));
            }}
          />
          <SpeedDialAction
            icon={<Save />}
            tooltipTitle="Save Preset"
          />
        </SpeedDial>
      </Box>

      {/* Template Gallery Dialog */}
      <Dialog 
        open={showTemplates} 
        onClose={() => setShowTemplates(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">Template Gallery</Typography>
            <Stack direction="row" spacing={1}>
              <Chip icon={<Star />} label="Popular" size="small" />
              <Chip label="New" size="small" color="primary" />
            </Stack>
          </Stack>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            {PRESET_TEMPLATES.map(preset => (
              <Grid item xs={12} sm={6} md={4} key={preset.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'translateY(-2px)' },
                    transition: 'transform 0.2s ease'
                  }}
                  onClick={() => applyPreset(preset)}
                >
                  <CardContent>
                    <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 1 }}>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {preset.thumbnail}
                      </Avatar>
                      <Box sx={{ flexGrow: 1 }}>
                        <Stack direction="row" alignItems="center" justifyContent="space-between">
                          <Typography variant="subtitle2" fontWeight="bold">
                            {preset.name}
                          </Typography>
                          {preset.popular && <StarOutline fontSize="small" color="warning" />}
                        </Stack>
                        <Typography variant="caption" color="text.secondary">
                          {preset.description}
                        </Typography>
                      </Box>
                    </Stack>
                    
                    <Stack direction="row" spacing={0.5} flexWrap="wrap" sx={{ mb: 2 }}>
                      {preset.tags.map(tag => (
                        <Chip key={tag} label={tag} size="small" variant="outlined" />
                      ))}
                    </Stack>

                    {/* Preview */}
                    <Paper 
                      variant="outlined" 
                      sx={{ 
                        height: 80, 
                        display: 'flex', 
                        alignItems: 'center', 
                        justifyContent: 'center',
                        bgcolor: 'grey.900',
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: Math.min(preset.config.typography?.fontSize || 24, 20),
                          fontWeight: preset.config.typography?.fontWeight || 400,
                          color: preset.config.colors?.text || '#fff',
                          textAlign: 'center'
                        }}
                      >
                        Sample Text
                      </Typography>
                    </Paper>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTemplates(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Animation Keyframes */}
      <style>
        {`
          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }
          @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
          }
          @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0, -20px, 0); }
            70% { transform: translate3d(0, -10px, 0); }
            90% { transform: translate3d(0, -4px, 0); }
          }
          @keyframes typewriter {
            from { width: 0; }
            to { width: 100%; }
          }
          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
          }
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
    </Box>
  );
};

export default TextOverlayEditor;