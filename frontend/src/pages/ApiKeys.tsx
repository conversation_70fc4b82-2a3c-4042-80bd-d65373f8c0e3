import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Pagination,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Card,
  CardContent,
  Grid,
  alpha,
  useTheme,
  Snackbar,
} from '@mui/material';
import {
  VpnKey as ApiKeyIcon,
  Add as AddIcon,
  ContentCopy as CopyIcon,
  Visibility as ViewIcon,
  VisibilityOff,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Today as TodayIcon,
  TrendingUp as UsageIcon
} from '@mui/icons-material';

interface ApiKey {
  id: string;
  name: string;
  key: string;
  userId: string;
  userEmail: string;
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
  expiresAt?: string;
  usageCount: number;
  rateLimit: number;
  permissions: string[];
}

interface User {
  id: string;
  email: string;
  role: string;
  fullName?: string;
}

interface BackendUser {
  id: string;
  email: string;
  role: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
  projects_count: number;
  api_keys_count: number;
}

interface BackendApiKey {
  id: string;
  name: string;
  key: string;
  user_id: string;
  user_email: string;
  is_active: boolean;
  created_at: string;
  last_used?: string;
  expires_at?: string;
  usage_count: number;
  rate_limit: number;
  permissions: string[];
}

interface ApiKeyFormData {
  name: string;
  user_id: string;
  rate_limit: number;
  expires_at: string;
  permissions: string[];
}

interface ApiKeysState {
  apiKeys: ApiKey[];
  users: User[];
  loading: boolean;
  error: string | null;
  page: number;
  totalPages: number;
  searchQuery: string;
  selectedStatus: string;
  dialogOpen: boolean;
  editingKey: ApiKey | null;
  deleteDialogOpen: boolean;
  keyToDelete: ApiKey | null;
  visibleKeys: Set<string>;
  snackbarOpen: boolean;
  snackbarMessage: string;
}

const ApiKeys: React.FC = () => {
  const theme = useTheme();
  const { apiKey: authApiKey } = useAuth();
  const [state, setState] = useState<ApiKeysState>({
    apiKeys: [],
    users: [],
    loading: false,
    error: null,
    page: 1,
    totalPages: 1,
    searchQuery: '',
    selectedStatus: 'all',
    dialogOpen: false,
    editingKey: null,
    deleteDialogOpen: false,
    keyToDelete: null,
    visibleKeys: new Set(),
    snackbarOpen: false,
    snackbarMessage: ''
  });


  const loadApiKeys = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    try {
      if (!authApiKey) {
        throw new Error('No API key found');
      }

      const response = await fetch(`/api/v1/dashboard/api-keys?page=${state.page}&limit=50&search=${state.searchQuery}&status_filter=${state.selectedStatus}`, {
        headers: {
          'X-API-Key': authApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        throw new Error(`Failed to fetch API keys (${response.status}): ${errorText.substring(0, 100)}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error('Non-JSON response received:', responseText.substring(0, 200));
        throw new Error('Server returned HTML instead of JSON - check authentication and API server status');
      }

      const data = await response.json();
      
      // Transform snake_case API response to camelCase for frontend
      const transformedApiKeys = data.map((item: BackendApiKey) => ({
        id: item.id,
        name: item.name,
        key: item.key,
        userId: item.user_id,
        userEmail: item.user_email,
        isActive: item.is_active,
        createdAt: item.created_at,
        lastUsed: item.last_used,
        expiresAt: item.expires_at,
        usageCount: item.usage_count,
        rateLimit: item.rate_limit,
        permissions: item.permissions
      }));

      setState(prev => ({
        ...prev,
        apiKeys: transformedApiKeys,
        totalPages: Math.ceil(transformedApiKeys.length / 50),
        loading: false
      }));
    } catch (error) {
      console.error('Error loading API keys:', error);
      setState(prev => ({
        ...prev,
        apiKeys: [],
        totalPages: 1,
        loading: false,
        error: 'Failed to load API keys - check connection and authentication'
      }));
    }
  }, [state.page, state.searchQuery, state.selectedStatus, authApiKey]);

  const loadUsers = useCallback(async () => {
    try {
      if (!authApiKey) {
        return;
      }

      const response = await fetch('/api/v1/dashboard/users?limit=100', {
        headers: {
          'X-API-Key': authApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error('Failed to fetch users');
        return;
      }

      const users = await response.json();
      
      // Transform snake_case API response to camelCase for frontend
      const transformedUsers = users.map((item: BackendUser) => ({
        id: item.id,
        email: item.email,
        role: item.role,
        fullName: `${item.email} (${item.role})` // Create display name
      }));

      setState(prev => ({ ...prev, users: transformedUsers }));
    } catch (error) {
      console.error('Error loading users:', error);
    }
  }, [authApiKey]);

  useEffect(() => {
    loadApiKeys();
    loadUsers();
  }, [loadApiKeys, loadUsers]);

  const handleCreateKey = () => {
    setState(prev => ({
      ...prev,
      dialogOpen: true,
      editingKey: null
    }));
  };

  const handleEditKey = (apiKey: ApiKey) => {
    setState(prev => ({
      ...prev,
      dialogOpen: true,
      editingKey: apiKey
    }));
  };

  const handleDeleteKey = (apiKey: ApiKey) => {
    setState(prev => ({
      ...prev,
      deleteDialogOpen: true,
      keyToDelete: apiKey
    }));
  };

  const confirmDelete = async () => {
    if (!state.keyToDelete) return;
    
    try {
      if (!authApiKey) {
        throw new Error('No API key found');
      }

      const response = await fetch(`/api/v1/dashboard/api-keys/${state.keyToDelete.id}`, {
        method: 'DELETE',
        headers: {
          'X-API-Key': authApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete API key');
      }

      setState(prev => ({
        ...prev,
        deleteDialogOpen: false,
        keyToDelete: null,
        snackbarOpen: true,
        snackbarMessage: 'API key deleted successfully'
      }));
      loadApiKeys();
    } catch (error) {
      console.error('Error deleting API key:', error);
      setState(prev => ({ ...prev, error: 'Failed to delete API key' }));
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    setState(prev => {
      const newVisibleKeys = new Set(prev.visibleKeys);
      if (newVisibleKeys.has(keyId)) {
        newVisibleKeys.delete(keyId);
      } else {
        newVisibleKeys.add(keyId);
      }
      return { ...prev, visibleKeys: newVisibleKeys };
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setState(prev => ({
      ...prev,
      snackbarOpen: true,
      snackbarMessage: 'API key copied to clipboard'
    }));
  };

  const maskApiKey = (key: string) => {
    return `${key.substring(0, 12)}${'*'.repeat(20)}${key.substring(key.length - 4)}`;
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const StatsCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    suffix?: string;
  }> = ({ title, value, icon, color, suffix = '' }) => (
    <Card 
      elevation={1}
      sx={{ 
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${alpha(color, 0.1)}`
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" fontWeight="bold" color={color}>
              {value.toLocaleString()}{suffix}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
          <Box 
            sx={{ 
              p: 1.5, 
              borderRadius: 2, 
              backgroundColor: alpha(color, 0.1),
              color: color
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box 
            sx={{ 
              p: 1.5, 
              borderRadius: 2, 
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            <ApiKeyIcon />
          </Box>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              API Keys Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage API keys and access permissions
            </Typography>
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Refresh API Keys">
            <IconButton onClick={loadApiKeys} disabled={state.loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateKey}
          >
            Create API Key
          </Button>
        </Box>
      </Box>

      {/* Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total API Keys"
            value={state.apiKeys.length}
            icon={<ApiKeyIcon />}
            color={theme.palette.primary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Active Keys"
            value={state.apiKeys.filter(k => k.isActive).length}
            icon={<ActiveIcon />}
            color={theme.palette.success.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Usage"
            value={state.apiKeys.reduce((sum, k) => sum + (k.usageCount || 0), 0)}
            icon={<UsageIcon />}
            color={theme.palette.info.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="This Month"
            value={state.apiKeys.filter(k => {
              if (!k.createdAt) return false;
              try {
                return new Date(k.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
              } catch {
                return false;
              }
            }).length}
            icon={<TodayIcon />}
            color={theme.palette.warning.main}
          />
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              placeholder="Search API keys by name, user, or key..."
              value={state.searchQuery}
              onChange={(e) => setState(prev => ({ ...prev, searchQuery: e.target.value }))}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={state.selectedStatus}
                label="Status"
                onChange={(e) => setState(prev => ({ ...prev, selectedStatus: e.target.value }))}
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="expired">Expired</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {state.error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {state.error}
        </Alert>
      )}

      {/* API Keys Table */}
      <TableContainer component={Paper} elevation={1}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>User</TableCell>
              <TableCell>API Key</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Usage</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Last Used</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.apiKeys.map((apiKey) => (
              <TableRow key={apiKey.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {apiKey.name}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                    {(apiKey.permissions || []).slice(0, 2).map((permission) => (
                      <Chip
                        key={permission}
                        label={permission}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    ))}
                    {(apiKey.permissions || []).length > 2 && (
                      <Chip
                        label={`+${(apiKey.permissions || []).length - 2}`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {apiKey.userEmail}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography 
                      variant="body2" 
                      fontFamily="monospace"
                      sx={{ maxWidth: 200, overflow: 'hidden' }}
                    >
                      {state.visibleKeys.has(apiKey.id) ? apiKey.key : maskApiKey(apiKey.key)}
                    </Typography>
                    <Tooltip title={state.visibleKeys.has(apiKey.id) ? 'Hide' : 'Show'}>
                      <IconButton 
                        size="small" 
                        onClick={() => toggleKeyVisibility(apiKey.id)}
                      >
                        {state.visibleKeys.has(apiKey.id) ? <VisibilityOff /> : <ViewIcon />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy to clipboard">
                      <IconButton 
                        size="small"
                        onClick={() => copyToClipboard(apiKey.key)}
                      >
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={apiKey.isActive ? <ActiveIcon /> : <InactiveIcon />}
                    label={apiKey.isActive ? 'Active' : 'Inactive'}
                    color={apiKey.isActive ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {(apiKey.usageCount || 0).toLocaleString()}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      / {apiKey.rateLimit || 100} limit
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {formatDate(apiKey.createdAt)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {apiKey.lastUsed ? formatDate(apiKey.lastUsed) : 'Never'}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Edit API Key">
                      <IconButton size="small" onClick={() => handleEditKey(apiKey)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete API Key">
                      <IconButton 
                        size="small" 
                        color="error"
                        onClick={() => handleDeleteKey(apiKey)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
        <Pagination
          count={state.totalPages}
          page={state.page}
          onChange={(_, page) => setState(prev => ({ ...prev, page }))}
          color="primary"
        />
      </Box>

      {/* API Key Dialog */}
      <CreateApiKeyDialog
        open={state.dialogOpen}
        editingKey={state.editingKey}
        users={state.users}
        onClose={() => setState(prev => ({ ...prev, dialogOpen: false }))}
        onSubmit={async (keyData) => {
          try {
            if (!authApiKey) {
              throw new Error('No API key found');
            }

            // Format the data for the backend
            const formattedData = {
              name: keyData.name,
              user_id: keyData.user_id,
              rate_limit: keyData.rate_limit,
              permissions: keyData.permissions,
              expires_at: keyData.expires_at ? keyData.expires_at : null
            };

            const url = state.editingKey 
              ? `/api/v1/dashboard/api-keys/${state.editingKey.id}`
              : '/api/v1/dashboard/api-keys';
            const method = state.editingKey ? 'PUT' : 'POST';

            const response = await fetch(url, {
              method,
              headers: {
                'X-API-Key': authApiKey,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(formattedData)
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error('API Key creation error:', response.status, errorText);
              throw new Error(`Failed to ${state.editingKey ? 'update' : 'create'} API key (${response.status}): ${errorText}`);
            }

            setState(prev => ({
              ...prev,
              dialogOpen: false,
              editingKey: null,
              snackbarOpen: true,
              snackbarMessage: `API key ${state.editingKey ? 'updated' : 'created'} successfully`
            }));
            loadApiKeys();
          } catch (error) {
            console.error(`Error ${state.editingKey ? 'updating' : 'creating'} API key:`, error);
            setState(prev => ({ ...prev, error: `Failed to ${state.editingKey ? 'update' : 'create'} API key` }));
          }
        }}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={state.deleteDialogOpen} onClose={() => setState(prev => ({ ...prev, deleteDialogOpen: false }))}>
        <DialogTitle>Delete API Key</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the API key "{state.keyToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setState(prev => ({ ...prev, deleteDialogOpen: false }))}>
            Cancel
          </Button>
          <Button color="error" variant="contained" onClick={confirmDelete}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={state.snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setState(prev => ({ ...prev, snackbarOpen: false }))}
        message={state.snackbarMessage}
      />
    </Box>
  );
};

// Create API Key Dialog Component
interface CreateApiKeyDialogProps {
  open: boolean;
  editingKey: ApiKey | null;
  users: User[];
  onClose: () => void;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
  onSubmit: (data: ApiKeyFormData) => Promise<void>;
}

const CreateApiKeyDialog: React.FC<CreateApiKeyDialogProps> = ({
  open,
  editingKey,
  users,
  onClose,
  onSubmit
}) => {
  const [formData, setFormData] = React.useState({
    name: '',
    user_id: users.length > 0 ? users[0].id : '',
    rate_limit: 100,
    expires_at: '',
    permissions: [] as string[]
  });
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    if (editingKey) {
      setFormData({
        name: editingKey.name,
        user_id: editingKey.userId,
        rate_limit: editingKey.rateLimit || 100,
        expires_at: editingKey.expiresAt || '',
        permissions: editingKey.permissions || []
      });
    } else {
      setFormData({
        name: '',
        user_id: users.length > 0 ? users[0].id : '',
        rate_limit: 100,
        expires_at: '',
        permissions: []
      });
    }
  }, [editingKey, users]);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await onSubmit(formData);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {editingKey ? 'Edit API Key' : 'Create New API Key'}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <TextField
            fullWidth
            label="Name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2 }}
            required
          />
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>User</InputLabel>
            <Select
              value={formData.user_id}
              label="User"
              onChange={(e) => setFormData(prev => ({ ...prev, user_id: e.target.value }))}
            >
              {users.map((user) => (
                <MenuItem key={user.id} value={user.id}>
                  {user.email} ({user.role})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <TextField
            fullWidth
            label="Rate Limit (requests/hour)"
            type="number"
            value={formData.rate_limit}
            onChange={(e) => setFormData(prev => ({ ...prev, rate_limit: parseInt(e.target.value) || 100 }))}
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            label="Expires At (optional)"
            type="datetime-local"
            value={formData.expires_at ? new Date(formData.expires_at).toISOString().slice(0, 16) : ''}
            onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value ? new Date(e.target.value).toISOString() : '' }))}
            sx={{ mb: 2 }}
            InputLabelProps={{ shrink: true }}
          />
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Permissions</InputLabel>
            <Select
              multiple
              value={formData.permissions}
              label="Permissions"
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                permissions: typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value
              }))}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip key={value} label={value} size="small" />
                  ))}
                </Box>
              )}
            >
              <MenuItem value="video:create">Video Create</MenuItem>
              <MenuItem value="video:read">Video Read</MenuItem>
              <MenuItem value="video:update">Video Update</MenuItem>
              <MenuItem value="video:delete">Video Delete</MenuItem>
              <MenuItem value="audio:create">Audio Create</MenuItem>
              <MenuItem value="audio:read">Audio Read</MenuItem>
              <MenuItem value="audio:update">Audio Update</MenuItem>
              <MenuItem value="audio:delete">Audio Delete</MenuItem>
              <MenuItem value="image:create">Image Create</MenuItem>
              <MenuItem value="image:read">Image Read</MenuItem>
              <MenuItem value="document:create">Document Create</MenuItem>
              <MenuItem value="document:read">Document Read</MenuItem>
              <MenuItem value="admin:all">Admin All</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          variant="contained" 
          onClick={handleSubmit}
          disabled={loading || !formData.name.trim()}
        >
          {loading ? 'Processing...' : (editingKey ? 'Update' : 'Create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ApiKeys;