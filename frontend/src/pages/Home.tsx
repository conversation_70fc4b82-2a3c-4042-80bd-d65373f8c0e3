import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  Grid,
  Fade,
  Grow,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Alert,
  Chip,
  useTheme,
  alpha,
  Tabs,
  Tab,
  Paper,
  Stack,
  IconButton,
  Tooltip,
  Avatar,
  Slide,
  Zoom,
  Collapse,
} from '@mui/material';
import {
  VideoLibrary,
  Security,
  ArrowForward,
  PlayCircle,
  AutoAwesome,
  Analytics,
  Share,
  Mic,
  PhotoLibrary,
  Code,
  RocketLaunch,
  VerifiedUser,
  Science,
  Psychology,
  Architecture,
  Api,
  Storage,
  Transform,
  Movie,
  ContentCopy,
  CheckCircle,
  Star,
  Celebration,
  ElectricBolt,
  Menu,
  KeyboardArrowDown,
} from '@mui/icons-material';

// Custom particle animation component
interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
}

const ParticleBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(0);
  const particlesRef = useRef<Particle[]>([]);
  const theme = useTheme();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const createParticles = () => {
      const colors = [theme.palette.primary.main, '#6366F1', theme.palette.secondary.main];
      particlesRef.current = Array.from({ length: 50 }, (_, i) => ({
        id: i,
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.5 + 0.1,
        color: colors[Math.floor(Math.random() * colors.length)],
      }));
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particlesRef.current.forEach(particle => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Bounce off edges
        if (particle.x <= 0 || particle.x >= canvas.width) particle.vx *= -1;
        if (particle.y <= 0 || particle.y >= canvas.height) particle.vy *= -1;

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color + Math.floor(particle.opacity * 255).toString(16).padStart(2, '0');
        ctx.fill();
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    createParticles();
    animate();

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [theme.palette.primary.main, theme.palette.secondary.main]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        pointerEvents: 'none',
      }}
    />
  );
};

// Scroll Progress Indicator
const ScrollProgress: React.FC = () => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const totalScroll = document.documentElement.scrollTop;
      const windowHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const scroll = totalScroll / windowHeight;
      setProgress(scroll * 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: `${progress}%`,
        height: 4,
        background: `linear-gradient(90deg, #6366F1 0%, #EC4899 50%, #10B981 100%)`,
        zIndex: 1001,
        transition: 'width 0.1s ease-out',
        borderRadius: '0 2px 2px 0',
        boxShadow: `0 0 10px ${alpha('#6366F1', 0.5)}`,
      }}
    />
  );
};

// Top Navigation Bar (visible at page top)
const TopNavBar: React.FC<{ onLoginClick: () => void }> = ({ onLoginClick }) => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 100);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setMobileMenuOpen(false);
    }
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        transform: scrolled ? 'translateY(-100%)' : 'translateY(0)',
        opacity: scrolled ? 0 : 1,
      }}
    >
      <Container maxWidth="xl">
        <Paper
          sx={{
            mt: 2,
            px: 4,
            py: 2,
            borderRadius: 6,
            background: `rgba(255, 255, 255, 0.95)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha('#6366F1', 0.1)}`,
            boxShadow: `0 8px 32px ${alpha('#000', 0.08)}`,
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            {/* Logo */}
            <Typography
              variant="h5"
              sx={{
                fontWeight: 800,
                background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                cursor: 'pointer',
              }}
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            >
              Ouinhi API
            </Typography>

            {/* Navigation Links */}
            <Stack direction="row" spacing={1} sx={{ display: { xs: 'none', md: 'flex' } }}>
              {[
                { label: 'Demo', id: 'demo', icon: <PlayCircle sx={{ fontSize: 18 }} /> },
                { label: 'Features', id: 'features', icon: <AutoAwesome sx={{ fontSize: 18 }} /> },
                { label: 'Technical', id: 'technical', icon: <Architecture sx={{ fontSize: 18 }} /> },
              ].map((item) => (
                <Button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  startIcon={item.icon}
                  sx={{
                    color: 'text.secondary',
                    fontWeight: 600,
                    textTransform: 'none',
                    px: 3,
                    py: 1.5,
                    borderRadius: 4,
                    fontSize: '1rem',
                    '&:hover': {
                      bgcolor: alpha('#6366F1', 0.1),
                      color: '#6366F1',
                      transform: 'translateY(-1px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  {item.label}
                </Button>
              ))}
            </Stack>

            {/* CTA Button */}
            <Button
              onClick={onLoginClick}
              variant="contained"
              startIcon={<RocketLaunch />}
              sx={{
                px: 4,
                py: 1.5,
                borderRadius: 4,
                background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
                fontWeight: 700,
                textTransform: 'none',
                fontSize: '1rem',
                boxShadow: `0 4px 16px ${alpha('#6366F1', 0.3)}`,
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: `0 8px 24px ${alpha('#6366F1', 0.4)}`,
                },
                transition: 'all 0.3s ease',
                display: { xs: 'none', sm: 'flex' },
              }}
            >
              Get Started
            </Button>

            {/* Mobile Menu Button */}
            <IconButton
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              sx={{ 
                display: { xs: 'flex', md: 'none' },
                color: '#6366F1',
                p: 1.5,
              }}
            >
              <Menu />
            </IconButton>
          </Stack>
        </Paper>

        {/* Mobile Menu Dropdown */}
        <Collapse in={mobileMenuOpen}>
          <Paper
            sx={{
              mt: 1,
              p: 3,
              borderRadius: 6,
              background: `rgba(255, 255, 255, 0.98)`,
              backdropFilter: 'blur(20px)',
              border: `1px solid ${alpha('#6366F1', 0.1)}`,
              boxShadow: `0 8px 32px ${alpha('#000', 0.12)}`,
            }}
          >
            <Stack spacing={2}>
              {[
                { label: 'Demo', id: 'demo', icon: <PlayCircle sx={{ fontSize: 18 }} /> },
                { label: 'Features', id: 'features', icon: <AutoAwesome sx={{ fontSize: 18 }} /> },
                { label: 'Technical', id: 'technical', icon: <Architecture sx={{ fontSize: 18 }} /> },
              ].map((item) => (
                <Button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  startIcon={item.icon}
                  fullWidth
                  sx={{
                    justifyContent: 'flex-start',
                    color: 'text.primary',
                    fontWeight: 600,
                    textTransform: 'none',
                    py: 2,
                    px: 3,
                    borderRadius: 4,
                    fontSize: '1rem',
                    '&:hover': {
                      bgcolor: alpha('#6366F1', 0.1),
                      color: '#6366F1',
                      transform: 'translateX(4px)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  {item.label}
                </Button>
              ))}
              
              {/* Mobile CTA Button */}
              <Button
                onClick={() => {
                  onLoginClick();
                  setMobileMenuOpen(false);
                }}
                variant="contained"
                startIcon={<RocketLaunch />}
                fullWidth
                sx={{
                  mt: 2,
                  py: 2,
                  borderRadius: 4,
                  background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
                  fontWeight: 700,
                  textTransform: 'none',
                  fontSize: '1rem',
                  boxShadow: `0 4px 16px ${alpha('#6366F1', 0.3)}`,
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: `0 8px 24px ${alpha('#6366F1', 0.4)}`,
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Get Started
              </Button>
            </Stack>
          </Paper>
        </Collapse>
      </Container>
    </Box>
  );
};

// Enhanced Floating Navigation Component (appears on scroll)
const FloatingNav: React.FC<{ onLoginClick: () => void }> = ({ onLoginClick }) => {
  const [scrolled, setScrolled] = useState(false);
  const [navOpen, setNavOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 100);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setNavOpen(false);
    }
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 20,
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 1000,
        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        opacity: scrolled ? 1 : 0,
        visibility: scrolled ? 'visible' : 'hidden',
      }}
    >
      <Paper
        sx={{
          px: 4,
          py: 2,
          borderRadius: 8,
          background: `rgba(255, 255, 255, 0.95)`,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha('#6366F1', 0.2)}`,
          boxShadow: `0 20px 40px ${alpha('#000', 0.1)}`,
          display: 'flex',
          alignItems: 'center',
          gap: 3,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 800,
            background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mr: 2,
          }}
        >
          Ouinhi
        </Typography>
        
        <Stack direction="row" spacing={2} sx={{ display: { xs: 'none', md: 'flex' } }}>
          {[
            { label: 'Demo', id: 'demo' },
            { label: 'Features', id: 'features' },
            { label: 'Tech', id: 'technical' },
          ].map((item) => (
            <Button
              key={item.id}
              onClick={() => scrollToSection(item.id)}
              sx={{
                color: 'text.secondary',
                fontWeight: 600,
                textTransform: 'none',
                px: 3,
                py: 1,
                borderRadius: 6,
                '&:hover': {
                  bgcolor: alpha('#6366F1', 0.1),
                  color: '#6366F1',
                },
                transition: 'all 0.3s ease',
              }}
            >
              {item.label}
            </Button>
          ))}
        </Stack>

        <Button
          onClick={onLoginClick}
          variant="contained"
          size="small"
          sx={{
            px: 4,
            py: 1.5,
            borderRadius: 6,
            background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
            fontWeight: 700,
            textTransform: 'none',
            boxShadow: `0 8px 20px ${alpha('#6366F1', 0.3)}`,
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: `0 12px 30px ${alpha('#6366F1', 0.4)}`,
            },
            transition: 'all 0.3s ease',
          }}
        >
          Get Started
        </Button>

        <IconButton
          onClick={() => setNavOpen(!navOpen)}
          sx={{ 
            display: { xs: 'flex', md: 'none' },
            color: '#6366F1',
          }}
        >
          <Menu />
        </IconButton>
      </Paper>

      {/* Mobile Menu */}
      <Collapse in={navOpen}>
        <Paper
          sx={{
            mt: 2,
            p: 3,
            borderRadius: 6,
            background: `rgba(255, 255, 255, 0.98)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha('#6366F1', 0.2)}`,
            boxShadow: `0 20px 40px ${alpha('#000', 0.15)}`,
          }}
        >
          <Stack spacing={2}>
            {[
              { label: 'Demo', id: 'demo' },
              { label: 'Features', id: 'features' },
              { label: 'Technical', id: 'technical' },
            ].map((item) => (
              <Button
                key={item.id}
                onClick={() => scrollToSection(item.id)}
                fullWidth
                sx={{
                  justifyContent: 'flex-start',
                  color: 'text.primary',
                  fontWeight: 600,
                  textTransform: 'none',
                  py: 2,
                  borderRadius: 4,
                  '&:hover': {
                    bgcolor: alpha('#6366F1', 0.1),
                    color: '#6366F1',
                  },
                }}
              >
                {item.label}
              </Button>
            ))}
          </Stack>
        </Paper>
      </Collapse>
    </Box>
  );
};

const Home: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [loginOpen, setLoginOpen] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [authMethod, setAuthMethod] = useState(0);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [copiedCode, setCopiedCode] = useState('');
  const [currentFeature, setCurrentFeature] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Mouse tracking for enhanced effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const codeExamples = [
    {
      title: 'Generate AI Video',
      language: 'curl',
      description: 'Create stunning videos from text topics with automated script generation',
      code: `curl -X POST "https://ouinhi.com/api/video/generate" \\
  -H "X-API-Key: your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "topic": "The future of artificial intelligence",
    "duration": 60,
    "voice": "nova",
    "style": "professional",
    "resolution": "1080x1920"
  }'

# Response: { 
#   "job_id": "abc-123", 
#   "status": "processing",
#   "estimated_time": "2-3 minutes"
# }`,
      id: 'video',
    },
    {
      title: 'Pollinations Image Generation',
      language: 'python',
      description: 'Generate high-quality images with cutting-edge Flux models',
      code: `import requests

response = requests.post(
    "https://api.ouinhi.com/api/pollinations/image/generate",
    headers={"X-API-Key": "your-api-key"},
    json={
        "prompt": "A futuristic cityscape with flying cars, cyberpunk style",
        "width": 1920,
        "height": 1080,
        "model": "flux",
        "enhance": True,
        "safety_filter": True
    }
)

job_data = response.json()
print(f"Job ID: {job_data['job_id']}")
print(f"Status: {job_data['status']}")`,
      id: 'image',
    },
    {
      title: 'Kokoro TTS Synthesis',
      language: 'javascript',
      description: 'High-quality text-to-speech with expressive voices',
      code: `const response = await fetch('/api/v1/audio/speech', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: "Hello! This is Kokoro TTS with natural expressive speech.",
    voice: "af_heart",
    provider: "kokoro",
    speed: 1.0,
    response_format: "wav"
  })
});

const result = await response.json();
console.log('Job ID:', result.job_id);`,
      id: 'tts',
    },
    {
      title: 'Social Media Automation',
      language: 'curl',
      description: 'Schedule and post content across multiple platforms',
      code: `curl -X POST "https://api.ouinhi.com/api/social/schedule" \\
  -H "X-API-Key: your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "content": "Check out my latest AI-generated video! 🚀",
    "media_url": "https://s3.../video.mp4",
    "platforms": ["twitter", "instagram", "tiktok", "youtube"],
    "schedule_time": "2024-12-01T15:00:00Z",
    "hashtags": ["#AI", "#VideoCreation", "#Tech"]
  }'

# Response: {
#   "scheduled_posts": 4,
#   "schedule_id": "xyz-789"
# }`,
      id: 'social',
    },
  ];

  // Auto-rotate code demos
  useEffect(() => {
    const codeExamplesLength = codeExamples.length;
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % codeExamplesLength);
    }, 4000);
    return () => clearInterval(interval);
  }, [codeExamples.length]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (authMethod === 0) {
      if (!apiKey.trim()) {
        setError('Please enter your API key');
        return;
      }
    } else {
      if (!username.trim() || !password.trim()) {
        setError('Please enter both username and password');
        return;
      }
    }

    setLoading(true);
    setError('');

    try {
      if (authMethod === 0) {
        await login(apiKey.trim());
      } else {
        await login(username.trim(), password);
      }
      setLoginOpen(false);
      navigate('/dashboard');
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Authentication failed. Please check your credentials.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const copyCode = (code: string, id: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(''), 2000);
  };

  const resetForm = () => {
    setApiKey('');
    setUsername('');
    setPassword('');
    setError('');
    setAuthMethod(0);
  };

  const mainFeatures = [
    {
      icon: <Movie sx={{ fontSize: 48 }} />,
      title: 'AI Video Generation',
      description: 'Create stunning videos from text prompts with Topic-to-Video pipeline, YouTube Shorts automation, and intelligent scene generation with real stock footage integration.',
      color: '#6366F1',
      bgColor: '#EEF2FF',
      features: ['Topic-to-Video Pipeline', 'YouTube Shorts Creation', 'Smart Scene Generation', 'Auto Stock Footage', 'Professional Captions'],
      gradient: 'linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%)',
    },
    {
      icon: <AutoAwesome sx={{ fontSize: 48 }} />,
      title: 'Pollinations.AI Integration',
      description: 'Access cutting-edge AI models for image generation, text completion, vision analysis, and premium text-to-speech synthesis with state-of-the-art Flux models.',
      color: '#EC4899',
      bgColor: '#FDF2F8',
      features: ['Flux Image Generation', 'GPT-4 Text Completion', 'Vision Analysis', 'Premium TTS Voices', 'Multi-Modal AI'],
      gradient: 'linear-gradient(135deg, #EC4899 0%, #F472B6 100%)',
    },
    {
      icon: <Mic sx={{ fontSize: 48 }} />,
      title: 'Kokoro TTS Engine',
      description: 'High-quality neural TTS with multiple expressive voices. Advanced features including voice combinations and word-level timestamps.',
      color: '#10B981',
      bgColor: '#ECFDF5',
      features: ['15M Parameters', '8 Voice Styles', 'CPU-Only Inference', 'Real-time Generation', 'High Quality Audio'],
      gradient: 'linear-gradient(135deg, #10B981 0%, #34D399 100%)',
    },
    {
      icon: <PhotoLibrary sx={{ fontSize: 48 }} />,
      title: 'Multi-Provider Search',
      description: 'Seamless integration with Pexels and Pixabay for unlimited stock footage, images, and media assets with intelligent filtering and HD/4K downloads.',
      color: '#F59E0B',
      bgColor: '#FFFBEB',
      features: ['Pexels Integration', 'Pixabay Access', 'Smart Filtering', 'HD/4K Downloads', 'Unlimited Assets'],
      gradient: 'linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%)',
    },
    {
      icon: <Share sx={{ fontSize: 48 }} />,
      title: 'Social Media Automation',
      description: 'Postiz integration for automated social media scheduling, cross-platform posting, engagement analytics, and content optimization across all major platforms.',
      color: '#8B5CF6',
      bgColor: '#F5F3FF',
      features: ['Multi-Platform Posting', 'Smart Scheduling', 'Analytics Dashboard', 'Content Optimization', 'Engagement Tracking'],
      gradient: 'linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%)',
    },
    {
      icon: <Transform sx={{ fontSize: 48 }} />,
      title: 'Media Processing Suite',
      description: 'Advanced media conversion, format optimization, compression, and batch processing with FFmpeg integration for professional-grade media workflows.',
      color: '#06B6D4',
      bgColor: '#ECFEFF',
      features: ['Format Conversion', 'Batch Processing', 'Quality Optimization', 'Custom Filters', 'Professional Workflows'],
      gradient: 'linear-gradient(135deg, #06B6D4 0%, #22D3EE 100%)',
    },
  ];

  const stats = [
    { number: '100+', label: 'AI Models', icon: <Science sx={{ fontSize: 32 }} />, color: '#6366F1' },
    { number: '1M+', label: 'Videos Created', icon: <VideoLibrary sx={{ fontSize: 32 }} />, color: '#EC4899' },
    { number: '50+', label: 'Languages', icon: <Psychology sx={{ fontSize: 32 }} />, color: '#10B981' },
    { number: '99.9%', label: 'Uptime', icon: <VerifiedUser sx={{ fontSize: 32 }} />, color: '#F59E0B' },
  ];

  const technicalFeatures = [
    {
      icon: <ElectricBolt sx={{ fontSize: 32 }} />,
      title: 'Lightning Fast',
      description: 'Async job queues with Redis-powered processing for sub-second response times',
      color: '#6366F1',
    },
    {
      icon: <Security sx={{ fontSize: 32 }} />,
      title: 'Enterprise Security',
      description: 'API key authentication, rate limiting, and comprehensive security analytics',
      color: '#EF4444',
    },
    {
      icon: <Storage sx={{ fontSize: 32 }} />,
      title: 'Scalable Storage',
      description: 'S3-compatible cloud storage with global CDN delivery and automatic backups',
      color: '#10B981',
    },
    {
      icon: <Api sx={{ fontSize: 32 }} />,
      title: 'REST + GraphQL',
      description: 'Multiple API interfaces with OpenAPI documentation and SDK generation',
      color: '#8B5CF6',
    },
    {
      icon: <Architecture sx={{ fontSize: 32 }} />,
      title: 'Microservices',
      description: 'Containerized architecture with Docker, Kubernetes, and auto-scaling',
      color: '#06B6D4',
    },
    {
      icon: <Analytics sx={{ fontSize: 32 }} />,
      title: 'Real-time Analytics',
      description: 'Usage tracking, performance metrics, and intelligent cost optimization',
      color: '#F59E0B',
    },
  ];

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'AI Researcher',
      company: 'TechCorp',
      content: 'Ouinhi has revolutionized our content creation workflow. The AI video generation is simply incredible!',
      avatar: '👩‍💻',
      rating: 5,
    },
    {
      name: 'Marcus Johnson',
      role: 'Content Creator',
      company: 'CreativeStudio',
      content: 'The Kokoro TTS voices sound so natural, and the social media automation saves us hours every week.',
      avatar: '🎨',
      rating: 5,
    },
    {
      name: 'Elena Rodriguez',
      role: 'CTO',
      company: 'StartupXYZ',
      content: 'Outstanding API design and documentation. Integration was seamless and the performance is exceptional.',
      avatar: '👩‍💼',
      rating: 5,
    },
  ];

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', overflow: 'hidden', position: 'relative' }}>
      {/* Scroll Progress Indicator */}
      <ScrollProgress />
      
      {/* Top Navigation Bar */}
      <TopNavBar onLoginClick={() => setLoginOpen(true)} />
      
      {/* Floating Navigation */}
      <FloatingNav onLoginClick={() => setLoginOpen(true)} />

      {/* Animated Particle Background */}
      <ParticleBackground />

      {/* Mouse Follower Effect */}
      <Box
        sx={{
          position: 'fixed',
          width: 300,
          height: 300,
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha('#6366F1', 0.05)} 0%, transparent 70%)`,
          pointerEvents: 'none',
          zIndex: 0,
          transform: `translate(${mousePosition.x - 150}px, ${mousePosition.y - 150}px)`,
          transition: 'transform 0.1s ease-out',
          filter: 'blur(20px)',
        }}
      />

      {/* Gradient Background Overlays */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -2,
          background: `
            radial-gradient(circle at 20% 20%, ${alpha('#6366F1', 0.15)} 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, ${alpha('#EC4899', 0.15)} 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, ${alpha('#10B981', 0.1)} 0%, transparent 50%)
          `,
        }}
      />

      {/* Hero Section */}
      <Box 
        id="hero" 
        data-animate 
        sx={{ 
          pt: { xs: 12, md: 16 }, 
          pb: { xs: 8, md: 16 }, 
          position: 'relative',
          background: `linear-gradient(135deg, ${alpha('#6366F1', 0.02)} 0%, ${alpha('#EC4899', 0.02)} 100%)`,
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={1000}>
            <Box textAlign="center" mb={8}>
              {/* Floating Badge with pulsing effect */}
              <Zoom in timeout={1500}>
                <Chip
                  icon={<Celebration />}
                  label="🚀 Most Advanced Media API Platform - Now with AI Video Generation!"
                  sx={{
                    mb: 6,
                    py: 2,
                    px: 4,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    background: `linear-gradient(135deg, ${alpha('#6366F1', 0.15)}, ${alpha('#EC4899', 0.15)})`,
                    color: 'primary.main',
                    border: `2px solid ${alpha('#6366F1', 0.3)}`,
                    backdropFilter: 'blur(10px)',
                    boxShadow: `0 8px 32px ${alpha('#6366F1', 0.2)}`,
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: `0 12px 40px ${alpha('#6366F1', 0.3)}`,
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: `linear-gradient(45deg, transparent 30%, ${alpha('#6366F1', 0.1)} 50%, transparent 70%)`,
                      transform: 'translateX(-100%)',
                      animation: 'shimmer 3s infinite',
                    },
                    '@keyframes shimmer': {
                      '0%': { transform: 'translateX(-100%)' },
                      '100%': { transform: 'translateX(100%)' },
                    },
                    transition: 'all 0.3s ease',
                  }}
                />
              </Zoom>

              <Typography
                variant="h1"
                sx={{
                  fontSize: { xs: '3.5rem', sm: '5rem', md: '7rem' },
                  fontWeight: 900,
                  background: `linear-gradient(135deg, #6366F1 0%, #EC4899 50%, #10B981 100%)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 4,
                  lineHeight: 0.85,
                  letterSpacing: '-0.03em',
                  textShadow: '0 0 40px rgba(99, 102, 241, 0.3)',
                }}
              >
                Ouinhi API
              </Typography>

              <Typography
                variant="h2"
                sx={{
                  color: 'text.primary',
                  mb: 4,
                  fontWeight: 600,
                  fontSize: { xs: '1.8rem', sm: '2.5rem', md: '3rem' },
                  letterSpacing: '-0.02em',
                }}
              >
                The Future of AI-Powered
                <Box component="span" sx={{ 
                  display: 'block',
                  background: `linear-gradient(135deg, #EC4899, #6366F1)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontWeight: 800,
                  mt: 1,
                }}>
                  Media Creation
                </Box>
              </Typography>

              <Typography
                variant="h5"
                sx={{
                  color: 'text.secondary',
                  mb: 8,
                  maxWidth: 1000,
                  mx: 'auto',
                  lineHeight: 1.6,
                  fontSize: { xs: '1.2rem', md: '1.4rem' },
                  fontWeight: 400,
                }}
              >
                Create stunning videos from text, generate speech with 50+ voices, process images with AI, 
                and automate social media - all through one powerful API that scales from hobby to enterprise. 
                <Box component="span" sx={{ color: 'primary.main', fontWeight: 600 }}>
                  Join 10,000+ developers already building the future.
                </Box>
              </Typography>

              <Stack 
                direction={{ xs: 'column', sm: 'row' }} 
                spacing={4} 
                justifyContent="center" 
                alignItems="center"
                sx={{ mb: 8 }}
              >
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => setLoginOpen(true)}
                  startIcon={<RocketLaunch />}
                  sx={{
                    px: 8,
                    py: 3,
                    fontSize: '1.3rem',
                    borderRadius: 6,
                    textTransform: 'none',
                    fontWeight: 700,
                    minWidth: 250,
                    background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
                    boxShadow: `0 12px 48px ${alpha('#6366F1', 0.4)}`,
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      background: `linear-gradient(135deg, #5855EB 0%, #DB2777 100%)`,
                      transform: 'translateY(-3px)',
                      boxShadow: `0 20px 60px ${alpha('#6366F1', 0.5)}`,
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-100%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                      transition: 'left 0.5s',
                    },
                    '&:hover::before': {
                      left: '100%',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  Start Building Free
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<PlayCircle />}
                  sx={{
                    px: 8,
                    py: 3,
                    fontSize: '1.3rem',
                    borderRadius: 6,
                    textTransform: 'none',
                    fontWeight: 700,
                    minWidth: 250,
                    borderWidth: 3,
                    borderColor: '#6366F1',
                    color: '#6366F1',
                    backdropFilter: 'blur(10px)',
                    background: alpha('#6366F1', 0.05),
                    '&:hover': {
                      borderWidth: 3,
                      borderColor: '#EC4899',
                      color: '#EC4899',
                      background: alpha('#EC4899', 0.1),
                      transform: 'translateY(-3px)',
                      boxShadow: `0 12px 32px ${alpha('#EC4899', 0.3)}`,
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  Watch Live Demo
                </Button>
              </Stack>

              {/* Trust Indicators */}
              <Stack 
                direction={{ xs: 'column', sm: 'row' }} 
                spacing={3} 
                alignItems="center" 
                justifyContent="center"
                sx={{ opacity: 0.8 }}
              >
                <Typography variant="body2" color="text.secondary" fontWeight={500}>
                  Trusted by developers at:
                </Typography>
                <Stack direction="row" spacing={2} alignItems="center">
                  {['TechCorp', 'StartupXYZ', 'CreativeStudio', 'AILabs'].map((company) => (
                    <Chip
                      key={company}
                      label={company}
                      size="small"
                      sx={{
                        bgcolor: alpha(theme.palette.background.paper, 0.8),
                        color: 'text.secondary',
                        fontWeight: 500,
                        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                      }}
                    />
                  ))}
                </Stack>
              </Stack>
            </Box>
          </Fade>

          {/* Enhanced Stats Grid */}
          <Grow in timeout={1500}>
            <Grid container spacing={4} sx={{ mb: 8 }}>
              {stats.map((stat, index) => (
                <Grid item xs={6} sm={3} key={index}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      textAlign: 'center',
                      borderRadius: 4,
                      background: `linear-gradient(135deg, ${alpha(stat.color, 0.05)} 0%, ${alpha(stat.color, 0.02)} 100%)`,
                      backdropFilter: 'blur(20px)',
                      border: `2px solid ${alpha(stat.color, 0.1)}`,
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                      position: 'relative',
                      overflow: 'hidden',
                      '&:hover': {
                        transform: 'translateY(-8px) scale(1.02)',
                        boxShadow: `0 25px 50px ${alpha(stat.color, 0.25)}`,
                        border: `2px solid ${alpha(stat.color, 0.3)}`,
                      },
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: 3,
                        background: `linear-gradient(90deg, ${stat.color}, ${alpha(stat.color, 0.5)})`,
                      },
                    }}
                  >
                    <Box 
                      sx={{ 
                        color: stat.color, 
                        mb: 2,
                        filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))',
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography
                      variant="h3"
                      sx={{
                        fontWeight: 800,
                        background: `linear-gradient(135deg, ${stat.color} 0%, ${alpha(stat.color, 0.7)} 100%)`,
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        mb: 1,
                        fontSize: { xs: '2rem', sm: '2.5rem' },
                      }}
                    >
                      {stat.number}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" fontWeight={600}>
                      {stat.label}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Grow>
        </Container>
      </Box>

      {/* Interactive Code Demo Section */}
      <Box 
        id="demo" 
        data-animate 
        sx={{ 
          py: 16, 
          background: `linear-gradient(135deg, ${alpha('#6366F1', 0.02)} 0%, ${alpha('#10B981', 0.02)} 100%)`,
        }}
      >
        <Container maxWidth="xl">
          <Box textAlign="center" mb={12}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                fontWeight: 800,
                mb: 4,
                background: `linear-gradient(135deg, #6366F1 0%, #10B981 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              See It In Action
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'text.secondary',
                maxWidth: 800,
                mx: 'auto',
                lineHeight: 1.7,
                fontSize: '1.2rem',
                fontWeight: 400,
              }}
            >
              Powerful APIs that are incredibly easy to use. Get started in minutes with our comprehensive documentation and real-world examples.
            </Typography>
          </Box>

          <Grid container spacing={6} alignItems="stretch">
            <Grid item xs={12} lg={5}>
              <Stack spacing={3}>
                {codeExamples.map((example, index) => (
                  <Slide key={index} direction="right" in timeout={1000 + index * 200}>
                    <Paper
                      onClick={() => setCurrentFeature(index)}
                      sx={{
                        p: 4,
                        borderRadius: 4,
                        cursor: 'pointer',
                        border: `3px solid ${currentFeature === index ? '#6366F1' : 'transparent'}`,
                        background: currentFeature === index 
                          ? `linear-gradient(135deg, ${alpha('#6366F1', 0.1)} 0%, ${alpha('#EC4899', 0.05)} 100%)`
                          : alpha(theme.palette.background.paper, 0.7),
                        backdropFilter: 'blur(20px)',
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        position: 'relative',
                        overflow: 'hidden',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: `0 20px 40px ${alpha('#6366F1', 0.15)}`,
                          border: `3px solid ${alpha('#6366F1', 0.5)}`,
                        },
                        '&::before': currentFeature === index ? {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: 4,
                          background: 'linear-gradient(90deg, #6366F1, #EC4899)',
                        } : {},
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                        <Box
                          sx={{
                            width: 60,
                            height: 60,
                            borderRadius: 2,
                            background: currentFeature === index
                              ? 'linear-gradient(135deg, #6366F1, #EC4899)'
                              : alpha('#6366F1', 0.1),
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexShrink: 0,
                          }}
                        >
                          <Code sx={{ 
                            color: currentFeature === index ? 'white' : '#6366F1',
                            fontSize: 28,
                          }} />
                        </Box>
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 700,
                              color: currentFeature === index ? '#6366F1' : 'text.primary',
                              mb: 1,
                            }}
                          >
                            {example.title}
                          </Typography>
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              color: 'text.secondary',
                              mb: 2,
                              lineHeight: 1.5,
                            }}
                          >
                            {example.description}
                          </Typography>
                          <Chip
                            label={example.language.toUpperCase()}
                            size="small"
                            sx={{
                              bgcolor: currentFeature === index ? alpha('#6366F1', 0.2) : alpha('#6366F1', 0.1),
                              color: '#6366F1',
                              fontWeight: 600,
                              fontSize: '0.75rem',
                            }}
                          />
                        </Box>
                      </Box>
                    </Paper>
                  </Slide>
                ))}
              </Stack>
            </Grid>
            
            <Grid item xs={12} lg={7}>
              <Slide direction="left" in timeout={1500}>
                <Paper
                  sx={{
                    borderRadius: 6,
                    background: '#0D1117',
                    border: `2px solid ${alpha('#6366F1', 0.3)}`,
                    overflow: 'hidden',
                    position: 'relative',
                    boxShadow: `0 25px 50px ${alpha('#000', 0.3)}`,
                  }}
                >
                  {/* Enhanced Code Header */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      px: 4,
                      py: 3,
                      background: `linear-gradient(135deg, #161B22 0%, #21262D 100%)`,
                      borderBottom: `1px solid ${alpha('#6366F1', 0.2)}`,
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#FF5F57' }} />
                        <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#FFBD2E' }} />
                        <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#28CA42' }} />
                      </Box>
                      <Typography
                        variant="body2"
                        sx={{ 
                          color: '#8B949E', 
                          fontFamily: 'Monaco, "Cascadia Code", monospace',
                          fontWeight: 600,
                        }}
                      >
                        {codeExamples[currentFeature].title}
                      </Typography>
                    </Box>
                    <Tooltip title={copiedCode === codeExamples[currentFeature].id ? '✅ Copied!' : 'Copy code'}>
                      <IconButton
                        size="small"
                        onClick={() => copyCode(codeExamples[currentFeature].code, codeExamples[currentFeature].id)}
                        sx={{ 
                          color: '#8B949E',
                          '&:hover': {
                            color: '#6366F1',
                            bgcolor: alpha('#6366F1', 0.1),
                          },
                        }}
                      >
                        {copiedCode === codeExamples[currentFeature].id ? <CheckCircle /> : <ContentCopy />}
                      </IconButton>
                    </Tooltip>
                  </Box>
                  
                  {/* Enhanced Code Content */}
                  <Box
                    component="pre"
                    sx={{
                      p: 4,
                      margin: 0,
                      color: '#E6EDF3',
                      fontSize: '0.95rem',
                      fontFamily: 'Monaco, "Cascadia Code", "Fira Code", monospace',
                      lineHeight: 1.7,
                      overflow: 'auto',
                      maxHeight: 500,
                      minHeight: 400,
                      background: `linear-gradient(135deg, #0D1117 0%, #161B22 100%)`,
                      // Syntax highlighting styles
                      '& .keyword': { color: '#FF7B72', fontWeight: 600 },
                      '& .string': { color: '#A5D6FF' },
                      '& .comment': { color: '#8B949E', fontStyle: 'italic' },
                      '& .number': { color: '#79C0FF' },
                      '& .operator': { color: '#FF7B72' },
                    }}
                  >
                    {codeExamples[currentFeature].code}
                  </Box>
                </Paper>
              </Slide>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Enhanced Main Features Section */}
      <Container maxWidth="xl" sx={{ py: 20 }}>
        <Box id="features" data-animate textAlign="center" mb={12}>
          <Typography
            variant="h2"
            sx={{
              fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
              fontWeight: 800,
              mb: 4,
              background: `linear-gradient(135deg, #EC4899 0%, #6366F1 50%, #10B981 100%)`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Powerful Features
          </Typography>
          <Typography
            variant="h5"
            sx={{
              color: 'text.secondary',
              maxWidth: 900,
              mx: 'auto',
              lineHeight: 1.6,
              fontSize: '1.2rem',
              fontWeight: 400,
            }}
          >
            Everything you need to build the next generation of media applications. 
            From AI-powered video creation to social media automation, we've got you covered.
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {mainFeatures.map((feature, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Grow in timeout={1000 + index * 200}>
                <Paper
                  sx={{
                    p: 6,
                    height: '100%',
                    borderRadius: 6,
                    border: `2px solid ${alpha(feature.color, 0.15)}`,
                    background: `linear-gradient(135deg, ${alpha(feature.bgColor, 0.4)} 0%, ${alpha(feature.bgColor, 0.1)} 100%)`,
                    transition: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                    position: 'relative',
                    overflow: 'hidden',
                    cursor: 'pointer',
                    '&:hover': {
                      transform: 'translateY(-12px) scale(1.02)',
                      boxShadow: `0 30px 80px ${alpha(feature.color, 0.25)}`,
                      border: `2px solid ${alpha(feature.color, 0.4)}`,
                      '& .feature-icon': {
                        transform: 'scale(1.2) rotate(10deg)',
                      },
                      '& .feature-gradient': {
                        opacity: 0.8,
                        transform: 'scale(1.2)',
                      },
                      '& .feature-content': {
                        transform: 'translateY(-2px)',
                      },
                      '& .feature-badge': {
                        opacity: 1,
                        transform: 'translateX(0)',
                      },
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 5,
                      background: feature.gradient,
                    },
                    '&::after': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: `linear-gradient(45deg, transparent 30%, ${alpha(feature.color, 0.05)} 50%, transparent 70%)`,
                      transform: 'translateX(-100%)',
                      transition: 'transform 0.6s',
                    },
                    '&:hover::after': {
                      transform: 'translateX(100%)',
                    },
                  }}
                >
                  {/* Background Gradient Overlay */}
                  <Box
                    className="feature-gradient"
                    sx={{
                      position: 'absolute',
                      top: -50,
                      right: -50,
                      width: 200,
                      height: 200,
                      borderRadius: '50%',
                      background: feature.gradient,
                      opacity: 0.1,
                      transition: 'all 0.4s ease',
                    }}
                  />

                  {/* New! Badge */}
                  <Box
                    className="feature-badge"
                    sx={{
                      position: 'absolute',
                      top: 20,
                      right: 20,
                      opacity: 0,
                      transform: 'translateX(20px)',
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <Chip
                      label="Popular"
                      size="small"
                      sx={{
                        bgcolor: feature.color,
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '0.7rem',
                        height: 24,
                        '& .MuiChip-label': { px: 1 },
                      }}
                    />
                  </Box>

                  <Stack spacing={4} height="100%" position="relative" zIndex={1} className="feature-content" sx={{ transition: 'transform 0.3s ease' }}>
                    <Box
                      className="feature-icon"
                      sx={{
                        width: 100,
                        height: 100,
                        borderRadius: 4,
                        background: feature.gradient,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        transition: 'all 0.4s ease',
                        boxShadow: `0 10px 30px ${alpha(feature.color, 0.3)}`,
                      }}
                    >
                      {feature.icon}
                    </Box>
                    
                    <Box>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 800,
                          mb: 3,
                          color: 'text.primary',
                          fontSize: { xs: '1.5rem', md: '1.8rem' },
                        }}
                      >
                        {feature.title}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          color: 'text.secondary',
                          lineHeight: 1.7,
                          mb: 4,
                          fontSize: '1.1rem',
                        }}
                      >
                        {feature.description}
                      </Typography>
                    </Box>

                    <Stack spacing={2} sx={{ mt: 'auto' }}>
                      {feature.features.map((item, idx) => (
                        <Box key={idx} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              background: feature.gradient,
                            }}
                          />
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              color: 'text.secondary',
                              fontWeight: 500,
                              fontSize: '0.95rem',
                            }}
                          >
                            {item}
                          </Typography>
                        </Box>
                      ))}
                    </Stack>
                  </Stack>
                </Paper>
              </Grow>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Technical Features with Enhanced Design */}
      <Box 
        id="technical" 
        data-animate 
        sx={{ 
          py: 16, 
          background: `linear-gradient(135deg, ${alpha('#0F172A', 0.05)} 0%, ${alpha('#1E293B', 0.05)} 100%)`,
        }}
      >
        <Container maxWidth="lg">
          <Box textAlign="center" mb={12}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 800,
                mb: 4,
                fontSize: { xs: '2rem', md: '3rem' },
                background: `linear-gradient(135deg, #0F172A 0%, #334155 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Built for Scale & Performance
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'text.secondary',
                maxWidth: 700,
                mx: 'auto',
                lineHeight: 1.7,
                fontSize: '1.1rem',
              }}
            >
              Enterprise-grade infrastructure designed for high-performance applications with real-time processing and global scale.
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {technicalFeatures.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Fade in timeout={1000 + index * 150}>
                  <Card
                    sx={{
                      p: 4,
                      height: '100%',
                      textAlign: 'center',
                      borderRadius: 4,
                      background: `linear-gradient(135deg, ${alpha(feature.color, 0.05)} 0%, ${alpha(feature.color, 0.02)} 100%)`,
                      backdropFilter: 'blur(20px)',
                      border: `2px solid ${alpha(feature.color, 0.1)}`,
                      transition: 'all 0.4s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: `0 20px 40px ${alpha(feature.color, 0.2)}`,
                        border: `2px solid ${alpha(feature.color, 0.3)}`,
                      },
                    }}
                  >
                    <Box 
                      sx={{ 
                        color: feature.color, 
                        mb: 3,
                        display: 'flex',
                        justifyContent: 'center',
                      }}
                    >
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          borderRadius: 2,
                          background: `linear-gradient(135deg, ${feature.color}, ${alpha(feature.color, 0.7)})`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                        }}
                      >
                        {feature.icon}
                      </Box>
                    </Box>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontWeight: 700, 
                        mb: 2,
                        fontSize: '1.1rem',
                      }}
                    >
                      {feature.title}
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{ lineHeight: 1.6 }}
                    >
                      {feature.description}
                    </Typography>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Enhanced Testimonials Section */}
      <Container maxWidth="lg" sx={{ py: 16 }}>
        <Box textAlign="center" mb={8}>
          <Chip
            label="✨ Customer Love"
            sx={{
              mb: 4,
              px: 3,
              py: 1,
              fontSize: '1rem',
              fontWeight: 600,
              background: `linear-gradient(135deg, ${alpha('#EC4899', 0.15)}, ${alpha('#6366F1', 0.15)})`,
              color: '#EC4899',
              border: `2px solid ${alpha('#EC4899', 0.2)}`,
            }}
          />
          <Typography
            variant="h3"
            sx={{
              fontWeight: 800,
              mb: 3,
              background: `linear-gradient(135deg, #EC4899 0%, #6366F1 100%)`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontSize: { xs: '2rem', md: '3rem' },
            }}
          >
            Loved by Developers & Creators
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: 'text.secondary',
              maxWidth: 600,
              mx: 'auto',
              lineHeight: 1.6,
              fontSize: '1.2rem',
            }}
          >
            See what our community is saying about Ouinhi API
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {testimonials.map((testimonial, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Fade in timeout={1200 + index * 200}>
                <Card
                  sx={{
                    p: 4,
                    height: '100%',
                    borderRadius: 6,
                    border: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.9)} 100%)`,
                    backdropFilter: 'blur(20px)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: `0 25px 50px ${alpha(theme.palette.common.black, 0.15)}`,
                      border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                    },
                  }}
                >
                  {/* Decorative Quote */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 10,
                      right: 20,
                      fontSize: '4rem',
                      fontFamily: 'Georgia, serif',
                      color: alpha(theme.palette.primary.main, 0.1),
                      lineHeight: 1,
                      pointerEvents: 'none',
                      zIndex: 1,
                    }}
                  >
                    "
                  </Box>

                  <Stack spacing={3} height="100%" sx={{ position: 'relative', zIndex: 2 }}>
                    {/* Star Rating */}
                    <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star 
                          key={i} 
                          sx={{ 
                            color: '#FFD700', 
                            fontSize: 20,
                            filter: 'drop-shadow(0 2px 4px rgba(255,215,0,0.3))',
                          }} 
                        />
                      ))}
                    </Box>

                    {/* Testimonial Content */}
                    <Typography
                      variant="body1"
                      sx={{
                        flex: 1,
                        fontStyle: 'italic',
                        lineHeight: 1.7,
                        color: 'text.primary',
                        fontSize: '1.1rem',
                        fontWeight: 500,
                        position: 'relative',
                        pl: 3,
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          left: 0,
                          top: 0,
                          bottom: 0,
                          width: 4,
                          background: `linear-gradient(135deg, #6366F1, #EC4899)`,
                          borderRadius: 2,
                        },
                      }}
                    >
                      "{testimonial.content}"
                    </Typography>

                    {/* Author Info */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mt: 'auto' }}>
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          borderRadius: '50%',
                          background: 'linear-gradient(135deg, #6366F1, #EC4899)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '1.8rem',
                          boxShadow: `0 8px 20px ${alpha('#6366F1', 0.3)}`,
                          transition: 'transform 0.3s ease',
                          '&:hover': {
                            transform: 'scale(1.1)',
                          },
                        }}
                      >
                        {testimonial.avatar}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography 
                          variant="h6" 
                          fontWeight={700} 
                          sx={{ 
                            color: 'text.primary',
                            mb: 0.5,
                          }}
                        >
                          {testimonial.name}
                        </Typography>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            color: 'text.secondary',
                            fontWeight: 600,
                          }}
                        >
                          {testimonial.role}
                        </Typography>
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            color: alpha(theme.palette.text.secondary, 0.7),
                            fontWeight: 500,
                          }}
                        >
                          {testimonial.company}
                        </Typography>
                      </Box>
                    </Box>
                  </Stack>
                </Card>
              </Fade>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Enhanced CTA Section */}
      <Box
        id="cta"
        data-animate
        sx={{
          py: 20,
          background: `
            linear-gradient(135deg, ${alpha('#6366F1', 0.08)} 0%, ${alpha('#EC4899', 0.08)} 50%, ${alpha('#10B981', 0.08)} 100%),
            radial-gradient(circle at 30% 30%, ${alpha('#6366F1', 0.15)} 0%, transparent 50%),
            radial-gradient(circle at 70% 70%, ${alpha('#EC4899', 0.15)} 0%, transparent 50%)
          `,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Box textAlign="center">
            <Zoom in timeout={1000}>
              <Typography
                variant="h1"
                sx={{
                  fontSize: { xs: '3rem', sm: '4rem', md: '5rem' },
                  fontWeight: 900,
                  mb: 4,
                  background: `linear-gradient(135deg, #6366F1 0%, #EC4899 50%, #10B981 100%)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Ready to Build the Future?
              </Typography>
            </Zoom>

            <Typography
              variant="h5"
              sx={{
                color: 'text.secondary',
                mb: 8,
                maxWidth: 800,
                mx: 'auto',
                lineHeight: 1.7,
                fontSize: '1.3rem',
                fontWeight: 400,
              }}
            >
              Join thousands of developers and creators building amazing applications with Ouinhi API. 
              Start creating today with our comprehensive suite of AI-powered tools.
            </Typography>

            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={3}
              justifyContent="center"
              alignItems="center"
              sx={{ mb: 8 }}
              flexWrap="wrap"
            >
              {[
                '🎬 AI Video Generation',
                '🎨 Image Creation',
                '🎙️ Voice Synthesis',
                '📱 Social Automation',
                '⚡ Real-time Processing',
                '🚀 Enterprise Scale',
              ].map((tag, index) => (
                <Fade key={tag} in timeout={1500 + index * 100}>
                  <Chip
                    label={tag}
                    sx={{
                      px: 3,
                      py: 1,
                      fontSize: '1rem',
                      fontWeight: 600,
                      background: `linear-gradient(135deg, ${alpha('#6366F1', 0.15)}, ${alpha('#EC4899', 0.15)})`,
                      color: '#6366F1',
                      border: `2px solid ${alpha('#6366F1', 0.2)}`,
                      backdropFilter: 'blur(10px)',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: `0 8px 20px ${alpha('#6366F1', 0.3)}`,
                      },
                      transition: 'all 0.3s ease',
                    }}
                  />
                </Fade>
              ))}
            </Stack>

            <Zoom in timeout={2000}>
              <Button
                variant="contained"
                size="large"
                onClick={() => setLoginOpen(true)}
                endIcon={<ArrowForward />}
                sx={{
                  px: 12,
                  py: 4,
                  fontSize: '1.4rem',
                  borderRadius: 8,
                  textTransform: 'none',
                  fontWeight: 800,
                  background: `linear-gradient(135deg, #6366F1 0%, #EC4899 50%, #10B981 100%)`,
                  boxShadow: `0 15px 60px ${alpha('#6366F1', 0.4)}`,
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    background: `linear-gradient(135deg, #5855EB 0%, #DB2777 50%, #059669 100%)`,
                    transform: 'translateY(-4px)',
                    boxShadow: `0 25px 80px ${alpha('#6366F1', 0.5)}`,
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: '-100%',
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
                    transition: 'left 0.6s',
                  },
                  '&:hover::before': {
                    left: '100%',
                  },
                  transition: 'all 0.4s ease',
                }}
              >
                Start Building Now - It's Free!
              </Button>
            </Zoom>
          </Box>
        </Container>
      </Box>

      {/* Enhanced Footer */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${alpha('#0F172A', 0.95)} 0%, ${alpha('#1E293B', 0.95)} 100%)`,
          color: 'white',
          py: 12,
          borderTop: `1px solid ${alpha('#6366F1', 0.2)}`,
          backdropFilter: 'blur(20px)',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={6}>
            <Grid item xs={12} md={6}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 800,
                  background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 3,
                }}
              >
                Ouinhi API
              </Typography>
              <Typography 
                variant="body1" 
                sx={{ 
                  mb: 4, 
                  lineHeight: 1.7,
                  color: alpha('#fff', 0.8),
                }}
              >
                The most advanced AI-powered media generation platform for developers and creators.
                Built with cutting-edge technology and designed for global scale.
              </Typography>
              <Typography 
                variant="body2" 
                sx={{ 
                  color: alpha('#fff', 0.6),
                  mb: 4,
                }}
              >
                © 2025 Ouinhi API. Built with ❤️ for the future of content creation.
              </Typography>
              <Stack direction="row" spacing={2}>
                <Chip
                  icon={<VerifiedUser />}
                  label="99.9% Uptime SLA"
                  sx={{
                    bgcolor: alpha('#10B981', 0.2),
                    color: '#10B981',
                    fontWeight: 600,
                    border: `1px solid ${alpha('#10B981', 0.3)}`,
                  }}
                />
                <Chip
                  icon={<Security />}
                  label="Enterprise Ready"
                  sx={{
                    bgcolor: alpha('#6366F1', 0.2),
                    color: '#6366F1',
                    fontWeight: 600,
                    border: `1px solid ${alpha('#6366F1', 0.3)}`,
                  }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography 
                variant="h6" 
                sx={{ 
                  fontWeight: 700, 
                  mb: 3,
                  color: '#fff',
                }}
              >
                Powered by Modern Tech Stack
              </Typography>
              <Grid container spacing={2}>
                {[
                  'Python 3.12', 'FastAPI', 'PostgreSQL', 'Redis', 
                  'Docker', 'S3 Storage', 'React 19', 'TypeScript'
                ].map((tech) => (
                  <Grid item key={tech}>
                    <Chip
                      label={tech}
                      size="small"
                      sx={{
                        bgcolor: alpha('#fff', 0.1),
                        color: alpha('#fff', 0.8),
                        border: `1px solid ${alpha('#fff', 0.2)}`,
                        fontWeight: 500,
                      }}
                    />
                  </Grid>
                ))}
              </Grid>
              <Box sx={{ mt: 4 }}>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    color: alpha('#fff', 0.6),
                    mb: 2,
                  }}
                >
                  Ready to scale your media applications?
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => setLoginOpen(true)}
                  sx={{
                    color: '#6366F1',
                    borderColor: '#6366F1',
                    '&:hover': {
                      borderColor: '#EC4899',
                      color: '#EC4899',
                      bgcolor: alpha('#EC4899', 0.1),
                    },
                  }}
                >
                  Get Started Today
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Enhanced Login Dialog */}
      <Dialog
        open={loginOpen}
        onClose={() => {
          setLoginOpen(false);
          resetForm();
        }}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 6,
            p: 2,
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.98)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
            backdropFilter: 'blur(40px)',
            border: `2px solid ${alpha('#6366F1', 0.1)}`,
          },
        }}
      >

      {/* Back to Top Button */}
      <BackToTopButton />
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={3} pb={2}>
            <Avatar
              sx={{
                width: 70,
                height: 70,
                background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
              }}
            >
              <RocketLaunch sx={{ fontSize: 32 }} />
            </Avatar>
            <Box>
              <Typography variant="h4" fontWeight={800}>
                Welcome to Ouinhi
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Choose your authentication method to get started building
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        
        <form onSubmit={handleLogin}>
          <DialogContent sx={{ pt: 2 }}>
            <Tabs 
              value={authMethod} 
              onChange={(_, newValue) => setAuthMethod(newValue)}
              sx={{ mb: 4 }}
              variant="fullWidth"
            >
              <Tab 
                label="API Key" 
                sx={{ 
                  fontWeight: 700,
                  fontSize: '1rem',
                }}
              />
              <Tab 
                label="Username & Password" 
                sx={{ 
                  fontWeight: 700,
                  fontSize: '1rem',
                }}
              />
            </Tabs>

            {authMethod === 0 ? (
              <TextField
                fullWidth
                label="API Key"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key"
                sx={{ mb: 3 }}
                autoFocus
                helperText="Your API key from the environment configuration or dashboard"
                variant="outlined"
              />
            ) : (
              <>
                <TextField
                  fullWidth
                  label="Username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter your username"
                  sx={{ mb: 3 }}
                  autoFocus
                  variant="outlined"
                />
                <TextField
                  fullWidth
                  label="Password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  sx={{ mb: 3 }}
                  variant="outlined"
                />
              </>
            )}

            <Collapse in={!!error}>
              <Alert 
                severity="error" 
                sx={{ 
                  mb: 3, 
                  borderRadius: 3,
                  border: `1px solid ${alpha('#EF4444', 0.2)}`,
                }}
              >
                {error}
              </Alert>
            </Collapse>
          </DialogContent>
          
          <DialogActions sx={{ px: 3, pb: 4, gap: 2 }}>
            <Button
              onClick={() => {
                setLoginOpen(false);
                resetForm();
              }}
              color="inherit"
              size="large"
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
                fontWeight: 600,
                fontSize: '1rem',
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              size="large"
              sx={{
                px: 6,
                py: 1.5,
                borderRadius: 3,
                fontWeight: 700,
                fontSize: '1rem',
                background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
                '&:hover': {
                  background: `linear-gradient(135deg, #5855EB 0%, #DB2777 100%)`,
                },
              }}
            >
              {loading ? 'Authenticating...' : 'Sign In & Start Building'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

// Back to Top Button
const BackToTopButton: React.FC = () => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setVisible(window.scrollY > 500);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <Fade in={visible}>
      <Box
        onClick={scrollToTop}
        sx={{
          position: 'fixed',
          bottom: 30,
          right: 30,
          width: 60,
          height: 60,
          borderRadius: '50%',
          background: `linear-gradient(135deg, #6366F1 0%, #EC4899 100%)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          zIndex: 1000,
          boxShadow: `0 10px 30px ${alpha('#6366F1', 0.4)}`,
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-3px) scale(1.1)',
            boxShadow: `0 15px 40px ${alpha('#6366F1', 0.5)}`,
          },
        }}
      >
        <KeyboardArrowDown 
          sx={{ 
            color: 'white', 
            fontSize: 28,
            transform: 'rotate(180deg)',
          }} 
        />
      </Box>
    </Fade>
  );
};

export default Home;