import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Tabs,
  Tab,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip
} from '@mui/material';
import {
  Add as CreateIcon,
  AutoAwesome,
  Lightbulb as TipsIcon,
  SmartToy as AIIcon,
  Psychology as ScriptIcon,
  LibraryBooks as ResearchIcon
} from '@mui/icons-material';

// Import modular content creation components
import { VideoCreatorTab, VideoGeneratorTab } from '../components/contentCreation';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`content-creation-tabpanel-${index}`}
    aria-labelledby={`content-creation-tab-${index}`}
  >
    {value === index && <Box>{children}</Box>}
  </div>
);

const ContentCreation: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  // Usage tips for each tab
  const usageTips = [
    {
      title: "Video Creator Tips",
      tips: [
        "Use the Research feature to gather information before generating a script",
        "Try different voice options to find the perfect narrator for your content",
        "Enable captions to increase engagement and accessibility",
        "Experiment with different media providers for varied visual styles",
        "Adjust the script type (facts, story, list) based on your content goals"
      ],
      bestFor: [
        "Educational content",
        "Product demonstrations",
        "Social media videos",
        "Storytelling content",
        "Explainer videos"
      ]
    },
    {
      title: "AI Video Generator Tips",
      tips: [
        "Be specific with your prompts for better results",
        "Try different AI providers (WaveSpeed, LTX-Video, ComfyUI) for varied outputs",
        "Use negative prompts to exclude unwanted elements",
        "For image-to-video, choose clear, high-quality images with good composition",
        "Experiment with different durations to find the sweet spot for your content"
      ],
      bestFor: [
        "Creative storytelling",
        "Concept visualization",
        "Animated content",
        "Fantasy/sci-fi scenes",
        "Abstract concepts"
      ]
    }
  ];

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 2
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Content Creation 🎬
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Create engaging videos with AI-powered script generation and research tools.
        </Typography>
      </Box>

      {/* Main Content with Sidebar */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, mb: 3, flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          aria-label="content creation tabs"
          sx={{ 
            borderBottom: 1, 
            borderColor: 'divider', 
            flexShrink: 0,
            '& .MuiTab-root': {
              fontSize: { xs: '0.8rem', sm: '0.875rem' },
              minWidth: { xs: 120, sm: 160 },
              py: { xs: 1, sm: 1.5 }
            }
          }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            label="Video Creator"
            icon={<CreateIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />}
            id="content-creation-tab-0"
            iconPosition="start"
            sx={{ 
              minWidth: { xs: 120, sm: 160 },
              '& .MuiTab-iconWrapper': {
                marginBottom: { xs: '2px !important', sm: '4px !important' }
              }
            }}
          />
          <Tab
            label="AI Video Generator"
            icon={<AutoAwesome sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />}
            id="content-creation-tab-1"
            iconPosition="start"
            sx={{ 
              minWidth: { xs: 120, sm: 160 },
              '& .MuiTab-iconWrapper': {
                marginBottom: { xs: '2px !important', sm: '4px !important' }
              }
            }}
          />
        </Tabs>

        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <Grid container spacing={3} sx={{ p: 3 }}>
            {/* Main Content Area */}
            <Grid item xs={12} lg={8}>
              <TabPanel value={tabValue} index={0}>
                <VideoCreatorTab />
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <VideoGeneratorTab />
              </TabPanel>
            </Grid>

            {/* Sidebar with Usage Tips */}
            <Grid item xs={12} lg={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, mb: 3 }}>
                <CardContent>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      mb: 2, 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: 1,
                      fontWeight: 600
                    }}
                  >
                    <TipsIcon color="primary" />
                    {usageTips[tabValue].title}
                  </Typography>
                  
                  <Typography variant="subtitle2" sx={{ mb: 1.5, fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AIIcon sx={{ fontSize: '1rem' }} />
                    Best Practices
                  </Typography>
                  <Box component="ul" sx={{ pl: 2, mb: 3 }}>
                    {usageTips[tabValue].tips.map((tip, index) => (
                      <Box component="li" key={index} sx={{ mb: 1, fontSize: '0.9rem' }}>
                        {tip}
                      </Box>
                    ))}
                  </Box>
                  
                  <Typography variant="subtitle2" sx={{ mb: 1.5, fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ScriptIcon sx={{ fontSize: '1rem' }} />
                    Best For
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                    {usageTips[tabValue].bestFor.map((item, index) => (
                      <Chip 
                        key={index} 
                        label={item} 
                        size="small" 
                        sx={{ 
                          backgroundColor: '#f0f9ff',
                          color: '#0369a1',
                          '& .MuiChip-label': {
                            fontSize: '0.75rem'
                          }
                        }} 
                      />
                    ))}
                  </Box>
                  
                  <Typography variant="subtitle2" sx={{ mb: 1.5, fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ResearchIcon sx={{ fontSize: '1rem' }} />
                    Pro Tips
                  </Typography>
                  <Box sx={{ 
                    p: 2, 
                    backgroundColor: '#f8fafc', 
                    borderRadius: 1, 
                    border: '1px solid #e2e8f0' 
                  }}>
                    <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                      {tabValue === 0 
                        ? "Use the 'Research Topic' feature before generating scripts for more accurate and detailed content. The AI will gather relevant information to make your video more informative and engaging."
                        : "For best results with AI video generation, be as descriptive as possible in your prompts. Include details about style, mood, setting, and specific elements you want to see."}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
              
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    Quick Start Guide
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {tabValue === 0 
                      ? "1. Enter a topic or use the research feature\n2. Generate or write a script\n3. Choose your voice and visual settings\n4. Add captions and background music\n5. Click 'Create Video' and wait for processing"
                      : "1. Choose between text-to-video or image-to-video\n2. Enter a detailed prompt\n3. Select your preferred AI provider\n4. Adjust video settings (duration, dimensions)\n5. Click 'Generate Video' and wait for processing"}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Need help? Check out our documentation or contact support for detailed guides.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Box>
  );
};

export default ContentCreation;