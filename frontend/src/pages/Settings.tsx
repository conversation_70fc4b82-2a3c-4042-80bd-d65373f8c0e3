import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  Button,
  TextField,
  Divider,
  Alert,
  alpha,
  useTheme,
  Icon<PERSON>utton,
  Toolt<PERSON>
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Save as SaveIcon,
  RestorePageTwoTone as ResetIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
  Api as ApiIcon,
  BugReport as BugReportIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { useVideoCreation } from '../hooks/useContentCreation';

interface SettingsState {
  autoRefresh: boolean;
  emailNotifications: boolean;
  apiLogging: boolean;
  maxConcurrentJobs: number;
  defaultVideoResolution: string;
  storageRetentionDays: number;
  error: string | null;
  success: string | null;
  loading: boolean;
}

interface SystemInfo {
  version: string;
  api_status: string;
  database: {
    status: string;
    version?: string;
    size_mb?: number;
    tables?: number;
  };
  redis: {
    status: string;
  };
  storage: {
    status: string;
    total_gb?: number;
    used_gb?: number;
    free_gb?: number;
    usage_percent?: number;
  };
  jobs?: {
    active?: number;
    completed?: number;
    failed?: number;
    total?: number;
  };
  api_keys?: {
    total?: number;
    active?: number;
    total_usage?: number;
  };
}

const Settings: React.FC = () => {
  const theme = useTheme();
  const { apiKey: authApiKey } = useAuth();
  const { clearAllJobs, clearOrphanedJobs } = useVideoCreation();
  const [debugStatus, setDebugStatus] = useState<string>('');
  const [settings, setSettings] = useState<SettingsState>({
    autoRefresh: true,
    emailNotifications: true,
    apiLogging: true,
    maxConcurrentJobs: 5,
    defaultVideoResolution: '1080x1920',
    storageRetentionDays: 90,
    error: null,
    success: null,
    loading: false
  });

  const [systemInfo, setSystemInfo] = useState<SystemInfo>({
    version: 'Loading...',
    api_status: 'unknown',
    database: { status: 'unknown' },
    redis: { status: 'unknown' },
    storage: { status: 'unknown' }
  });

  const handleClearAllJobs = async () => {
    try {
      setDebugStatus('Clearing all jobs from localStorage...');
      clearAllJobs();
      setDebugStatus('✅ Successfully cleared all jobs from localStorage');
      setTimeout(() => setDebugStatus(''), 3000);
    } catch (error) {
      setDebugStatus(`❌ Error clearing jobs: ${error}`);
      setTimeout(() => setDebugStatus(''), 5000);
    }
  };

  const handleClearOrphanedJobs = async () => {
    try {
      setDebugStatus('Checking for orphaned jobs...');
      await clearOrphanedJobs();
      setDebugStatus('✅ Successfully cleaned up orphaned jobs');
      setTimeout(() => setDebugStatus(''), 3000);
    } catch (error) {
      setDebugStatus(`❌ Error cleaning orphaned jobs: ${error}`);
      setTimeout(() => setDebugStatus(''), 5000);
    }
  };

  // Load settings from API
  const loadSettings = useCallback(async () => {
    try {
      if (!authApiKey) {
        throw new Error('No API key found');
      }

      const response = await fetch('/api/v1/dashboard/settings', {
        headers: {
          'X-API-Key': authApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load settings');
      }

      const data = await response.json();
      setSettings(prev => ({
        ...prev,
        autoRefresh: data.auto_refresh,
        emailNotifications: data.email_notifications,
        apiLogging: data.api_logging,
        maxConcurrentJobs: data.max_concurrent_jobs,
        defaultVideoResolution: data.default_video_resolution,
        storageRetentionDays: data.storage_retention_days,
        loading: false
      }));
    } catch (error) {
      console.error('Error loading settings:', error);
      setSettings(prev => ({
        ...prev,
        error: 'Failed to load settings',
        loading: false
      }));
    }
  }, [authApiKey]);

  // Load system information from API
  const loadSystemInfo = useCallback(async () => {
    try {
      if (!authApiKey) {
        return;
      }

      const response = await fetch('/api/v1/dashboard/system-info', {
        headers: {
          'X-API-Key': authApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSystemInfo(data);
      }
    } catch (error) {
      console.error('Error loading system info:', error);
    }
  }, [authApiKey]);

  const handleSave = async () => {
    setSettings(prev => ({ ...prev, loading: true, error: null, success: null }));
    
    try {
      if (!authApiKey) {
        throw new Error('No API key found');
      }

      const payload = {
        auto_refresh: settings.autoRefresh,
        email_notifications: settings.emailNotifications,
        api_logging: settings.apiLogging,
        max_concurrent_jobs: settings.maxConcurrentJobs,
        default_video_resolution: settings.defaultVideoResolution,
        storage_retention_days: settings.storageRetentionDays
      };

      const response = await fetch('/api/v1/dashboard/settings', {
        method: 'PUT',
        headers: {
          'X-API-Key': authApiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      setSettings(prev => ({
        ...prev,
        success: 'Settings saved successfully',
        error: null,
        loading: false
      }));

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSettings(prev => ({ ...prev, success: null }));
      }, 3000);

    } catch (error) {
      console.error('Error saving settings:', error);
      setSettings(prev => ({
        ...prev,
        error: 'Failed to save settings',
        success: null,
        loading: false
      }));
    }
  };

  const handleReset = () => {
    setSettings({
      autoRefresh: true,
      emailNotifications: true,
      apiLogging: true,
      maxConcurrentJobs: 5,
      defaultVideoResolution: '1080x1920',
      storageRetentionDays: 90,
      error: null,
      success: null,
      loading: false
    });
  };

  // Load data on component mount
  useEffect(() => {
    setSettings(prev => ({ ...prev, loading: true }));
    loadSettings();
    loadSystemInfo();
  }, [loadSettings, loadSystemInfo]);

  const SettingCard: React.FC<{
    title: string;
    description: string;
    icon: React.ReactNode;
    children: React.ReactNode;
  }> = ({ title, description, icon, children }) => (
    <Card elevation={1}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Box 
            sx={{ 
              p: 1, 
              borderRadius: 1, 
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            {icon}
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="medium">
              {title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          </Box>
        </Box>
        {children}
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box 
            sx={{ 
              p: 1.5, 
              borderRadius: 2, 
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            <SettingsIcon />
          </Box>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              Settings
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Configure system preferences and behavior
            </Typography>
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Reset to defaults">
            <IconButton onClick={handleReset}>
              <ResetIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={settings.loading}
          >
            {settings.loading ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>
      </Box>

      {/* Alerts */}
      {settings.error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {settings.error}
        </Alert>
      )}
      
      {settings.success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {settings.success}
        </Alert>
      )}

      {/* Settings Grid */}
      <Grid container spacing={3}>
        {/* General Settings */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="General"
            description="Basic application preferences"
            icon={<SettingsIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.autoRefresh}
                    onChange={(e) => setSettings(prev => ({ ...prev, autoRefresh: e.target.checked }))}
                  />
                }
                label="Auto-refresh dashboard data"
              />
              
              <TextField
                label="Default Video Resolution"
                value={settings.defaultVideoResolution}
                onChange={(e) => setSettings(prev => ({ ...prev, defaultVideoResolution: e.target.value }))}
                helperText="Format: WIDTHxHEIGHT (e.g., 1080x1920)"
                fullWidth
                size="small"
              />
              
              <TextField
                label="Max Concurrent Jobs"
                type="number"
                value={settings.maxConcurrentJobs}
                onChange={(e) => setSettings(prev => ({ ...prev, maxConcurrentJobs: parseInt(e.target.value) || 5 }))}
                helperText="Maximum number of jobs to process simultaneously"
                fullWidth
                size="small"
                inputProps={{ min: 1, max: 20 }}
              />
            </Box>
          </SettingCard>
        </Grid>

        {/* Notifications */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="Notifications"
            description="Email and system notifications"
            icon={<NotificationsIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.emailNotifications}
                    onChange={(e) => setSettings(prev => ({ ...prev, emailNotifications: e.target.checked }))}
                  />
                }
                label="Email notifications for job completion"
              />
              
              <Typography variant="body2" color="text.secondary">
                Receive email notifications when video generation jobs complete or fail.
              </Typography>
            </Box>
          </SettingCard>
        </Grid>

        {/* API Settings */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="API Configuration"
            description="API behavior and logging"
            icon={<ApiIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.apiLogging}
                    onChange={(e) => setSettings(prev => ({ ...prev, apiLogging: e.target.checked }))}
                  />
                }
                label="Enable detailed API logging"
              />
              
              <Typography variant="body2" color="text.secondary">
                Log detailed information about API requests and responses for debugging.
              </Typography>
            </Box>
          </SettingCard>
        </Grid>

        {/* Storage Settings */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="Storage"
            description="Data retention and cleanup"
            icon={<StorageIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Storage Retention (Days)"
                type="number"
                value={settings.storageRetentionDays}
                onChange={(e) => setSettings(prev => ({ ...prev, storageRetentionDays: parseInt(e.target.value) || 90 }))}
                helperText="Number of days to retain generated videos and data"
                fullWidth
                size="small"
                inputProps={{ min: 1, max: 365 }}
              />
              
              <Typography variant="body2" color="text.secondary">
                Videos and job data older than this will be automatically deleted.
              </Typography>
            </Box>
          </SettingCard>
        </Grid>

        {/* Debug Tools */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="Debug Tools"
            description="Development and troubleshooting utilities"
            icon={<BugReportIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={handleClearOrphanedJobs}
                fullWidth
                size="small"
              >
                Clear Orphaned Jobs
              </Button>
              
              <Button
                variant="outlined"
                color="warning"
                startIcon={<ClearIcon />}
                onClick={handleClearAllJobs}
                fullWidth
                size="small"
              >
                Clear All Jobs
              </Button>
              
              {debugStatus && (
                <Alert 
                  severity={debugStatus.includes('❌') ? 'error' : 'success'} 
                  sx={{ mt: 1 }}
                >
                  {debugStatus}
                </Alert>
              )}
              
              <Typography variant="body2" color="text.secondary">
                Use these tools to fix issues with stuck job polling.
              </Typography>
            </Box>
          </SettingCard>
        </Grid>

        {/* System Information */}
        <Grid item xs={12}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Box 
                sx={{ 
                  p: 1, 
                  borderRadius: 1, 
                  backgroundColor: alpha(theme.palette.info.main, 0.1),
                  color: theme.palette.info.main
                }}
              >
                <SecurityIcon />
              </Box>
              <Typography variant="h6" fontWeight="medium">
                System Information
              </Typography>
            </Box>
            
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Version
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  Ouinhi v{systemInfo.version}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  API Status
                </Typography>
                <Typography 
                  variant="body1" 
                  fontWeight="medium" 
                  color={systemInfo.api_status === 'operational' ? 'success.main' : 'error.main'}
                >
                  {systemInfo.api_status?.charAt(0).toUpperCase() + systemInfo.api_status?.slice(1)}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Database
                </Typography>
                <Typography 
                  variant="body1" 
                  fontWeight="medium" 
                  color={systemInfo.database.status === 'connected' ? 'success.main' : 'error.main'}
                >
                  {systemInfo.database.status?.charAt(0).toUpperCase() + systemInfo.database.status?.slice(1)}
                </Typography>
                {systemInfo.database.size_mb && (
                  <Typography variant="caption" color="text.secondary">
                    {systemInfo.database.size_mb}MB, {systemInfo.database.tables} tables
                  </Typography>
                )}
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Storage
                </Typography>
                <Typography 
                  variant="body1" 
                  fontWeight="medium" 
                  color={systemInfo.storage.status === 'available' ? 'success.main' : 'error.main'}
                >
                  {systemInfo.storage.status?.charAt(0).toUpperCase() + systemInfo.storage.status?.slice(1)}
                </Typography>
                {systemInfo.storage.used_gb && systemInfo.storage.total_gb && (
                  <Typography variant="caption" color="text.secondary">
                    {systemInfo.storage.used_gb}GB / {systemInfo.storage.total_gb}GB ({systemInfo.storage.usage_percent}%)
                  </Typography>
                )}
              </Grid>
              
              {/* Additional System Info */}
              {systemInfo.redis && (
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Redis Cache
                  </Typography>
                  <Typography 
                    variant="body1" 
                    fontWeight="medium" 
                    color={systemInfo.redis.status === 'connected' ? 'success.main' : 'error.main'}
                  >
                    {systemInfo.redis.status?.charAt(0).toUpperCase() + systemInfo.redis.status?.slice(1)}
                  </Typography>
                </Grid>
              )}
              
              {systemInfo.jobs && (
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Jobs
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {systemInfo.jobs.active || 0} active, {systemInfo.jobs.completed || 0} completed
                  </Typography>
                </Grid>
              )}
              
              {systemInfo.api_keys && (
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    API Keys
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {systemInfo.api_keys.active || 0} active, {systemInfo.api_keys.total_usage || 0} total usage
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;