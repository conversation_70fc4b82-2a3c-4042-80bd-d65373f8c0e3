import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  Search as SearchIcon,
  Movie as ScenesIcon,
  Newspaper as NewsIcon,
  Chat as ChatIcon,
  ExpandMore as ExpandMoreIcon,
  Download as DownloadIcon,
  PlayArrow as PlayIcon,
  ContentCopy as CopyIcon,
  VideoLibrary as VideoIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-tools-tabpanel-${index}`}
      aria-labelledby={`ai-tools-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    script?: string;
    script_content?: string;
    final_video_url?: string;
    video_url?: string;
    search_queries?: string[];
    // New query format from video search API
    queries?: Array<{
      query: string;
      start_time: number;
      end_time: number;
      duration: number;
      visual_concept?: string;
    }>;
    total_duration?: number;
    total_segments?: number;
    videos?: Array<{ 
      id: string; 
      url: string; 
      image: string; 
      duration: number;
      width: number;
      height: number;
      user: { name: string };
    }>;
    // Enhanced articles structure for news research
    articles?: Array<{
      title: string;
      description?: string;
      snippet?: string;
      content?: string;
      url?: string;
      link?: string;
      source: string;
      publishedAt?: string;
      date?: string;
      provider?: string;
      model_used?: string;
      research_type?: string;
      search_result?: boolean;
    }>;
    // Enhanced news research fields
    summary?: string;
    sources?: string[];
    sources_used?: string[];
    total_sources?: number;
    research_date?: string;
    content?: string;
    // Video search result fields
    query_used?: string;
    provider_used?: string;
    total_results?: number;
    page?: number;
    per_page?: number;
    [key: string]: unknown;
  };
  error?: string | null;
}

const AIVideoTools: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [results, setResults] = useState<Record<string, JobResult | null>>({});
  const [errors, setErrors] = useState<Record<string, string | null>>({});
  const [jobStatuses, setJobStatuses] = useState<Record<string, string>>({});

  // Form states for different tools
  const [scriptGenForm, setScriptGenForm] = useState({
    topic: '',
    script_type: 'facts',
    language: 'en',
    target_duration: 60,
    style: 'engaging'
  });

  const [videoSearchForm, setVideoSearchForm] = useState({
    topic: '',
    language: 'en',
    orientation: 'portrait',
    per_page: 15,
    page: 1
  });

  const [manualSearchForm, setManualSearchForm] = useState({
    query: '',
    provider: 'pexels',
    orientation: 'landscape',
    per_page: 15,
    page: 1
  });

  const [scenesForm, setScenesForm] = useState({
    scenes: [
      { text: '', searchTerms: [''], duration: 3.0 }
    ],
    voice_provider: 'kokoro',
    voice_name: 'af_bella',
    language: 'en',
    resolution: '1080x1920'
  });

  const [newsResearchForm, setNewsResearchForm] = useState({
    searchTerm: '',
    targetLanguage: 'en',
    maxResults: 5
  });

  const [aiChatForm, setAiChatForm] = useState({
    prompt: '',
    max_tokens: 1000,
    temperature: 0.7,
    provider: 'auto'
  });

  // Generic job polling function
  const pollJobStatus = async (jobId: string, endpoint: string, toolName: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`${endpoint}/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatuses(prev => ({ ...prev, [toolName]: `${status} (${attempts}/${maxAttempts})` }));

        if (status === 'completed') {
          setResults(prev => ({ ...prev, [toolName]: { job_id: jobId, result: jobResult, status: 'completed' } }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        } else if (status === 'failed') {
          setErrors(prev => ({ ...prev, [toolName]: jobError || 'Job failed' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Job polling timeout' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Failed to check job status' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      }
    };

    poll();
  };

  const handleScriptGeneration = async () => {
    if (!scriptGenForm.topic.trim()) {
      setErrors(prev => ({ ...prev, scriptgen: 'Topic is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, scriptgen: true }));
    setErrors(prev => ({ ...prev, scriptgen: null }));
    setResults(prev => ({ ...prev, scriptgen: null }));

    try {
      const response = await directApi.post('/api/v1/ai/script-generation', scriptGenForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/script-generation', 'scriptgen');
      } else {
        setErrors(prev => ({ ...prev, scriptgen: 'Failed to create script generation job' }));
        setLoading(prev => ({ ...prev, scriptgen: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, scriptgen: errorMessage }));
      setLoading(prev => ({ ...prev, scriptgen: false }));
    }
  };

  const handleVideoSearch = async () => {
    if (!videoSearchForm.topic.trim()) {
      setErrors(prev => ({ ...prev, videosearch: 'Topic is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, videosearch: true }));
    setErrors(prev => ({ ...prev, videosearch: null }));
    setResults(prev => ({ ...prev, videosearch: null }));

    try {
      const response = await directApi.post('/api/v1/ai/video-search-queries', videoSearchForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/video-search-queries', 'videosearch');
      } else {
        setErrors(prev => ({ ...prev, videosearch: 'Failed to create video search job' }));
        setLoading(prev => ({ ...prev, videosearch: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, videosearch: errorMessage }));
      setLoading(prev => ({ ...prev, videosearch: false }));
    }
  };

  const handleManualVideoSearch = async () => {
    if (!manualSearchForm.query.trim()) {
      setErrors(prev => ({ ...prev, manualsearch: 'Search query is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, manualsearch: true }));
    setErrors(prev => ({ ...prev, manualsearch: null }));
    setResults(prev => ({ ...prev, manualsearch: null }));

    try {
      const response = await directApi.post('/api/v1/ai/video-browse', manualSearchForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/video-browse', 'manualsearch');
      } else {
        setErrors(prev => ({ ...prev, manualsearch: 'Failed to create manual video search job' }));
        setLoading(prev => ({ ...prev, manualsearch: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, manualsearch: errorMessage }));
      setLoading(prev => ({ ...prev, manualsearch: false }));
    }
  };

  const handleScenesVideo = async () => {
    const validScenes = scenesForm.scenes.filter(scene => scene.text.trim() && scene.searchTerms.some(term => term.trim()));
    if (validScenes.length === 0) {
      setErrors(prev => ({ ...prev, scenes: 'At least one scene with text and search terms is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, scenes: true }));
    setErrors(prev => ({ ...prev, scenes: null }));
    setResults(prev => ({ ...prev, scenes: null }));

    try {
      const response = await directApi.post('/api/v1/ai/scenes-to-video', {
        ...scenesForm,
        scenes: validScenes
      });
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/scenes-to-video', 'scenes');
      } else {
        setErrors(prev => ({ ...prev, scenes: 'Failed to create scenes video job' }));
        setLoading(prev => ({ ...prev, scenes: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, scenes: errorMessage }));
      setLoading(prev => ({ ...prev, scenes: false }));
    }
  };

  const handleNewsResearch = async () => {
    if (!newsResearchForm.searchTerm.trim()) {
      setErrors(prev => ({ ...prev, news: 'Search term is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, news: true }));
    setErrors(prev => ({ ...prev, news: null }));
    setResults(prev => ({ ...prev, news: null }));

    try {
      const response = await directApi.post('/api/v1/ai/news-research', newsResearchForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/news-research', 'news');
      } else {
        setErrors(prev => ({ ...prev, news: 'Failed to create news research job' }));
        setLoading(prev => ({ ...prev, news: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, news: errorMessage }));
      setLoading(prev => ({ ...prev, news: false }));
    }
  };

  const handleAIChat = async () => {
    if (!aiChatForm.prompt.trim()) {
      setErrors(prev => ({ ...prev, aichat: 'Prompt is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, aichat: true }));
    setErrors(prev => ({ ...prev, aichat: null }));
    setResults(prev => ({ ...prev, aichat: null }));

    try {
      const response = await directApi.post('/api/v1/ai/generate', aiChatForm);
      setResults(prev => ({ ...prev, aichat: { job_id: 'chat', result: response.data, status: 'completed' } }));
      setLoading(prev => ({ ...prev, aichat: false }));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, aichat: errorMessage }));
      setLoading(prev => ({ ...prev, aichat: false }));
    }
  };

  const addScene = () => {
    setScenesForm(prev => ({
      ...prev,
      scenes: [...prev.scenes, { text: '', searchTerms: [''], duration: 3.0 }]
    }));
  };

  const removeScene = (index: number) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.filter((_, i) => i !== index)
    }));
  };

  const updateScene = (index: number, field: string, value: unknown) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === index ? { ...scene, [field]: value } : scene
      )
    }));
  };

  const addSearchTerm = (sceneIndex: number) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === sceneIndex 
          ? { ...scene, searchTerms: [...scene.searchTerms, ''] }
          : scene
      )
    }));
  };

  const removeSearchTerm = (sceneIndex: number, termIndex: number) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === sceneIndex 
          ? { ...scene, searchTerms: scene.searchTerms.filter((_, ti) => ti !== termIndex) }
          : scene
      )
    }));
  };

  const updateSearchTerm = (sceneIndex: number, termIndex: number, value: string) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === sceneIndex 
          ? { 
              ...scene, 
              searchTerms: scene.searchTerms.map((term, ti) => 
                ti === termIndex ? value : term
              ) 
            }
          : scene
      )
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderJobResult = (toolName: string, result: JobResult | null, icon: React.ReactNode) => {
    if (!result && !loading[toolName] && !errors[toolName]) return null;

    return (
      <Card elevation={0} sx={{ border: '1px solid #e2e8f0', mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon}
            {toolName.charAt(0).toUpperCase() + toolName.slice(1)} Result
            {loading[toolName] && <CircularProgress size={20} sx={{ ml: 1 }} />}
          </Typography>

          {loading[toolName] && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress sx={{ mb: 1, height: 6, borderRadius: 3 }} />
              <Typography variant="body2" color="text.secondary">
                Status: {jobStatuses[toolName] || 'Processing...'}
              </Typography>
            </Box>
          )}

          {errors[toolName] && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors[toolName]}
            </Alert>
          )}

          {result && result.result && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 {toolName.charAt(0).toUpperCase() + toolName.slice(1)} completed successfully!
              </Alert>

              {/* Script Content */}
              {(result.result.script || result.result.script_content) && (
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      Generated Script
                    </Typography>
                    <Button
                      startIcon={<CopyIcon />}
                      onClick={() => copyToClipboard(result.result?.script || result.result?.script_content || '')}
                      variant="outlined"
                      size="small"
                    >
                      Copy Script
                    </Button>
                  </Box>
                  
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 400, overflow: 'auto' }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.result.script || result.result.script_content}
                    </Typography>
                  </Paper>
                </Box>
              )}

              {/* Search Queries - AI Generated */}
              {result.result?.queries && Array.isArray(result.result.queries) && result.result.queries.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    AI-Generated Search Queries ({result.result.queries.length})
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {result.result.queries.map((queryObj, index) => (
                      <Card key={index} elevation={1} sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                              "{queryObj.query}"
                            </Typography>
                            {queryObj.visual_concept && (
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                {queryObj.visual_concept}
                              </Typography>
                            )}
                            <Typography variant="caption" color="text.secondary">
                              Duration: {queryObj.duration}s ({queryObj.start_time}s - {queryObj.end_time}s)
                            </Typography>
                          </Box>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => copyToClipboard(queryObj.query)}
                            sx={{ ml: 2 }}
                          >
                            Copy
                          </Button>
                        </Box>
                      </Card>
                    ))}
                  </Box>
                  {result.result?.total_duration && (
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                      Total Duration: {result.result.total_duration}s • {result.result?.total_segments || 0} segments
                    </Typography>
                  )}
                </Box>
              )}

              {/* Legacy Search Queries (fallback) */}
              {result.result?.search_queries && Array.isArray(result.result.search_queries) && result.result.search_queries.length > 0 && !result.result?.queries && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    AI-Generated Search Queries
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {result.result.search_queries.map((query, index) => (
                      <Chip 
                        key={index} 
                        label={query} 
                        variant="outlined"
                        onClick={() => copyToClipboard(query)}
                        sx={{ cursor: 'pointer' }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Video Results */}
              {result.result?.videos && Array.isArray(result.result.videos) && result.result.videos.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    Found Videos ({result.result.videos.length})
                  </Typography>
                  
                  {/* Video Search Details */}
                  <Box sx={{ mb: 2, p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                    <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                      {result.result?.query_used && typeof result.result.query_used === 'string' && (
                        <Grid item xs={12} sm={6}>
                          <strong>Search Query:</strong> {result.result.query_used}
                        </Grid>
                      )}
                      {result.result?.provider_used && typeof result.result.provider_used === 'string' && (
                        <Grid item xs={12} sm={6}>
                          <strong>Provider:</strong> {result.result.provider_used.charAt(0).toUpperCase() + result.result.provider_used.slice(1)}
                        </Grid>
                      )}
                      {result.result?.total_results && typeof result.result.total_results === 'number' && (
                        <Grid item xs={12} sm={6}>
                          <strong>Total Available:</strong> {result.result.total_results.toLocaleString()} videos
                        </Grid>
                      )}
                      {result.result?.page && result.result?.per_page && typeof result.result.page === 'number' && typeof result.result.per_page === 'number' && (
                        <Grid item xs={12} sm={6}>
                          <strong>Page:</strong> {result.result.page} ({result.result.per_page} per page)
                        </Grid>
                      )}
                    </Grid>
                  </Box>
                  <Grid container spacing={{ xs: 1.5, sm: 2 }}>
                    {result.result.videos.slice(0, 12).map((video, index) => (
                      <Grid item xs={12} sm={6} md={4} key={video.id || `video-${index}`}>
                        <Card elevation={1}>
                          <Box sx={{ position: 'relative' }}>
                            {video.image ? (
                              <img
                                src={video.image}
                                alt={`Video ${index + 1}`}
                                style={{
                                  width: '100%',
                                  height: '120px',
                                  objectFit: 'cover'
                                }}
                                onError={(e) => {
                                  // Replace with a colored background if image fails
                                  e.currentTarget.style.display = 'none';
                                  const parent = e.currentTarget.parentElement;
                                  if (parent && !parent.querySelector('.placeholder-bg')) {
                                    const placeholder = document.createElement('div');
                                    placeholder.className = 'placeholder-bg';
                                    placeholder.style.cssText = 'width: 100%; height: 120px; background: #f5f5f5; display: flex; align-items: center; justify-content: center; color: #999;';
                                    placeholder.textContent = 'Video Preview';
                                    parent.appendChild(placeholder);
                                  }
                                }}
                              />
                            ) : (
                              <Box
                                sx={{
                                  width: '100%',
                                  height: '120px',
                                  bgcolor: '#f5f5f5',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: '#999'
                                }}
                              >
                                Video Preview
                              </Box>
                            )}
                            <Chip 
                              label={`${video.duration || 0}s`}
                              size="small"
                              sx={{ 
                                position: 'absolute', 
                                bottom: 8, 
                                right: 8,
                                bgcolor: 'rgba(0,0,0,0.7)',
                                color: 'white'
                              }}
                            />
                          </Box>
                          <CardContent sx={{ p: 1.5 }}>
                            <Typography variant="caption" color="text.secondary">
                              {video.width || '?'}x{video.height || '?'} • by {video.user?.name || 'Unknown'}
                            </Typography>
                            <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                              <Button
                                startIcon={<PlayIcon />}
                                href={video.url || '#'}
                                target="_blank"
                                size="small"
                                variant="outlined"
                                sx={{ flex: 1 }}
                                disabled={!video.url}
                              >
                                View
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              {/* News Research Results */}
              {((result.result?.articles && Array.isArray(result.result.articles) && result.result.articles.length > 0) || result.result?.summary) && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    News Research Results
                  </Typography>
                  
                  {/* Research Summary */}
                  {result.result?.summary && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Research Summary
                      </Typography>
                      <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 500, overflow: 'auto' }}>
                        <Typography 
                          variant="body2" 
                          component="div"
                          sx={{ 
                            whiteSpace: 'pre-wrap',
                            lineHeight: 1.6,
                            fontSize: '0.875rem',
                            '& strong': { fontWeight: 700 },
                            // Handle markdown-style formatting
                            '& h1, & h2, & h3, & h4': { 
                              fontWeight: 600, 
                              marginTop: 2, 
                              marginBottom: 1,
                              fontSize: '1rem'
                            }
                          }}
                          dangerouslySetInnerHTML={{
                            __html: (result.result?.summary || '')
                              // Convert **text** to bold
                              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                              // Convert ### Header to h3
                              .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                              // Convert ## Header to h2
                              .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                              // Convert # Header to h1
                              .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                              // Convert - bullet points to HTML lists
                              .replace(/^- (.*$)/gim, '• $1')
                              // Convert line breaks
                              .replace(/\n\n/g, '<br><br>')
                              .replace(/\n/g, '<br>')
                          }}
                        />
                      </Paper>
                      {result.result?.research_date && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Research Date: {new Date(result.result.research_date).toLocaleString()}
                        </Typography>
                      )}
                    </Box>
                  )}

                  {/* News Articles */}
                  {result.result?.articles && Array.isArray(result.result.articles) && result.result.articles.length > 0 && (
                    <>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                        News Articles ({result.result.articles.length})
                      </Typography>
                  <List>
                    {result.result.articles.map((article, index) => (
                      <React.Fragment key={index}>
                        <ListItem alignItems="flex-start">
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'primary.main' }}>
                              <NewsIcon />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, pr: 2 }}>
                                  {article.title}
                                </Typography>
                                {(article.link || article.url) && (
                                  <Button
                                    component="a"
                                    href={article.link || article.url || '#'}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    size="small"
                                    variant="outlined"
                                    sx={{ ml: 1, minWidth: 'auto', flexShrink: 0 }}
                                  >
                                    Read
                                  </Button>
                                )}
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                  {article.snippet || article.description || article.content?.substring(0, 200) + '...'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {article.source} • {article.date ? 
                                    new Date(article.date).toLocaleDateString() : 
                                    (article.publishedAt ? new Date(article.publishedAt).toLocaleDateString() : 'Invalid Date')
                                  }
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < (result.result?.articles?.length || 0) - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                    </>
                  )}

                  {/* Research Sources */}
                  {(result.result?.sources_used && Array.isArray(result.result.sources_used) && result.result.sources_used.length > 0) && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Research Sources ({result.result.total_sources || result.result.sources_used.length})
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {result.result.sources_used.map((source, index) => (
                          <Chip 
                            key={index}
                            label={source}
                            variant="outlined"
                            size="small"
                            onClick={() => copyToClipboard(source)}
                            sx={{ cursor: 'pointer' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                  {/* Fallback for legacy sources format */}
                  {(!result.result?.sources_used && result.result?.sources && Array.isArray(result.result.sources) && result.result.sources.length > 0) && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Sources
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {result.result.sources.map((source, index) => (
                          <Chip 
                            key={index}
                            label={source}
                            variant="outlined"
                            size="small"
                            onClick={() => copyToClipboard(source)}
                            sx={{ cursor: 'pointer' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              )}

              {/* AI Chat Response */}
              {result.result.content && (
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      AI Response
                    </Typography>
                    <Button
                      startIcon={<CopyIcon />}
                      onClick={() => copyToClipboard(result.result?.content || '')}
                      variant="outlined"
                      size="small"
                    >
                      Copy Response
                    </Button>
                  </Box>
                  
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 400, overflow: 'auto' }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.result.content}
                    </Typography>
                  </Paper>
                </Box>
              )}

              {/* Video Download */}
              {(result.result.final_video_url || result.result.video_url) && (
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      Generated Video
                    </Typography>
                    <Button
                      startIcon={<DownloadIcon />}
                      component="a"
                      href={result.result?.final_video_url || result.result?.video_url || '#'}
                      target="_blank"
                      variant="contained"
                      size="small"
                    >
                      Download
                    </Button>
                  </Box>
                  
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc', textAlign: 'center' }}>
                    <video
                      src={result.result.final_video_url || result.result.video_url}
                      controls
                      style={{
                        width: '100%',
                        maxHeight: '400px',
                        borderRadius: '8px'
                      }}
                    />
                  </Paper>
                </Box>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: { xs: 4, sm: 8 },
      px: { xs: 2, sm: 3 }
    }}>
      {/* Header */}
      <Box sx={{ mb: { xs: 3, sm: 4 } }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c',
            fontSize: { xs: '1.75rem', sm: '2rem', md: '2.125rem' }
          }}
        >
          AI Video Tools 🤖
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ 
            fontSize: { xs: '1rem', sm: '1.1rem' },
            lineHeight: 1.5
          }}
        >
          Advanced AI-powered tools for script generation, video search, scene creation, and content research.
        </Typography>
      </Box>

      {/* Main Content */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, flexGrow: 1 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ 
              px: { xs: 1.5, sm: 3 },
              '& .MuiTab-root': {
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                minWidth: { xs: 100, sm: 140 },
                py: { xs: 1, sm: 1.5 }
              }
            }}
          >
            <Tab icon={<AIIcon />} label="Script Generator" />
            <Tab icon={<SearchIcon />} label="Video Search" />
            <Tab icon={<ScenesIcon />} label="Scene Builder" />
            <Tab icon={<NewsIcon />} label="News Research" />
            <Tab icon={<ChatIcon />} label="AI Chat" />
          </Tabs>
        </Box>

        <Box sx={{ p: { xs: 2, sm: 3 } }}>
          {/* Script Generator Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} lg={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AIIcon color="primary" />
                      AI Script Generator
                    </Typography>

                    <Grid container spacing={{ xs: 2, sm: 3 }}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Topic"
                          placeholder="Enter your video topic or concept..."
                          value={scriptGenForm.topic}
                          onChange={(e) => setScriptGenForm({ ...scriptGenForm, topic: e.target.value })}
                          helperText="Describe what you want your video to be about"
                        />
                      </Grid>

                      <Grid item xs={12} lg={4}>
                        <FormControl fullWidth>
                          <InputLabel>Script Type</InputLabel>
                          <Select
                            value={scriptGenForm.script_type}
                            label="Script Type"
                            onChange={(e) => setScriptGenForm({ ...scriptGenForm, script_type: e.target.value })}
                          >
                            <MenuItem value="facts">Interesting Facts</MenuItem>
                            <MenuItem value="story">Storytelling</MenuItem>
                            <MenuItem value="educational">Educational</MenuItem>
                            <MenuItem value="motivation">Motivational</MenuItem>
                            <MenuItem value="pov">Point of View (POV)</MenuItem>
                            <MenuItem value="conspiracy">Mystery/Conspiracy</MenuItem>
                            <MenuItem value="life_hacks">Life Hacks</MenuItem>
                            <MenuItem value="would_you_rather">Would You Rather</MenuItem>
                            <MenuItem value="before_you_die">Before You Die</MenuItem>
                            <MenuItem value="dark_psychology">Psychology Insights</MenuItem>
                            <MenuItem value="reddit_stories">Personal Stories</MenuItem>
                            <MenuItem value="shower_thoughts">Shower Thoughts</MenuItem>
                            <MenuItem value="daily_news">Daily News</MenuItem>
                            <MenuItem value="prayer">Spiritual/Prayer</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} lg={4}>
                        <FormControl fullWidth>
                          <InputLabel>Language</InputLabel>
                          <Select
                            value={scriptGenForm.language}
                            label="Language"
                            onChange={(e) => setScriptGenForm({ ...scriptGenForm, language: e.target.value })}
                          >
                            <MenuItem value="en">English</MenuItem>
                            <MenuItem value="es">Spanish</MenuItem>
                            <MenuItem value="fr">French</MenuItem>
                            <MenuItem value="de">German</MenuItem>
                            <MenuItem value="it">Italian</MenuItem>
                            <MenuItem value="pt">Portuguese</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} lg={4}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Target Duration (seconds)"
                          value={scriptGenForm.target_duration}
                          onChange={(e) => setScriptGenForm({ ...scriptGenForm, target_duration: parseInt(e.target.value) })}
                          inputProps={{ min: 15, max: 300 }}
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.scriptgen ? <CircularProgress size={20} /> : <AIIcon />}
                      onClick={handleScriptGeneration}
                      disabled={loading.scriptgen || !scriptGenForm.topic.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.scriptgen ? 'Generating...' : 'Generate Script'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} lg={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Script Features
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="AI-Powered Content" variant="outlined" size="small" />
                      <Chip label="14 Script Types" variant="outlined" size="small" />
                      <Chip label="Optimized for TTS" variant="outlined" size="small" />
                      <Chip label="Viral Content Focus" variant="outlined" size="small" />
                      <Chip label="Duration Targeting" variant="outlined" size="small" />
                      <Chip label="POV & Psychology Content" variant="outlined" size="small" />
                      <Chip label="Multi-language Support" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('scriptgen', results.scriptgen, <AIIcon />)}
          </TabPanel>

          {/* Video Search Tab */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                AI-Powered Video Search
              </Typography>
              
              <Grid container spacing={{ xs: 2, sm: 3 }}>
                <Grid item xs={12} md={6}>
                  <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                    <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        Smart Search Queries
                      </Typography>
                      
                      <Grid container spacing={{ xs: 1.5, sm: 2 }}>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Topic"
                            placeholder="AI and machine learning"
                            value={videoSearchForm.topic}
                            onChange={(e) => setVideoSearchForm({ ...videoSearchForm, topic: e.target.value })}
                            helperText="AI will generate optimized search queries"
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth>
                            <InputLabel>Orientation</InputLabel>
                            <Select
                              value={videoSearchForm.orientation}
                              label="Orientation"
                              onChange={(e) => setVideoSearchForm({ ...videoSearchForm, orientation: e.target.value })}
                            >
                              <MenuItem value="portrait">Portrait (9:16)</MenuItem>
                              <MenuItem value="landscape">Landscape (16:9)</MenuItem>
                              <MenuItem value="square">Square (1:1)</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth>
                            <InputLabel>Language</InputLabel>
                            <Select
                              value={videoSearchForm.language}
                              label="Language"
                              onChange={(e) => setVideoSearchForm({ ...videoSearchForm, language: e.target.value })}
                            >
                              <MenuItem value="en">English</MenuItem>
                              <MenuItem value="es">Spanish</MenuItem>
                              <MenuItem value="fr">French</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>

                      <Button
                        variant="contained"
                        size="large"
                        startIcon={loading.videosearch ? <CircularProgress size={20} /> : <AIIcon />}
                        onClick={handleVideoSearch}
                        disabled={loading.videosearch || !videoSearchForm.topic.trim()}
                        sx={{ mt: 2, px: 3 }}
                      >
                        {loading.videosearch ? 'Searching...' : 'Generate Search Queries'}
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                    <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        Manual Video Browse
                      </Typography>
                      
                      <Grid container spacing={{ xs: 1.5, sm: 2 }}>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Search Query"
                            placeholder="technology, innovation, startup"
                            value={manualSearchForm.query}
                            onChange={(e) => setManualSearchForm({ ...manualSearchForm, query: e.target.value })}
                            helperText={`Direct search in ${manualSearchForm.provider.charAt(0).toUpperCase() + manualSearchForm.provider.slice(1)} database`}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth>
                            <InputLabel>Provider</InputLabel>
                            <Select
                              value={manualSearchForm.provider}
                              label="Provider"
                              onChange={(e) => setManualSearchForm({ ...manualSearchForm, provider: e.target.value })}
                            >
                              <MenuItem value="pexels">Pexels</MenuItem>
                              <MenuItem value="pixabay">Pixabay</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth>
                            <InputLabel>Orientation</InputLabel>
                            <Select
                              value={manualSearchForm.orientation}
                              label="Orientation"
                              onChange={(e) => setManualSearchForm({ ...manualSearchForm, orientation: e.target.value })}
                            >
                              <MenuItem value="landscape">Landscape</MenuItem>
                              <MenuItem value="portrait">Portrait</MenuItem>
                              <MenuItem value="square">Square</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Results Per Page"
                            value={manualSearchForm.per_page}
                            onChange={(e) => setManualSearchForm({ ...manualSearchForm, per_page: parseInt(e.target.value) })}
                            inputProps={{ min: 5, max: 80 }}
                          />
                        </Grid>
                      </Grid>

                      <Button
                        variant="outlined"
                        size="large"
                        startIcon={loading.manualsearch ? <CircularProgress size={20} /> : <SearchIcon />}
                        onClick={handleManualVideoSearch}
                        disabled={loading.manualsearch || !manualSearchForm.query.trim()}
                        sx={{ mt: 2, px: 3 }}
                      >
                        {loading.manualsearch ? 'Searching...' : 'Browse Videos'}
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>

            {renderJobResult('videosearch', results.videosearch, <SearchIcon />)}
            {renderJobResult('manualsearch', results.manualsearch, <VideoIcon />)}
          </TabPanel>

          {/* Scene Builder Tab */}
          <TabPanel value={tabValue} index={2}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ScenesIcon color="primary" />
                  Scene-Based Video Creator
                </Typography>

                <Grid container spacing={{ xs: 2, sm: 3 }}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                      Video Scenes
                    </Typography>
                    {scenesForm.scenes.map((scene, sceneIndex) => (
                      <Accordion key={sceneIndex} sx={{ mb: 2 }}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle2">
                            Scene {sceneIndex + 1} {scene.text && `- "${scene.text.slice(0, 30)}..."`}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={{ xs: 1.5, sm: 2 }}>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                multiline
                                rows={3}
                                label="Scene Text"
                                placeholder="Enter the narration text for this scene..."
                                value={scene.text}
                                onChange={(e) => updateScene(sceneIndex, 'text', e.target.value)}
                              />
                            </Grid>

                            <Grid item xs={12} lg={8}>
                              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                Search Terms for Background Video
                              </Typography>
                              {scene.searchTerms.map((term, termIndex) => (
                                <Box key={termIndex} sx={{ display: 'flex', gap: 1, mb: 1 }}>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    placeholder="e.g., technology, innovation"
                                    value={term}
                                    onChange={(e) => updateSearchTerm(sceneIndex, termIndex, e.target.value)}
                                  />
                                  {scene.searchTerms.length > 1 && (
                                    <Button 
                                      variant="outlined" 
                                      color="error"
                                      size="small"
                                      onClick={() => removeSearchTerm(sceneIndex, termIndex)}
                                      sx={{ minWidth: 'auto', px: 1 }}
                                    >
                                      ✕
                                    </Button>
                                  )}
                                </Box>
                              ))}
                              <Button 
                                variant="outlined" 
                                size="small"
                                onClick={() => addSearchTerm(sceneIndex)}
                                sx={{ mt: 1 }}
                              >
                                + Add Search Term
                              </Button>
                            </Grid>

                            <Grid item xs={12} lg={4}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Duration (seconds)"
                                value={scene.duration}
                                onChange={(e) => updateScene(sceneIndex, 'duration', parseFloat(e.target.value))}
                                inputProps={{ min: 1, max: 30, step: 0.5 }}
                              />
                            </Grid>

                            {scenesForm.scenes.length > 1 && (
                              <Grid item xs={12}>
                                <Button 
                                  variant="outlined" 
                                  color="error"
                                  onClick={() => removeScene(sceneIndex)}
                                >
                                  Remove Scene
                                </Button>
                              </Grid>
                            )}
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                    
                    <Button 
                      variant="outlined" 
                      onClick={addScene}
                      sx={{ mt: 1 }}
                    >
                      + Add Scene
                    </Button>
                  </Grid>

                  <Grid item xs={12} lg={4}>
                    <FormControl fullWidth>
                      <InputLabel>Voice Provider</InputLabel>
                      <Select
                        value={scenesForm.voice_provider}
                        label="Voice Provider"
                        onChange={(e) => setScenesForm({ ...scenesForm, voice_provider: e.target.value })}
                      >
                        <MenuItem value="kokoro">Kokoro TTS</MenuItem>
                        <MenuItem value="edge">Edge TTS</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} lg={4}>
                    <TextField
                      fullWidth
                      label="Voice Name"
                      value={scenesForm.voice_name}
                      onChange={(e) => setScenesForm({ ...scenesForm, voice_name: e.target.value })}
                      placeholder="af_bella, en_male_1, etc."
                    />
                  </Grid>

                  <Grid item xs={12} lg={4}>
                    <FormControl fullWidth>
                      <InputLabel>Resolution</InputLabel>
                      <Select
                        value={scenesForm.resolution}
                        label="Resolution"
                        onChange={(e) => setScenesForm({ ...scenesForm, resolution: e.target.value })}
                      >
                        <MenuItem value="1080x1920">1080x1920 (Portrait)</MenuItem>
                        <MenuItem value="1920x1080">1920x1080 (Landscape)</MenuItem>
                        <MenuItem value="1080x1080">1080x1080 (Square)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading.scenes ? <CircularProgress size={20} /> : <ScenesIcon />}
                  onClick={handleScenesVideo}
                  disabled={loading.scenes || scenesForm.scenes.every(s => !s.text.trim())}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading.scenes ? 'Creating Video...' : 'Create Video from Scenes'}
                </Button>
              </CardContent>
            </Card>

            {renderJobResult('scenes', results.scenes, <ScenesIcon />)}
          </TabPanel>

          {/* News Research Tab */}
          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} lg={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <NewsIcon color="primary" />
                      News Research
                    </Typography>

                    <Grid container spacing={{ xs: 2, sm: 3 }}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Search Term"
                          placeholder="artificial intelligence, climate change, technology trends..."
                          value={newsResearchForm.searchTerm}
                          onChange={(e) => setNewsResearchForm({ ...newsResearchForm, searchTerm: e.target.value })}
                          helperText="Enter topics to research current news and trends"
                        />
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel>Language</InputLabel>
                          <Select
                            value={newsResearchForm.targetLanguage}
                            label="Language"
                            onChange={(e) => setNewsResearchForm({ ...newsResearchForm, targetLanguage: e.target.value })}
                          >
                            <MenuItem value="en">English</MenuItem>
                            <MenuItem value="es">Spanish</MenuItem>
                            <MenuItem value="fr">French</MenuItem>
                            <MenuItem value="de">German</MenuItem>
                            <MenuItem value="it">Italian</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Max Results"
                          value={newsResearchForm.maxResults}
                          onChange={(e) => setNewsResearchForm({ ...newsResearchForm, maxResults: parseInt(e.target.value) })}
                          inputProps={{ min: 1, max: 20 }}
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.news ? <CircularProgress size={20} /> : <NewsIcon />}
                      onClick={handleNewsResearch}
                      disabled={loading.news || !newsResearchForm.searchTerm.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.news ? 'Researching...' : 'Research News'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} lg={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Research Features
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="Real-time News" variant="outlined" size="small" />
                      <Chip label="Multiple Sources" variant="outlined" size="small" />
                      <Chip label="Content Ideas" variant="outlined" size="small" />
                      <Chip label="Trending Topics" variant="outlined" size="small" />
                      <Chip label="Multi-language" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('news', results.news, <NewsIcon />)}
          </TabPanel>

          {/* AI Chat Tab */}
          <TabPanel value={tabValue} index={4}>
            <Grid container spacing={{ xs: 2, sm: 3 }}>
              <Grid item xs={12} lg={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ChatIcon color="primary" />
                      AI Assistant
                    </Typography>

                    <Grid container spacing={{ xs: 2, sm: 3 }}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={4}
                          label="Prompt"
                          placeholder="Ask anything about content creation, video ideas, script improvements..."
                          value={aiChatForm.prompt}
                          onChange={(e) => setAiChatForm({ ...aiChatForm, prompt: e.target.value })}
                          helperText="Get AI assistance for creative ideas, content planning, and more"
                        />
                      </Grid>

                      <Grid item xs={12} lg={4}>
                        <FormControl fullWidth>
                          <InputLabel>AI Provider</InputLabel>
                          <Select
                            value={aiChatForm.provider}
                            label="AI Provider"
                            onChange={(e) => setAiChatForm({ ...aiChatForm, provider: e.target.value })}
                          >
                            <MenuItem value="auto">Auto (Best Available)</MenuItem>
                            <MenuItem value="openai">OpenAI GPT</MenuItem>
                            <MenuItem value="groq">Groq Llama</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} lg={4}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Max Tokens"
                          value={aiChatForm.max_tokens}
                          onChange={(e) => setAiChatForm({ ...aiChatForm, max_tokens: parseInt(e.target.value) })}
                          inputProps={{ min: 100, max: 4000 }}
                        />
                      </Grid>

                      <Grid item xs={12} lg={4}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Temperature"
                          value={aiChatForm.temperature}
                          onChange={(e) => setAiChatForm({ ...aiChatForm, temperature: parseFloat(e.target.value) })}
                          inputProps={{ min: 0, max: 2, step: 0.1 }}
                          helperText="0=Focused, 1=Creative"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.aichat ? <CircularProgress size={20} /> : <ChatIcon />}
                      onClick={handleAIChat}
                      disabled={loading.aichat || !aiChatForm.prompt.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.aichat ? 'Thinking...' : 'Ask AI'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} lg={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      AI Capabilities
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="Content Planning" variant="outlined" size="small" />
                      <Chip label="Script Enhancement" variant="outlined" size="small" />
                      <Chip label="Creative Ideas" variant="outlined" size="small" />
                      <Chip label="SEO Optimization" variant="outlined" size="small" />
                      <Chip label="Trend Analysis" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('aichat', results.aichat, <ChatIcon />)}
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default AIVideoTools;