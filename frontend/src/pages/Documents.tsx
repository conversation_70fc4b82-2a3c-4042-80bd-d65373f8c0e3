import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControlLabel,
  Switch,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Upload as UploadIcon,
  Link as LinkIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  Check as CheckIcon,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  Article as WordIcon,
  TableChart as ExcelIcon,
  Image as ImageIcon,
  Code as CodeIcon,
  Psychology as ExtractIcon,
  Schema as SchemaIcon,
  ExpandMore as ExpandMoreIcon,
  AutoAwesome as MarkerIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { directApi } from '../utils/api';

// Type definitions for document processing API
interface DocumentResult {
  markdown_content: string;
  original_filename: string;
  file_type: string;
  word_count: number;
  character_count: number;
  processing_time: number;
}


interface DocumentConversionResult {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: DocumentResult;
  error?: string | null;
}

// Type definitions for Langextract API
interface ExtractedEntity {
  text: string;
  value: string; // Keep for backwards compatibility
  sources: {
    start: number;
    end: number;
    text: string;
  }[];
  confidence_score?: number;
  attributes?: Record<string, string | number | boolean>;
  context?: string;
  type?: string;
  role?: string;
}

interface LangextractResult {
  extracted_data: Record<string, ExtractedEntity[]>;
  total_extractions: number;
  processing_time: number;
  model_used: string;
  input_text_length: number;
  extraction_type?: string;
  source_grounding_enabled?: boolean;
  extraction_config?: {
    entity_types: string[];
    has_attributes: boolean;
    extraction_passes: number;
    max_workers: number;
    temperature: number;
  };
  quality_metrics?: {
    entities_with_attributes: number;
    average_confidence: number;
    unique_entity_types: number;
    processing_speed_chars_per_sec: number;
  };
}

interface LangextractConversionResult {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: LangextractResult;
  error?: string | null;
}

// Type definitions for Marker API
interface MarkerMetadata {
  file_size?: number;
  page_count?: number;
  creation_date?: string;
  modification_date?: string;
  author?: string;
  title?: string;
  subject?: string;
  language?: string;
  [key: string]: string | number | boolean | undefined;
}

interface MarkerProcessingSettings {
  force_ocr?: boolean;
  preserve_images?: boolean;
  use_llm?: boolean;
  paginate_output?: boolean;
  llm_service?: string;
  output_format?: string;
  [key: string]: string | number | boolean | undefined;
}

interface MarkerResult {
  content: string;
  content_url: string;
  original_filename: string;
  output_filename: string;
  output_format: string;
  word_count: number;
  character_count: number;
  image_count: number;
  image_urls: Record<string, string>;
  metadata: MarkerMetadata;
  processing_settings: MarkerProcessingSettings;
}

interface MarkerConversionResult {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: MarkerResult;
  error?: string | null;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`document-tabpanel-${index}`}
    aria-labelledby={`document-tab-${index}`}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);


const Documents: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<DocumentConversionResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [jobProgress, setJobProgress] = useState<string>('');
  const [pollingJobId, setPollingJobId] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  // Langextract state
  const [extractLoading, setExtractLoading] = useState(false);
  const [extractResult, setExtractResult] = useState<LangextractConversionResult | null>(null);
  const [extractError, setExtractError] = useState<string | null>(null);
  const [extractJobStatus, setExtractJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [extractJobProgress, setExtractJobProgress] = useState<string>('');
  const [extractPollingJobId, setExtractPollingJobId] = useState<string | null>(null);

  // Marker state
  const [markerLoading, setMarkerLoading] = useState(false);
  const [markerResult, setMarkerResult] = useState<MarkerConversionResult | null>(null);
  const [markerError, setMarkerError] = useState<string | null>(null);
  const [markerJobStatus, setMarkerJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [markerJobProgress, setMarkerJobProgress] = useState<string>('');
  const [markerPollingJobId, setMarkerPollingJobId] = useState<string | null>(null);

  // Form states
  const [urlForm, setUrlForm] = useState({
    url: '',
    includeMetadata: true,
    preserveFormatting: true
  });

  const [fileForm, setFileForm] = useState({
    file: null as File | null,
    includeMetadata: true,
    preserveFormatting: true
  });

  // Langextract form states
  const [extractForm, setExtractForm] = useState({
    inputText: '',
    inputUrl: '',
    inputFile: null as File | null,
    extractionSchema: '{"entities": ["person", "organization", "location"], "relationships": ["works_for", "located_in"]}',
    extractionPrompt: 'Extract all people, organizations, and locations from the text. Also identify relationships between entities.',
    useCustomPrompt: false,
    model: 'gemini'
  });

  // Marker form states
  const [markerForm, setMarkerForm] = useState({
    inputFile: null as File | null,
    inputUrl: '',
    outputFormat: 'markdown',
    forceOcr: false,
    preserveImages: true,
    useLlm: false,
    paginateOutput: false,
    llmService: 'openai'  // Default to OpenAI since you have API key configured
  });


  // Get file icon based on extension
  const getFileIcon = (filename?: string) => {
    if (!filename) return <DocumentIcon />;
    
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'pdf':
        return <PdfIcon color="error" />;
      case 'docx':
      case 'doc':
        return <WordIcon color="primary" />;
      case 'xlsx':
      case 'xls':
        return <ExcelIcon color="success" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
        return <ImageIcon color="secondary" />;
      default:
        return <DocumentIcon />;
    }
  };

  // Job status polling function
  const pollJobStatus = async (jobId: string) => {
    const maxAttempts = 120; // 10 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.getLongTimeout(`/api/v1/documents/to-markdown/${jobId}`, 120000); // 2 minutes timeout per request

        // Handle array response format
        const responseData = Array.isArray(statusResponse.data) ? statusResponse.data[0] : statusResponse.data;
        const status = responseData.status;
        const jobResult = responseData.result;
        const jobError = responseData.error;

        setJobStatus(status);

        if (status === 'completed') {
          setJobProgress('Document conversion completed successfully!');
          setResult({
            job_id: jobId,
            status: 'completed',
            result: jobResult,
            error: null
          });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Document conversion failed');
          setError(jobError || 'Document conversion failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing document... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  // Marker job status polling function
  const pollMarkerJobStatus = async (jobId: string) => {
    const maxAttempts = 300; // 25 minutes max (Marker needs time for model downloads and processing)
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.getLongTimeout(`/api/v1/documents/marker/${jobId}`, 120000); // 2 minutes timeout per request

        const responseData = Array.isArray(statusResponse.data) ? statusResponse.data[0] : statusResponse.data;
        const status = responseData.status;
        const jobResult = responseData.result;
        const jobError = responseData.error;

        setMarkerJobStatus(status);

        if (status === 'completed') {
          setMarkerJobProgress('Document conversion completed successfully!');
          setMarkerResult({
            job_id: jobId,
            status: 'completed',
            result: jobResult,
            error: null
          });
          setMarkerLoading(false);
          return;
        } else if (status === 'failed') {
          setMarkerJobProgress('Document conversion failed');
          setMarkerError(jobError || 'Document conversion failed');
          setMarkerLoading(false);
          return;
        } else if (status === 'processing') {
          setMarkerJobProgress(`Processing document with Marker... (${attempts}/${maxAttempts})`);
        } else {
          setMarkerJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setMarkerError('Job polling timeout. Please check status manually.');
          setMarkerLoading(false);
        }
      } catch (err) {
        console.error('Marker polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setMarkerError('Failed to check job status');
          setMarkerLoading(false);
        }
      }
    };

    poll();
  };

  const handleUrlSubmit = async () => {
    if (!urlForm.url.trim()) {
      setError('Document URL is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('url', urlForm.url);
      formData.append('include_metadata', urlForm.includeMetadata.toString());
      formData.append('preserve_formatting', urlForm.preserveFormatting.toString());

      // Use axios directly with proper headers for FormData
      const apiKey = localStorage.getItem('ouinhi_api_key');
      const response = await fetch('/api/v1/documents/to-markdown', {
        method: 'POST',
        headers: {
          'X-API-Key': apiKey || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.job_id) {
        setResult({ job_id: data.job_id, status: 'pending' });
        setPollingJobId(data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting document conversion...');
        pollJobStatus(data.job_id);
      } else {
        setError('Failed to start document conversion');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSubmit = async () => {
    if (!fileForm.file) {
      setError('Please select a file to upload');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', fileForm.file);
      formData.append('include_metadata', fileForm.includeMetadata.toString());
      formData.append('preserve_formatting', fileForm.preserveFormatting.toString());

      // Use fetch directly with proper headers for FormData
      const apiKey = localStorage.getItem('ouinhi_api_key');
      const response = await fetch('/api/v1/documents/to-markdown', {
        method: 'POST',
        headers: {
          'X-API-Key': apiKey || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.job_id) {
        setResult({ job_id: data.job_id, status: 'pending' });
        setPollingJobId(data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting document conversion...');
        pollJobStatus(data.job_id);
      } else {
        setError('Failed to start document conversion');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileForm(prev => ({ ...prev, file }));
    }
  };

  // Langextract job status polling function
  const pollExtractJobStatus = async (jobId: string) => {
    const maxAttempts = 120; // 10 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.getLongTimeout(`/api/v1/documents/langextract/${jobId}`, 120000); // 2 minutes timeout per request

        const responseData = Array.isArray(statusResponse.data) ? statusResponse.data[0] : statusResponse.data;
        const status = responseData.status;
        const jobResult = responseData.result;
        const jobError = responseData.error;

        setExtractJobStatus(status);

        if (status === 'completed') {
          setExtractJobProgress('Data extraction completed successfully!');
          setExtractResult({
            job_id: jobId,
            status: 'completed',
            result: jobResult,
            error: null
          });
          setExtractLoading(false);
          return;
        } else if (status === 'failed') {
          setExtractJobProgress('Data extraction failed');
          setExtractError(jobError || 'Data extraction failed');
          setExtractLoading(false);
          return;
        } else if (status === 'processing') {
          setExtractJobProgress(`Extracting structured data... (${attempts}/${maxAttempts})`);
        } else {
          setExtractJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setExtractError('Job polling timeout. Please check status manually.');
          setExtractLoading(false);
        }
      } catch (err) {
        console.error('Extract polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setExtractError('Failed to check job status');
          setExtractLoading(false);
        }
      }
    };

    poll();
  };

  const handleExtractSubmit = async () => {
    // Validate input
    if (!extractForm.inputText.trim() && !extractForm.inputUrl.trim() && !extractForm.inputFile) {
      setExtractError('Please provide text, URL, or file for data extraction');
      return;
    }

    setExtractLoading(true);
    setExtractError(null);
    setExtractResult(null);

    try {
      const formData = new FormData();
      
      // Add input data
      if (extractForm.inputText.trim()) {
        formData.append('input_text', extractForm.inputText);
      } else if (extractForm.inputUrl.trim()) {
        formData.append('file_url', extractForm.inputUrl);
      } else if (extractForm.inputFile) {
        formData.append('file', extractForm.inputFile);
      }

      // Add extraction configuration
      formData.append('extraction_schema', extractForm.extractionSchema);
      formData.append('extraction_prompt', extractForm.extractionPrompt);
      formData.append('use_custom_prompt', extractForm.useCustomPrompt.toString());
      formData.append('model', extractForm.model);

      const apiKey = localStorage.getItem('ouinhi_api_key');
      const response = await fetch('/api/v1/documents/langextract', {
        method: 'POST',
        headers: {
          'X-API-Key': apiKey || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.job_id) {
        setExtractResult({ job_id: data.job_id, status: 'pending' });
        setExtractPollingJobId(data.job_id);
        setExtractJobStatus('pending');
        setExtractJobProgress('Job created, starting data extraction...');
        pollExtractJobStatus(data.job_id);
      } else {
        setExtractError('Failed to start data extraction');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setExtractError(errorMessage);
    } finally {
      setExtractLoading(false);
    }
  };

  const handleMarkerSubmit = async () => {
    // Validate input
    if (!markerForm.inputFile && !markerForm.inputUrl.trim()) {
      setMarkerError('Please provide a file upload or URL for document conversion');
      return;
    }

    setMarkerLoading(true);
    setMarkerError(null);
    setMarkerResult(null);

    try {
      const formData = new FormData();
      
      // Add input data
      if (markerForm.inputFile) {
        formData.append('file', markerForm.inputFile);
      } else if (markerForm.inputUrl.trim()) {
        formData.append('url', markerForm.inputUrl);
      }

      // Add Marker configuration
      formData.append('output_format', markerForm.outputFormat);
      formData.append('force_ocr', markerForm.forceOcr.toString());
      formData.append('preserve_images', markerForm.preserveImages.toString());
      formData.append('use_llm', markerForm.useLlm.toString());
      formData.append('paginate_output', markerForm.paginateOutput.toString());
      
      if (markerForm.useLlm && markerForm.llmService) {
        formData.append('llm_service', markerForm.llmService);
      }

      const apiKey = localStorage.getItem('ouinhi_api_key');
      const response = await fetch('/api/v1/documents/marker/convert', {
        method: 'POST',
        headers: {
          'X-API-Key': apiKey || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.job_id) {
        setMarkerResult({ job_id: data.job_id, status: 'pending' });
        setMarkerPollingJobId(data.job_id);
        setMarkerJobStatus('pending');
        setMarkerJobProgress('Job created, starting document conversion with Marker...');
        pollMarkerJobStatus(data.job_id);
      } else {
        setMarkerError('Failed to start document conversion');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setMarkerError(errorMessage);
    } finally {
      setMarkerLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const downloadMarkdown = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Document Processor 📄
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Convert PDF, Word, Excel, and other documents to clean Markdown format.
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          aria-label="document processing tabs"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Convert to Markdown" icon={<DocumentIcon />} id="document-tab-0" />
          <Tab label="Extract Structured Data" icon={<ExtractIcon />} id="document-tab-1" />
          <Tab label="Marker Professional" icon={<MarkerIcon />} id="document-tab-2" />
        </Tabs>

        {/* Document Conversion Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3 }}>
                    Convert Document to Markdown
                  </Typography>

                  <Grid container spacing={3}>
                    {/* URL Input */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Document URL (Optional)"
                        placeholder="https://example.com/document.pdf"
                        value={urlForm.url}
                        onChange={(e) => setUrlForm(prev => ({ ...prev, url: e.target.value }))}
                        helperText="Enter a URL to convert a document from the web"
                      />
                    </Grid>

                    {/* Divider */}
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, my: 1 }}>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                        <Typography variant="body2" color="text.secondary" sx={{ px: 2 }}>
                          OR
                        </Typography>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                      </Box>
                    </Grid>

                    {/* File Upload */}
                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        component="label"
                        fullWidth
                        startIcon={<UploadIcon />}
                        sx={{ mb: 2, py: 2 }}
                      >
                        {fileForm.file ? fileForm.file.name : 'Choose File to Upload'}
                        <input
                          type="file"
                          hidden
                          accept=".pdf,.docx,.doc,.pptx,.ppt,.xlsx,.xls,.txt,.html,.htm"
                          onChange={handleFileChange}
                        />
                      </Button>
                      {fileForm.file && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          {getFileIcon(fileForm.file.name)}
                          <Typography variant="body2">
                            {fileForm.file.name} ({(fileForm.file.size / 1024 / 1024).toFixed(2)} MB)
                          </Typography>
                        </Box>
                      )}
                      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        Supported formats: PDF, DOCX, DOC, PPTX, PPT, XLSX, XLS, TXT, HTML
                      </Typography>
                    </Grid>

                    {/* Settings */}
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={urlForm.includeMetadata}
                            onChange={(e) => {
                              setUrlForm(prev => ({ ...prev, includeMetadata: e.target.checked }));
                              setFileForm(prev => ({ ...prev, includeMetadata: e.target.checked }));
                            }}
                          />
                        }
                        label="Include Metadata"
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={urlForm.preserveFormatting}
                            onChange={(e) => {
                              setUrlForm(prev => ({ ...prev, preserveFormatting: e.target.checked }));
                              setFileForm(prev => ({ ...prev, preserveFormatting: e.target.checked }));
                            }}
                          />
                        }
                        label="Preserve Formatting"
                      />
                    </Grid>

                    {/* Convert Button */}
                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={loading ? <CircularProgress size={20} /> : 
                          (fileForm.file ? <UploadIcon /> : <LinkIcon />)}
                        onClick={fileForm.file ? handleFileSubmit : handleUrlSubmit}
                        disabled={loading || (!urlForm.url.trim() && !fileForm.file)}
                        sx={{ px: 4 }}
                      >
                        {loading ? 'Converting...' : 
                          fileForm.file ? 'Upload & Convert' : 'Convert from URL'}
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Quick Examples
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Try these example documents:
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 3 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setUrlForm(prev => ({ ...prev, url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Sample PDF
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setUrlForm(prev => ({ ...prev, url: 'https://file-examples.com/wp-content/storage/2017/02/file-sample_100kB.docx' }))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Sample DOCX
                    </Button>
                  </Box>

                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Supported Formats
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Chip icon={<PdfIcon />} label="PDF" color="error" variant="outlined" size="small" />
                    <Chip icon={<WordIcon />} label="Word (DOCX/DOC)" color="primary" variant="outlined" size="small" />
                    <Chip icon={<ExcelIcon />} label="Excel (XLSX/XLS)" color="success" variant="outlined" size="small" />
                    <Chip icon={<DocumentIcon />} label="PowerPoint" color="secondary" variant="outlined" size="small" />
                    <Chip icon={<CodeIcon />} label="Text/HTML" color="info" variant="outlined" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Langextract Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ExtractIcon />
                    Extract Structured Data with AI
                  </Typography>

                  <Grid container spacing={3}>
                    {/* Text Input */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        label="Input Text (Optional)"
                        placeholder="Paste your text here to extract structured information..."
                        value={extractForm.inputText}
                        onChange={(e) => setExtractForm(prev => ({ ...prev, inputText: e.target.value }))}
                        helperText="Enter text directly for immediate processing"
                      />
                    </Grid>

                    {/* Divider */}
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, my: 1 }}>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                        <Typography variant="body2" color="text.secondary" sx={{ px: 2 }}>
                          OR
                        </Typography>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                      </Box>
                    </Grid>

                    {/* URL Input */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Document URL (Optional)"
                        placeholder="https://example.com/document.pdf"
                        value={extractForm.inputUrl}
                        onChange={(e) => setExtractForm(prev => ({ ...prev, inputUrl: e.target.value }))}
                        helperText="Extract data from a web document"
                      />
                    </Grid>

                    {/* Divider */}
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, my: 1 }}>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                        <Typography variant="body2" color="text.secondary" sx={{ px: 2 }}>
                          OR
                        </Typography>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                      </Box>
                    </Grid>

                    {/* File Upload */}
                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        component="label"
                        fullWidth
                        startIcon={<UploadIcon />}
                        sx={{ mb: 2, py: 2 }}
                      >
                        {extractForm.inputFile ? extractForm.inputFile.name : 'Choose File for Data Extraction'}
                        <input
                          type="file"
                          hidden
                          accept=".pdf,.docx,.doc,.txt,.html,.htm"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setExtractForm(prev => ({ ...prev, inputFile: file }));
                            }
                          }}
                        />
                      </Button>
                      {extractForm.inputFile && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          {getFileIcon(extractForm.inputFile.name)}
                          <Typography variant="body2">
                            {extractForm.inputFile.name} ({(extractForm.inputFile.size / 1024 / 1024).toFixed(2)} MB)
                          </Typography>
                        </Box>
                      )}
                    </Grid>

                    {/* Extraction Configuration */}
                    <Grid item xs={12}>
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <SchemaIcon />
                            Extraction Configuration
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={extractForm.useCustomPrompt}
                                    onChange={(e) => setExtractForm(prev => ({ ...prev, useCustomPrompt: e.target.checked }))}
                                  />
                                }
                                label="Use Custom Extraction Prompt"
                              />
                            </Grid>

                            {extractForm.useCustomPrompt ? (
                              <Grid item xs={12}>
                                <TextField
                                  fullWidth
                                  multiline
                                  rows={3}
                                  label="Custom Extraction Prompt"
                                  value={extractForm.extractionPrompt}
                                  onChange={(e) => setExtractForm(prev => ({ ...prev, extractionPrompt: e.target.value }))}
                                  helperText="Describe what specific information you want to extract"
                                />
                              </Grid>
                            ) : (
                              <Grid item xs={12}>
                                <TextField
                                  fullWidth
                                  multiline
                                  rows={3}
                                  label="JSON Schema (Optional)"
                                  value={extractForm.extractionSchema}
                                  onChange={(e) => setExtractForm(prev => ({ ...prev, extractionSchema: e.target.value }))}
                                  helperText='Define structure like: {"entities": ["person", "organization"], "attributes": ["name", "title"]}'
                                />
                              </Grid>
                            )}
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    </Grid>

                    {/* Extract Button */}
                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={extractLoading ? <CircularProgress size={20} /> : <ExtractIcon />}
                        onClick={handleExtractSubmit}
                        disabled={extractLoading || (!extractForm.inputText.trim() && !extractForm.inputUrl.trim() && !extractForm.inputFile)}
                        sx={{ px: 4 }}
                      >
                        {extractLoading ? 'Extracting...' : 'Extract Structured Data'}
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    🧠 What is Langextract?
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Google's Langextract extracts structured information from unstructured text using AI:
                  </Typography>
                  <Box sx={{ mb: 3 }}>
                    <Chip label="Named Entities" size="small" color="primary" sx={{ m: 0.5 }} />
                    <Chip label="Relationships" size="small" color="secondary" sx={{ m: 0.5 }} />
                    <Chip label="Attributes" size="small" color="success" sx={{ m: 0.5 }} />
                    <Chip label="Source Grounding" size="small" color="info" sx={{ m: 0.5 }} />
                  </Box>

                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Example Use Cases
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 3 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setExtractForm(prev => ({
                        ...prev,
                        inputText: 'John Smith works as a Senior Engineer at Google Inc. in Mountain View, California. He previously worked at Apple Inc.',
                        extractionSchema: '{"entities": ["person", "organization", "location", "job_title"], "relationships": ["works_at", "previously_worked_at"]}'
                      }))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      📄 Resume Analysis
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setExtractForm(prev => ({
                        ...prev,
                        inputText: 'Apple Inc. reported Q4 revenue of $94.9B, up 6% YoY. iPhone sales were $39.3B, while Services reached $22.3B.',
                        extractionSchema: '{"entities": ["company", "financial_metrics", "products"], "attributes": ["revenue", "growth_rate", "time_period"]}'
                      }))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      📊 Financial Reports
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setExtractForm(prev => ({
                        ...prev,
                        inputText: 'Dr. Sarah Johnson published a paper on machine learning in Nature journal. She collaborated with researchers from Stanford University and MIT.',
                        extractionSchema: '{"entities": ["person", "publication", "institution"], "relationships": ["published", "collaborated_with"]}'
                      }))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      🔬 Research Papers
                    </Button>
                  </Box>

                  <Typography variant="h6" sx={{ mb: 2 }}>
                    AI Models
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Chip icon={<ExtractIcon />} label="Gemini (Primary)" color="primary" variant="outlined" size="small" />
                    <Chip icon={<ExtractIcon />} label="OpenAI (Fallback)" color="secondary" variant="outlined" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Marker Professional Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MarkerIcon />
                    Professional Document Processing with Marker
                  </Typography>

                  <Grid container spacing={3}>
                    {/* URL Input */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Document URL (Optional)"
                        placeholder="https://example.com/document.pdf"
                        value={markerForm.inputUrl}
                        onChange={(e) => setMarkerForm(prev => ({ ...prev, inputUrl: e.target.value }))}
                        helperText="Enter a URL to convert a document from the web"
                      />
                    </Grid>

                    {/* Divider */}
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, my: 1 }}>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                        <Typography variant="body2" color="text.secondary" sx={{ px: 2 }}>
                          OR
                        </Typography>
                        <Box sx={{ flex: 1, height: 1, bgcolor: 'divider' }} />
                      </Box>
                    </Grid>

                    {/* File Upload */}
                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        component="label"
                        fullWidth
                        startIcon={<UploadIcon />}
                        sx={{ mb: 2, py: 2 }}
                      >
                        {markerForm.inputFile ? markerForm.inputFile.name : 'Choose File for Professional Processing'}
                        <input
                          type="file"
                          hidden
                          accept=".pdf,.docx,.doc,.pptx,.ppt,.xlsx,.xls,.png,.jpg,.jpeg,.gif,.bmp,.tiff,.html,.htm,.epub,.txt"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setMarkerForm(prev => ({ ...prev, inputFile: file }));
                            }
                          }}
                        />
                      </Button>
                      {markerForm.inputFile && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          {getFileIcon(markerForm.inputFile.name)}
                          <Typography variant="body2">
                            {markerForm.inputFile.name} ({(markerForm.inputFile.size / 1024 / 1024).toFixed(2)} MB)
                          </Typography>
                        </Box>
                      )}
                      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        Supports: PDF, DOCX, PPTX, XLSX, images, HTML, EPUB, TXT
                      </Typography>
                    </Grid>

                    {/* Advanced Configuration */}
                    <Grid item xs={12}>
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <SettingsIcon />
                            Advanced Processing Options
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                select
                                label="Output Format"
                                value={markerForm.outputFormat}
                                onChange={(e) => setMarkerForm(prev => ({ ...prev, outputFormat: e.target.value }))}
                                SelectProps={{ native: true }}
                              >
                                <option value="markdown">Markdown</option>
                                <option value="json">JSON</option>
                                <option value="html">HTML</option>
                                <option value="chunks">Chunks</option>
                              </TextField>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                select
                                label="LLM Service (when enabled)"
                                value={markerForm.llmService}
                                onChange={(e) => setMarkerForm(prev => ({ ...prev, llmService: e.target.value }))}
                                SelectProps={{ native: true }}
                                disabled={!markerForm.useLlm}
                                helperText={markerForm.useLlm ? "Enhanced accuracy with AI" : "Enable LLM Enhancement first"}
                              >
                                <option value="openai">OpenAI</option>
                                <option value="gemini">Gemini</option>
                              </TextField>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={markerForm.forceOcr}
                                    onChange={(e) => setMarkerForm(prev => ({ ...prev, forceOcr: e.target.checked }))}
                                  />
                                }
                                label="Force OCR"
                              />
                              <Typography variant="caption" display="block" color="text.secondary">
                                Force OCR on all text for best quality
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={markerForm.preserveImages}
                                    onChange={(e) => setMarkerForm(prev => ({ ...prev, preserveImages: e.target.checked }))}
                                  />
                                }
                                label="Extract Images"
                              />
                              <Typography variant="caption" display="block" color="text.secondary">
                                Extract and save images from document
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={markerForm.useLlm}
                                    onChange={(e) => setMarkerForm(prev => ({ ...prev, useLlm: e.target.checked }))}
                                  />
                                }
                                label="Use LLM Enhancement"
                              />
                              <Typography variant="caption" display="block" color="text.secondary">
                                Use AI for highest quality results
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={markerForm.paginateOutput}
                                    onChange={(e) => setMarkerForm(prev => ({ ...prev, paginateOutput: e.target.checked }))}
                                  />
                                }
                                label="Paginate Output"
                              />
                              <Typography variant="caption" display="block" color="text.secondary">
                                Add page breaks to output
                              </Typography>
                            </Grid>
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    </Grid>

                    {/* Convert Button */}
                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={markerLoading ? <CircularProgress size={20} /> : <MarkerIcon />}
                        onClick={handleMarkerSubmit}
                        disabled={markerLoading || (!markerForm.inputFile && !markerForm.inputUrl.trim())}
                        sx={{ px: 4 }}
                      >
                        {markerLoading ? 'Processing...' : 'Convert with Marker Pro'}
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    🚀 Why Marker?
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Professional-grade document processing with advanced AI features:
                  </Typography>
                  <Box sx={{ mb: 3 }}>
                    <Chip label="95%+ Accuracy" size="small" color="primary" sx={{ m: 0.5 }} />
                    <Chip label="Table Processing" size="small" color="secondary" sx={{ m: 0.5 }} />
                    <Chip label="Math Equations" size="small" color="success" sx={{ m: 0.5 }} />
                    <Chip label="Multi-Language" size="small" color="info" sx={{ m: 0.5 }} />
                    <Chip label="Image Extraction" size="small" color="warning" sx={{ m: 0.5 }} />
                    <Chip label="LLM Enhanced" size="small" color="error" sx={{ m: 0.5 }} />
                  </Box>

                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Core Features
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 3 }}>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      ✓ Advanced table formatting
                    </Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      ✓ Mathematical equation processing
                    </Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      ✓ Header/footer removal
                    </Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      ✓ Code block formatting
                    </Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      ✓ Reference link preservation
                    </Typography>
                  </Box>

                  <Typography variant="h6" sx={{ mb: 2 }}>
                    🤖 LLM Enhancement
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 3 }}>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      🧠 Table merging across pages
                    </Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      📊 Form value extraction
                    </Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      🔧 Complex layout handling
                    </Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      ✨ Content correction & cleanup
                    </Typography>
                  </Box>

                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Supported Formats
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Chip icon={<PdfIcon />} label="PDF" color="error" variant="outlined" size="small" />
                    <Chip icon={<WordIcon />} label="DOCX/DOC" color="primary" variant="outlined" size="small" />
                    <Chip icon={<ExcelIcon />} label="XLSX/XLS" color="success" variant="outlined" size="small" />
                    <Chip icon={<ImageIcon />} label="Images" color="secondary" variant="outlined" size="small" />
                    <Chip icon={<CodeIcon />} label="HTML/EPUB" color="info" variant="outlined" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* Results Section */}
      {(result || error || jobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                📄 Document Conversion Result
                {jobStatus && jobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {jobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {pollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={jobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: jobStatus === 'completed' ? 'success.main' : 
                           jobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {jobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {/* Results */}
              {result && jobStatus === 'completed' && result.result?.markdown_content && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    🎉 Document converted successfully!
                  </Alert>
                  
                  {/* Document Info */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Document Information:</Typography>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        {result.result.original_filename && (
                          <Grid item xs={6}>
                            <strong>Filename:</strong> {result.result.original_filename}
                          </Grid>
                        )}
                        {result.result.word_count && (
                          <Grid item xs={6}>
                            <strong>Word Count:</strong> {result.result.word_count}
                          </Grid>
                        )}
                        {result.result.character_count && (
                          <Grid item xs={6}>
                            <strong>Character Count:</strong> {result.result.character_count}
                          </Grid>
                        )}
                        {result.result.processing_time && (
                          <Grid item xs={6}>
                            <strong>Processing Time:</strong> {result.result.processing_time.toFixed(2)}s
                          </Grid>
                        )}
                        {result.result.file_type && (
                          <Grid item xs={6}>
                            <strong>File Type:</strong> {result.result.file_type}
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  </Box>

                  {/* Markdown Content */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2">📝 Markdown Content:</Typography>
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => copyToClipboard(result.result?.markdown_content || '')}
                          title="Copy to clipboard"
                        >
                          {copied ? <CheckIcon color="success" /> : <CopyIcon />}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => downloadMarkdown(
                            result.result?.markdown_content || '',
                            `${result.result?.original_filename || 'document'}.md`
                          )}
                          title="Download as .md"
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Box>
                    </Box>
                    <Paper sx={{ p: 3, bgcolor: '#f0f9ff', maxHeight: 400, overflow: 'auto' }}>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                        {result.result.markdown_content}
                      </Typography>
                    </Paper>
                  </Box>
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {result && !result.result?.markdown_content && jobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Document conversion job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {result.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Langextract Results Section */}
      {(extractResult || extractError || extractJobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <ExtractIcon />
                AI Data Extraction Result
                {extractJobStatus && extractJobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {extractJobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {extractPollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={extractJobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={extractJobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: extractJobStatus === 'completed' ? 'success.main' : 
                           extractJobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {extractJobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {extractError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {extractError}
                </Alert>
              )}
              
              {/* Extraction Results */}
              {extractResult && extractJobStatus === 'completed' && extractResult.result && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    🧠 Structured data extracted successfully!
                  </Alert>
                  
                  {/* Enhanced Extraction Info */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>📊 Enhanced Extraction Summary:</Typography>
                    
                    {/* Key Metrics */}
                    <Paper sx={{ p: 2, bgcolor: '#f0f9ff', mb: 2 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                              {extractResult.result.total_extractions}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Total Entities</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="secondary" sx={{ fontWeight: 'bold' }}>
                              {extractResult.result.quality_metrics?.unique_entity_types || Object.keys(extractResult.result.extracted_data).length}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Entity Types</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="success.main" sx={{ fontWeight: 'bold' }}>
                              {extractResult.result.quality_metrics?.average_confidence ? 
                                `${Math.round(extractResult.result.quality_metrics.average_confidence * 100)}%` : 'N/A'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Avg Confidence</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="info.main" sx={{ fontWeight: 'bold' }}>
                              {Math.round(extractResult.result.input_text_length / Math.max(0.1, extractResult.result.processing_time))}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Chars/sec</Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Paper>

                    {/* Detailed Info */}
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        <Grid item xs={6}>
                          <strong>Model Used:</strong> {extractResult.result.model_used}
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Processing Time:</strong> {extractResult.result.processing_time?.toFixed(2)}s
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Input Length:</strong> {extractResult.result.input_text_length} characters
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Extraction Type:</strong> {extractResult.result.extraction_type || 'Unknown'}
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Source Grounding:</strong> {extractResult.result.source_grounding_enabled ? 'Enabled' : 'Disabled'}
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Rich Attributes:</strong> {extractResult.result.extraction_config?.has_attributes ? 'Yes' : 'No'}
                        </Grid>
                        {extractResult.result.quality_metrics?.entities_with_attributes && (
                          <Grid item xs={6}>
                            <strong>Entities with Attributes:</strong> {extractResult.result.quality_metrics.entities_with_attributes}
                          </Grid>
                        )}
                        {extractResult.result.extraction_config?.extraction_passes && (
                          <Grid item xs={6}>
                            <strong>Extraction Passes:</strong> {extractResult.result.extraction_config.extraction_passes}
                          </Grid>
                        )}
                      </Grid>

                      {/* Configuration Tags */}
                      <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip label={extractResult.result.model_used} size="small" color="primary" />
                        <Chip label={`${extractResult.result.processing_time.toFixed(2)}s`} size="small" color="secondary" />
                        {extractResult.result.extraction_config?.extraction_passes && (
                          <Chip label={`${extractResult.result.extraction_config.extraction_passes} passes`} size="small" color="info" />
                        )}
                        {extractResult.result.extraction_config?.has_attributes && (
                          <Chip label="Rich attributes" size="small" color="success" />
                        )}
                        {extractResult.result.quality_metrics?.average_confidence && extractResult.result.quality_metrics.average_confidence > 0.8 && (
                          <Chip label="High confidence" size="small" color="success" />
                        )}
                      </Box>
                    </Paper>
                  </Box>

                  {/* Extracted Data */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2">🔍 Extracted Structured Data:</Typography>
                      <IconButton
                        size="small"
                        onClick={() => copyToClipboard(JSON.stringify(extractResult.result?.extracted_data, null, 2) || '')}
                        title="Copy JSON to clipboard"
                      >
                        {copied ? <CheckIcon color="success" /> : <CopyIcon />}
                      </IconButton>
                    </Box>
                    
                    {extractResult.result.extracted_data && Object.entries(extractResult.result.extracted_data).map(([category, entities]) => (
                      <Accordion key={category} sx={{ mb: 1 }}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip 
                              label={`${category.toUpperCase()} (${entities.length})`} 
                              size="small" 
                              color="primary" 
                              variant="outlined" 
                            />
                            {/* Show average confidence for this category */}
                            {entities.some(e => e.confidence_score !== undefined) && (
                              <Chip 
                                label={`${Math.round((entities.reduce((sum, e) => sum + (e.confidence_score || 0), 0) / entities.length) * 100)}% confidence`}
                                size="small" 
                                color="success" 
                                variant="filled"
                              />
                            )}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                            {entities.map((entity: ExtractedEntity, index: number) => (
                              <Paper key={index} sx={{ p: 2, bgcolor: '#f0f9ff', border: '1px solid #e0f2fe' }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                                  <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                                    {entity.text || entity.value}
                                  </Typography>
                                  {entity.confidence_score !== undefined && (
                                    <Chip 
                                      label={`${Math.round(entity.confidence_score * 100)}%`}
                                      size="small"
                                      color={entity.confidence_score > 0.8 ? "success" : entity.confidence_score > 0.6 ? "warning" : "error"}
                                      sx={{ ml: 1 }}
                                    />
                                  )}
                                </Box>

                                {/* Rich Attributes Display */}
                                {entity.attributes && Object.keys(entity.attributes).length > 0 && (
                                  <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 'medium' }}>
                                      📋 Attributes:
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                      {Object.entries(entity.attributes).map(([key, value]) => (
                                        <Chip
                                          key={key}
                                          label={`${key}: ${value}`}
                                          size="small"
                                          variant="outlined"
                                          color="secondary"
                                          sx={{ fontSize: '0.7rem' }}
                                        />
                                      ))}
                                    </Box>
                                  </Box>
                                )}

                                {/* Context and Type Display */}
                                <Box sx={{ mb: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                  {entity.context && (
                                    <Chip label={`Context: ${entity.context}`} size="small" color="info" variant="outlined" />
                                  )}
                                  {entity.type && (
                                    <Chip label={`Type: ${entity.type}`} size="small" color="secondary" variant="outlined" />
                                  )}
                                  {entity.role && (
                                    <Chip label={`Role: ${entity.role}`} size="small" color="primary" variant="outlined" />
                                  )}
                                </Box>

                                {/* Source Grounding */}
                                {entity.sources && entity.sources.length > 0 && (
                                  <Box>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 'medium' }}>
                                      🎯 Source grounding:
                                    </Typography>
                                    {entity.sources.map((source, sourceIndex: number) => (
                                      <Box key={sourceIndex} sx={{ mb: 1 }}>
                                        <Typography variant="body2" sx={{ 
                                          fontFamily: 'monospace', 
                                          fontSize: '0.8rem',
                                          bgcolor: '#fff3e0',
                                          p: 1.5,
                                          borderRadius: 1,
                                          border: '1px solid #ffcc02',
                                          display: 'inline-block',
                                          maxWidth: '100%',
                                          wordBreak: 'break-word'
                                        }}>
                                          "{source.text}"
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary" sx={{ ml: 1, display: 'block', mt: 0.5 }}>
                                          📍 Position: {source.start}-{source.end} ({source.end - source.start} chars)
                                        </Typography>
                                      </Box>
                                    ))}
                                  </Box>
                                )}
                              </Paper>
                            ))}
                          </Box>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                  </Box>
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {extractResult && !extractResult.result && extractJobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Data extraction job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {extractResult.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Marker Results Section */}
      {(markerResult || markerError || markerJobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <MarkerIcon />
                Marker Professional Processing Result
                {markerJobStatus && markerJobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {markerJobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {markerPollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={markerJobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={markerJobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: markerJobStatus === 'completed' ? 'success.main' : 
                           markerJobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {markerJobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {markerError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {markerError}
                </Alert>
              )}
              
              {/* Marker Results */}
              {markerResult && markerJobStatus === 'completed' && markerResult.result && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    🚀 Document processed successfully with Marker!
                  </Alert>
                  
                  {/* Document Info */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2 }}>📊 Processing Summary:</Typography>
                    
                    {/* Key Metrics */}
                    <Paper sx={{ p: 2, bgcolor: '#f0f9ff', mb: 2 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                              {markerResult.result.word_count}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Words</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="secondary" sx={{ fontWeight: 'bold' }}>
                              {markerResult.result.image_count}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Images</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="success.main" sx={{ fontWeight: 'bold' }}>
                              {markerResult.result.output_format.toUpperCase()}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Format</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Box sx={{ textAlign: 'center', p: 1 }}>
                            <Typography variant="h4" color="info.main" sx={{ fontWeight: 'bold' }}>
                              {Math.round(markerResult.result.character_count / 1024)}KB
                            </Typography>
                            <Typography variant="caption" color="text.secondary">Size</Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Paper>

                    {/* Processing Settings */}
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        <Grid item xs={6}>
                          <strong>Original File:</strong> {markerResult.result.original_filename}
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Output File:</strong> {markerResult.result.output_filename}
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Characters:</strong> {markerResult.result.character_count.toLocaleString()}
                        </Grid>
                        <Grid item xs={6}>
                          <strong>Processing:</strong> {markerResult.result.processing_settings.force_ocr ? 'OCR' : 'Text'} + 
                          {markerResult.result.processing_settings.use_llm ? ' LLM' : ' Standard'}
                        </Grid>
                      </Grid>

                      {/* Settings Tags */}
                      <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip label={markerResult.result.output_format} size="small" color="primary" />
                        {markerResult.result.processing_settings.force_ocr && (
                          <Chip label="OCR Enabled" size="small" color="secondary" />
                        )}
                        {markerResult.result.processing_settings.use_llm && (
                          <Chip label="LLM Enhanced" size="small" color="success" />
                        )}
                        {markerResult.result.processing_settings.preserve_images && (
                          <Chip label="Images Extracted" size="small" color="info" />
                        )}
                      </Box>
                    </Paper>
                  </Box>

                  {/* Content Display */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2">📄 Processed Content:</Typography>
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => copyToClipboard(markerResult.result?.content || '')}
                          title="Copy content to clipboard"
                        >
                          {copied ? <CheckIcon color="success" /> : <CopyIcon />}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => window.open(markerResult.result?.content_url, '_blank')}
                          title="Download processed file"
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Box>
                    </Box>
                    <Paper sx={{ p: 3, bgcolor: '#f0f9ff', maxHeight: 400, overflow: 'auto' }}>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                        {markerResult.result.content.substring(0, 2000)}
                        {markerResult.result.content.length > 2000 && '...\n\n[Content truncated - click download to view full file]'}
                      </Typography>
                    </Paper>
                  </Box>

                  {/* Image Gallery */}
                  {Object.keys(markerResult.result.image_urls).length > 0 && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" sx={{ mb: 2 }}>🖼️ Extracted Images ({Object.keys(markerResult.result.image_urls).length}):</Typography>
                      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        {Object.entries(markerResult.result.image_urls).map(([imageName, imageUrl]) => (
                          <Box key={imageName} sx={{ textAlign: 'center' }}>
                            <img 
                              src={imageUrl} 
                              alt={imageName}
                              style={{ 
                                maxWidth: '150px', 
                                maxHeight: '150px', 
                                objectFit: 'contain',
                                border: '1px solid #e0e0e0',
                                borderRadius: '4px'
                              }}
                            />
                            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                              {imageName}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {markerResult && !markerResult.result && markerJobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Marker processing job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {markerResult.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default Documents;
