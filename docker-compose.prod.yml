version: "3.8"

# Production configuration - optimized for deployment
# Can be used standalone for Coolify deployments

services:
  postgres:
    image: postgres:16-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ouinhi}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-ouinhi}"]
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources: {}
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources: {}
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  api:
    build:
      context: .
      target: production
      cache_from:
        - ${DOCKER_REGISTRY:-}ouinhi-api:latest
      args:
        BUILDKIT_INLINE_CACHE: 1
    expose:
      - "8000"
    volumes:
      - kokoro_models:/app/models
      - huggingface_cache:/root/.cache/huggingface
      - production_logs:/app/logs
    environment:
      # Production optimizations
      PYTHONUNBUFFERED: "1"
      PYTHONDONTWRITEBYTECODE: "1"
      DEBUG: "false"
      
      # Core API configuration
      API_KEY: ${API_KEY}
      
      # Admin authentication
      ADMIN_USERNAME: ${ADMIN_USERNAME:-admin}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD:-admin123}
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-your_jwt_secret_key_change_in_production}
      
      # Dashboard authentication
      DEFAULT_USERNAME: ${DEFAULT_USERNAME:-admin}
      DEFAULT_PASSWORD: ${DEFAULT_PASSWORD:-admin}
      
      # S3 Storage configuration
      S3_ENDPOINT_URL: ${S3_ENDPOINT_URL}
      S3_ACCESS_KEY: ${S3_ACCESS_KEY}
      S3_SECRET_KEY: ${S3_SECRET_KEY}
      S3_BUCKET_NAME: ${S3_BUCKET_NAME}
      S3_REGION: ${S3_REGION}
      
      # Database configuration using environment variables
      DATABASE_URL: postgresql+asyncpg://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-ouinhi}
      POSTGRES_DB: ${POSTGRES_DB:-ouinhi}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      
      # Redis configuration
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      
      # AI Services
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      POLLINATIONS_API_KEY: ${POLLINATIONS_API_KEY}
      GROQ_API_KEY: ${GROQ_API_KEY}
      TOGETHER_API_KEY: ${TOGETHER_API_KEY}
      PERPLEXITY_API_KEY: ${PERPLEXITY_API_KEY}
      
      # TTS Configuration
      TTS_PROVIDER: ${TTS_PROVIDER:-kokoro}
      KOKORO_MODEL_PATH: ${KOKORO_MODEL_PATH:-/app/models}
      
      # Postiz Integration
      POSTIZ_API_KEY: ${POSTIZ_API_KEY}
      POSTIZ_API_URL: ${POSTIZ_API_URL:-https://api.postiz.com/public/v1}
      
      # ComfyUI Integration
      COMFYUI_URL: ${COMFYUI_URL}
      COMFYUI_USERNAME: ${COMFYUI_USERNAME}
      COMFYUI_PASSWORD: ${COMFYUI_PASSWORD}
      COMFYUI_API_KEY: ${COMFYUI_API_KEY}
      
      # Model pre-loading configuration
      ENABLE_MODEL_PRELOAD: ${ENABLE_MODEL_PRELOAD:-true}
      MUSICGEN_MODEL_SIZE: ${MUSICGEN_MODEL_SIZE:-small}
      PRELOAD_MUSICGEN_MEDIUM: ${PRELOAD_MUSICGEN_MEDIUM:-false}
      PRELOAD_MUSICGEN_LARGE: ${PRELOAD_MUSICGEN_LARGE:-false}
      TRANSFORMERS_CACHE: /root/.cache/huggingface
      
      # Performance optimizations
      TORCH_NUM_THREADS: "4"
      OMP_NUM_THREADS: "4"
      MKL_NUM_THREADS: "4"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs", "--connect-timeout", "10"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s  # Allow more time for model pre-loading
    deploy:
      resources:
        limits:
          memory: 8G  # Optimized for production
        reservations:
          memory: 2G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    # Production command with optimized workers for your system
    command: >
      sh -c "./scripts/startup.sh && 
      exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 2 --worker-class uvicorn.workers.UvicornWorker"

volumes:
  postgres_data:
  redis_data:
  kokoro_models:
  huggingface_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ouinhi/model_cache  # Persistent path on host
  production_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ouinhi/logs

networks:
  default:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16