# Postiz Integration Examples

This guide provides practical examples for integrating Postiz with your content generation workflows.

## Complete Workflow Examples

### Example 1: AI Video → Social Media Pipeline

```bash
#!/bin/bash
# Complete pipeline: Generate video and auto-schedule to social media

API_KEY="your_api_key_here"
BASE_URL="http://localhost:8000"

# Step 1: Generate a video from topic
echo "🎬 Generating video..."
VIDEO_JOB=$(curl -s -X POST "$BASE_URL/api/v1/ai/footage-to-video" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "10 Mind-Blowing Space Facts",
    "duration": 60,
    "voice_provider": "kokoro",
    "voice_name": "af_bella"
  }' | jq -r '.job_id')

echo "Video job ID: $VIDEO_JOB"

# Step 2: Poll until video is complete
while true; do
  STATUS=$(curl -s -X GET "$BASE_URL/api/v1/ai/footage-to-video/$VIDEO_JOB" \
    -H "X-API-Key: $API_KEY" | jq -r '.status')
  
  if [ "$STATUS" = "completed" ]; then
    echo "✅ Video generation completed!"
    break
  elif [ "$STATUS" = "failed" ]; then
    echo "❌ Video generation failed!"
    exit 1
  else
    echo "⏳ Video status: $STATUS"
    sleep 30
  fi
done

# Step 3: Get available social media integrations
echo "📱 Getting social media integrations..."
INTEGRATIONS=$(curl -s -X GET "$BASE_URL/api/v1/postiz/integrations" \
  -H "X-API-Key: $API_KEY")

echo "Available integrations: $INTEGRATIONS"

# Step 4: Schedule to multiple platforms
echo "📤 Scheduling to social media..."
curl -X POST "$BASE_URL/api/v1/postiz/schedule-job" \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"job_id\": \"$VIDEO_JOB\",
    \"content\": \"🚀 Just discovered these incredible space facts! Which one surprised you the most? #Space #Facts #AI #Science\",
    \"integrations\": [\"twitter_123\", \"linkedin_456\"],
    \"post_type\": \"now\",
    \"tags\": [\"space\", \"facts\", \"AI\", \"educational\"]
  }"

echo "🎉 Content scheduled successfully!"
```

### Example 2: Batch Image Generation and Scheduling

```javascript
// Node.js example for batch image generation and social media scheduling
const axios = require('axios');

const API_KEY = 'your_api_key_here';
const BASE_URL = 'http://localhost:8000';

async function generateAndScheduleImages() {
  const topics = [
    'Futuristic cityscape at sunset',
    'Serene mountain lake reflection',
    'Abstract digital art with neon colors'
  ];

  for (const topic of topics) {
    try {
      // Generate image
      console.log(`🎨 Generating image: ${topic}`);
      const imageResponse = await axios.post(
        `${BASE_URL}/api/images/generate`,
        {
          prompt: topic,
          width: 1024,
          height: 1024,
          quality: 'high'
        },
        {
          headers: { 'X-API-Key': API_KEY }
        }
      );

      const jobId = imageResponse.data.job_id;
      
      // Poll for completion
      let status = 'pending';
      while (status !== 'completed') {
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        const statusResponse = await axios.get(
          `${BASE_URL}/api/images/generate/${jobId}`,
          { headers: { 'X-API-Key': API_KEY } }
        );
        
        status = statusResponse.data.status;
        console.log(`📊 Image status: ${status}`);
        
        if (status === 'failed') {
          throw new Error('Image generation failed');
        }
      }

      // Schedule to social media with custom content
      const socialContent = `✨ Check out this AI-generated masterpiece: "${topic}" 🎭\n\n#AIArt #DigitalArt #Creative #AI`;
      
      await axios.post(
        `${BASE_URL}/api/v1/postiz/schedule-job`,
        {
          job_id: jobId,
          content: socialContent,
          integrations: ['instagram_789', 'twitter_123'],
          post_type: 'schedule',
          schedule_date: new Date(Date.now() + Math.random() * 24 * 60 * 60 * 1000).toISOString(), // Random time within 24 hours
          tags: ['ai-art', 'creative', 'generated']
        },
        {
          headers: { 'X-API-Key': API_KEY }
        }
      );

      console.log(`✅ Scheduled: ${topic}`);
      
    } catch (error) {
      console.error(`❌ Error with "${topic}":`, error.message);
    }
  }
}

generateAndScheduleImages();
```

### Example 3: YouTube Shorts to TikTok Pipeline

```python
import requests
import time
import json

API_KEY = "your_api_key_here"
BASE_URL = "http://localhost:8000"

def create_tiktok_short(youtube_url):
    """Convert YouTube video to TikTok short and schedule"""
    
    # Step 1: Generate YouTube Short
    print(f"🎬 Creating short from: {youtube_url}")
    response = requests.post(
        f"{BASE_URL}/api/v1/yt-shorts/",
        headers={"X-API-Key": API_KEY},
        json={
            "video_url": youtube_url,
            "max_duration": 60,
            "highlight_detection": True,
            "face_tracking": True,
            "viral_optimization": True
        }
    )
    
    job_id = response.json()["job_id"]
    print(f"📋 Job ID: {job_id}")
    
    # Step 2: Wait for completion
    while True:
        status_response = requests.get(
            f"{BASE_URL}/api/v1/yt-shorts/{job_id}",
            headers={"X-API-Key": API_KEY}
        )
        
        status_data = status_response.json()
        status = status_data["status"]
        
        if status == "completed":
            print("✅ Short creation completed!")
            result = status_data["result"]
            break
        elif status == "failed":
            print("❌ Short creation failed!")
            return None
        else:
            print(f"⏳ Status: {status}")
            time.sleep(30)
    
    # Step 3: Create engaging TikTok content
    video_title = result.get("title", "Amazing content")
    tiktok_content = f"""
🔥 {video_title} 

{result.get('description', 'Check out this incredible content!')}

#viral #trending #fyp #amazing #ai
""".strip()
    
    # Step 4: Schedule to TikTok
    print("📱 Scheduling to TikTok...")
    schedule_response = requests.post(
        f"{BASE_URL}/api/v1/postiz/schedule-job",
        headers={"X-API-Key": API_KEY},
        json={
            "job_id": job_id,
            "content": tiktok_content,
            "integrations": ["tiktok_999"],
            "post_type": "schedule",
            "schedule_date": "2024-12-25T18:00:00Z",  # Peak TikTok time
            "tags": ["viral", "tiktok", "shorts", "ai"]
        }
    )
    
    if schedule_response.status_code == 200:
        print("🎉 Successfully scheduled to TikTok!")
        return schedule_response.json()
    else:
        print(f"❌ Scheduling failed: {schedule_response.text}")
        return None

# Usage
youtube_urls = [
    "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "https://www.youtube.com/watch?v=example123"
]

for url in youtube_urls:
    result = create_tiktok_short(url)
    if result:
        print(f"✅ Processed: {url}")
    else:
        print(f"❌ Failed: {url}")
    
    time.sleep(60)  # Rate limiting
```

### Example 4: Daily Content Automation

```python
import schedule
import requests
from datetime import datetime, timedelta

API_KEY = "your_api_key_here"
BASE_URL = "http://localhost:8000"

class ContentAutomation:
    def __init__(self):
        self.topics = [
            "Daily motivation quote",
            "Tech tip of the day", 
            "Fun fact about science",
            "Productivity hack",
            "Inspiring success story"
        ]
        self.integrations = ["twitter_123", "linkedin_456"]
    
    def generate_daily_content(self):
        """Generate and schedule daily content"""
        today = datetime.now()
        topic = self.topics[today.weekday() % len(self.topics)]
        
        print(f"📅 {today.strftime('%Y-%m-%d')}: Generating '{topic}'")
        
        # Generate image for the topic
        response = requests.post(
            f"{BASE_URL}/api/images/generate",
            headers={"X-API-Key": API_KEY},
            json={
                "prompt": f"Professional social media post about {topic}, clean design, inspiring",
                "width": 1080,
                "height": 1080
            }
        )
        
        job_id = response.json()["job_id"]
        
        # Wait for completion (simplified)
        import time
        time.sleep(60)  # Wait 1 minute
        
        # Schedule for optimal posting time (9 AM next day)
        schedule_time = (today + timedelta(days=1)).replace(hour=9, minute=0, second=0)
        
        schedule_response = requests.post(
            f"{BASE_URL}/api/v1/postiz/schedule-job",
            headers={"X-API-Key": API_KEY},
            json={
                "job_id": job_id,
                "content": f"🌟 {topic.title()} 🌟\n\nWhat's your take on this? Share in the comments! 👇\n\n#motivation #productivity #success",
                "integrations": self.integrations,
                "post_type": "schedule",
                "schedule_date": schedule_time.isoformat() + "Z",
                "tags": ["daily", "automation", topic.lower().replace(" ", "-")]
            }
        )
        
        print(f"✅ Scheduled for {schedule_time}")
    
    def weekly_batch_generation(self):
        """Generate a week's worth of content"""
        print("📦 Generating weekly batch content...")
        
        for i in range(7):
            future_date = datetime.now() + timedelta(days=i)
            topic = f"Weekly insight #{i+1}: {self.topics[i % len(self.topics)]}"
            
            # Generate content
            response = requests.post(
                f"{BASE_URL}/api/v1/ai/footage-to-video",
                headers={"X-API-Key": API_KEY},
                json={
                    "topic": topic,
                    "duration": 30,
                    "voice_provider": "kokoro"
                }
            )
            
            job_id = response.json()["job_id"]
            
            # Schedule for different times throughout the week
            schedule_time = future_date.replace(hour=10 + (i % 12), minute=0)
            
            requests.post(
                f"{BASE_URL}/api/v1/postiz/schedule-job",
                headers={"X-API-Key": API_KEY},
                json={
                    "job_id": job_id,
                    "integrations": self.integrations,
                    "post_type": "schedule",
                    "schedule_date": schedule_time.isoformat() + "Z",
                    "tags": ["weekly", "batch", f"day-{i+1}"]
                }
            )
            
            print(f"📋 Queued: {topic} for {schedule_time}")

# Set up automation
automation = ContentAutomation()

# Schedule daily content generation
schedule.every().day.at("08:00").do(automation.generate_daily_content)

# Schedule weekly batch on Sundays
schedule.every().sunday.at("07:00").do(automation.weekly_batch_generation)

# Run the scheduler
print("🤖 Content automation started!")
while True:
    schedule.run_pending()
    time.sleep(60)
```

## Integration with Other APIs

### Webhook Integration

```javascript
// Express.js webhook handler for automated scheduling
const express = require('express');
const axios = require('axios');

const app = express();
app.use(express.json());

app.post('/webhook/job-completed', async (req, res) => {
  const { job_id, job_type, result } = req.body;
  
  // Auto-schedule certain job types
  const schedulableTypes = ['footage_to_video', 'image_generation', 'yt_shorts'];
  
  if (schedulableTypes.includes(job_type)) {
    try {
      await axios.post('http://localhost:8000/api/v1/postiz/schedule-job', {
        job_id: job_id,
        integrations: ['twitter_123'],
        post_type: 'schedule',
        schedule_date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours later
        tags: ['auto-scheduled', job_type]
      }, {
        headers: { 'X-API-Key': process.env.API_KEY }
      });
      
      console.log(`✅ Auto-scheduled job ${job_id}`);
    } catch (error) {
      console.error(`❌ Failed to auto-schedule ${job_id}:`, error.message);
    }
  }
  
  res.status(200).json({ success: true });
});

app.listen(3000, () => {
  console.log('Webhook server running on port 3000');
});
```

## Performance Optimization Tips

1. **Batch Operations**: Process multiple jobs before scheduling
2. **Rate Limiting**: Respect social media platform limits
3. **Error Recovery**: Implement retry logic for failed requests
4. **Caching**: Cache integration data to reduce API calls
5. **Monitoring**: Log all scheduling activities for debugging

## Security Best Practices

1. **API Key Protection**: Never expose API keys in client-side code
2. **Input Validation**: Validate all user inputs before API calls
3. **Rate Limiting**: Implement proper rate limiting in your applications
4. **Error Handling**: Don't expose internal errors in API responses
5. **Audit Logging**: Keep logs of all scheduling activities

---

*These examples demonstrate various integration patterns. Adapt them to your specific use case and requirements.*