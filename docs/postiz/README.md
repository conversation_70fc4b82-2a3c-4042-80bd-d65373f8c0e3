# Postiz Integration

Postiz integration allows you to automatically schedule and publish your generated content to multiple social media platforms directly from the Ouinhi API.

## Overview

The Postiz integration enables seamless social media scheduling for:
- Generated videos (topic-to-video, YouTube Shorts, etc.)
- Created images (AI-generated, edited, etc.)
- Audio content (TTS, music, etc.)
- Custom text posts with media attachments

## Features

- **Multi-Platform Support**: Post to Twitter, LinkedIn, Instagram, Facebook, TikTok, and more
- **Flexible Scheduling**: Post now, schedule for later, or save as draft
- **Job Integration**: Automatically schedule content from completed jobs
- **Media Support**: Handle videos, images, and audio attachments
- **Smart Content Suggestions**: AI-generated post content based on your media

## Prerequisites

1. **Postiz Account**: Sign up at [postiz.com](https://postiz.com)
2. **API Key**: Get your Postiz API key from your account settings
3. **Social Media Integrations**: Connect your social media accounts in Postiz

## Environment Configuration

```bash
# Postiz API Configuration
POSTIZ_API_KEY=your_postiz_api_key_here
POSTIZ_API_URL=https://api.postiz.com/public/v1  # Default: Postiz cloud API
```

## API Endpoints

### Base URL
All Postiz endpoints are prefixed with `/api/v1/postiz`

### Authentication
All endpoints require the `X-API-Key` header with your Ouinhi API key.

## Quick Start

### 1. Get Available Integrations

```bash
curl -X GET "http://localhost:8000/api/v1/postiz/integrations" \
  -H "X-API-Key: your_api_key"
```

**Response:**
```json
[
  {
    "id": "twitter_123",
    "name": "@your_twitter",
    "provider": "twitter"
  },
  {
    "id": "linkedin_456",
    "name": "Your LinkedIn",
    "provider": "linkedin"
  }
]
```

### 2. Schedule a Simple Post

```bash
curl -X POST "http://localhost:8000/api/v1/postiz/schedule" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Check out this amazing AI-generated content! 🚀",
    "integrations": ["twitter_123", "linkedin_456"],
    "post_type": "now"
  }'
```

### 3. Schedule Content from a Completed Job

```bash
# First, create a video
curl -X POST "http://localhost:8000/api/v1/ai/footage-to-video" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"topic": "amazing ocean facts"}'

# Then schedule the result
curl -X POST "http://localhost:8000/api/v1/postiz/schedule-job" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "job_id": "your_completed_job_id",
    "integrations": ["twitter_123"],
    "post_type": "now"
  }'
```

## Advanced Usage

### Scheduled Posts

```bash
curl -X POST "http://localhost:8000/api/v1/postiz/schedule" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Exciting content coming soon! 🎬",
    "integrations": ["twitter_123"],
    "post_type": "schedule",
    "schedule_date": "2024-12-25T12:00:00Z",
    "tags": ["AI", "content", "automation"]
  }'
```

### Draft Posts

```bash
curl -X POST "http://localhost:8000/api/v1/postiz/create-draft" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Draft content to review later",
    "integrations": ["linkedin_456"],
    "tags": ["draft", "review"]
  }'
```

### Check Job Scheduling Availability

```bash
curl -X GET "http://localhost:8000/api/v1/postiz/job/your_job_id/scheduling-info" \
  -H "X-API-Key: your_api_key"
```

## Supported Content Types

### Automatic Detection
The Postiz integration automatically detects and handles different content types:

- **Videos**: `.mp4`, `.mov`, `.avi` files from video generation jobs
- **Images**: `.jpg`, `.png`, `.gif` files from image generation jobs  
- **Audio**: `.mp3`, `.wav` files from audio generation jobs
- **Text**: Plain text posts with optional media attachments

### Job Type Support
These job types can be automatically scheduled:
- `footage_to_video` - Topic-based video generation
- `aiimage_to_video` - Image-to-video conversion
- `scenes_to_video` - Scene-based video creation
- `short_video_creation` - YouTube Shorts generation
- `image_to_video` - Image animation
- `image_generation` - AI image creation
- `audio_generation` - TTS and music generation

## Error Handling

### Common Errors

**Configuration Error:**
```json
{
  "detail": "Postiz configuration error: Missing POSTIZ_API_KEY"
}
```

**Invalid API Key:**
```json
{
  "detail": "Invalid Postiz API key. Please check your POSTIZ_API_KEY environment variable."
}
```

**Job Not Found:**
```json
{
  "detail": "Job not found"
}
```

**Job Not Completed:**
```json
{
  "detail": "Job must be completed to schedule"
}
```

## Best Practices

1. **Check Job Completion**: Always verify jobs are completed before scheduling
2. **Use Suggested Content**: Let the API generate smart post content when possible
3. **Test Integrations**: Verify your social media integrations in Postiz dashboard
4. **Handle Errors**: Implement proper error handling for network and API issues
5. **Respect Rate Limits**: Be mindful of social media platform posting limits

## Integration Examples

See [examples.md](examples.md) for detailed integration examples and workflows.

## Troubleshooting

See [troubleshooting.md](troubleshooting.md) for common issues and solutions.

---

*For more information about Postiz features and account management, visit [postiz.com](https://postiz.com)*