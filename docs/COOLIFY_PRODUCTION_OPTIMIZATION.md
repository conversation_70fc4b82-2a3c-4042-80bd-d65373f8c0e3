# Coolify Deployment Guide - Production Optimizations 🚀

This guide provides production optimization recommendations for deploying your Ouinhi API with Coolify, including model pre-loading and persistent storage.

## 🎯 Key Optimizations Implemented

### 1. Model Pre-loading on Container Startup
- **Automatic model downloading**: Models are downloaded during container initialization rather than on first request
- **Reduced cold start times**: First API requests return instantly since models are already loaded
- **Graceful fallbacks**: Container startup won't fail if model loading encounters issues

### 2. Persistent Model Storage
- **HuggingFace model cache**: Persisted between deployments to avoid re-downloading models
- **Volume optimization**: Models are cached in `/root/.cache/huggingface` and persist across container restarts
- **Storage efficiency**: Shared model cache reduces disk usage for multiple deployments

## 🔧 Coolify Configuration

### Volume Mounts
Configure these persistent volumes in Coolify:

```yaml
# Required volumes for production optimization
volumes:
  - /opt/ouinhi/model_cache:/root/.cache/huggingface  # HuggingFace models
  - /opt/ouinhi/logs:/app/logs                        # Application logs
```

### Environment Variables
Add these environment variables in Coolify:

```env
# Model Pre-loading Configuration
ENABLE_MODEL_PRELOAD=true
MUSICGEN_MODEL_SIZE=small
PRELOAD_MUSICGEN_MEDIUM=false
PRELOAD_MUSICGEN_LARGE=false

# Performance Optimizations
TORCH_NUM_THREADS=4
OMP_NUM_THREADS=4
MKL_NUM_THREADS=4

# Cache Configuration
TRANSFORMERS_CACHE=/root/.cache/huggingface
```

### Resource Allocation
Recommended resource limits for Coolify:

```yaml
resources:
  limits:
    memory: 8GB    # Sufficient for model loading and operations
    cpu: 4000m     # 4 CPU cores for optimal performance
  reservations:
    memory: 2GB    # Minimum guaranteed memory
    cpu: 1000m     # 1 CPU core minimum
```

### Health Check Configuration
Update health check settings to accommodate model loading:

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health", "--connect-timeout", "5"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 120s  # Extended to allow model pre-loading
```

## 🚀 Deployment Steps

### 1. Prepare Host Storage
On your Coolify server, create the required directories:

```bash
sudo mkdir -p /opt/ouinhi/model_cache
sudo mkdir -p /opt/ouinhi/logs
sudo chown -R 1000:1000 /opt/ouinhi
```

### 2. Deploy with Production Configuration
Use the production Docker Compose configuration:

```bash
# Deploy using production configuration
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Verify Model Pre-loading
Check the container logs to confirm models are being pre-loaded:

```bash
docker logs <container_name> | grep "Pre-loading"
```

Expected output:
```
🚀 Pre-loading models for production optimization...
🚀 Pre-loading model: facebook/musicgen-stereo-small
✅ Successfully pre-loaded: facebook/musicgen-stereo-small
✅ Model pre-loading completed: 1/1 models loaded successfully
```

## 📊 Performance Benefits

### Before Optimization
- First API call: ~30-60 seconds (model download + loading)
- Subsequent calls: ~2-5 seconds
- Storage: ~2GB downloaded per deployment

### After Optimization
- First API call: ~2-5 seconds (model already loaded)
- Subsequent calls: ~2-5 seconds
- Storage: Models cached, minimal re-download

## 🔧 Advanced Configuration

### Load Multiple Model Sizes
For high-traffic deployments, pre-load multiple model sizes:

```env
PRELOAD_MUSICGEN_MEDIUM=true  # Enable medium model
PRELOAD_MUSICGEN_LARGE=true   # Enable large model (requires more memory)
```

**Resource Requirements:**
- Small model: ~2GB RAM
- Medium model: ~4GB RAM  
- Large model: ~8GB RAM

### Disable Pre-loading (if needed)
To disable model pre-loading:

```env
ENABLE_MODEL_PRELOAD=false
```

### Custom Model Cache Location
Change the cache location:

```env
TRANSFORMERS_CACHE=/custom/path/to/cache
```

## 🐛 Troubleshooting

### Model Loading Timeout
If models fail to load within the timeout:

1. **Increase timeout**: Modify the script timeout in `preload_models.py`
2. **Check network**: Ensure server has good internet connectivity to HuggingFace
3. **Reduce model size**: Use `MUSICGEN_MODEL_SIZE=small` for faster loading

### Storage Issues
If running out of disk space:

1. **Clean cache**: `docker system prune -a`
2. **Monitor usage**: `docker system df`
3. **Optimize models**: Only pre-load required model sizes

### Container Startup Failures
If container fails to start after optimization:

1. **Check logs**: `docker logs <container>`
2. **Disable pre-loading**: Set `ENABLE_MODEL_PRELOAD=false`
3. **Verify volumes**: Ensure volume paths exist and have correct permissions

## 📈 Monitoring

### Key Metrics to Monitor
- Container startup time
- Memory usage during model loading
- Disk usage for model cache
- API response times

### Logging
Model pre-loading events are logged with these prefixes:
- `🚀` Model loading started
- `✅` Model loaded successfully  
- `❌` Model loading failed
- `📦` Pre-loading disabled/skipped

## 🎉 Result

With these optimizations implemented:
- **Faster API responses**: No cold start delays
- **Reliable performance**: Consistent response times
- **Efficient resource usage**: Persistent model caching
- **Production ready**: Optimized for Coolify deployment

Your Ouinhi API will now start faster and provide consistent performance for your users! 🚀
