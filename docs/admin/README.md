# Admin API Documentation

The Ouinhi API provides comprehensive administrative endpoints for user management, job monitoring, and system maintenance.

## Overview

Admin endpoints provide:
- **User Management**: Create, update, list, and delete user accounts
- **Job Management**: Monitor background jobs, cleanup old jobs, and system statistics
- **System Monitoring**: Track API usage, performance metrics, and health status
- **Database Administration**: Manage database operations and maintenance

## Authentication

All admin endpoints require the `X-API-Key` header with a valid API key.

```bash
-H "X-API-Key: your_api_key"
```

## Base URLs

- **User Management**: `/api/admin/users`
- **Job Management**: `/api/admin/jobs`

## User Management API

### Create User

Create a new user account with specified role and permissions.

```bash
curl -X POST "http://localhost:8000/api/admin/users/" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "newuser",
    "full_name": "<PERSON>",
    "password": "secure_password",
    "role": "user",
    "is_active": true
  }'
```

**Request Model:**
```json
{
  "email": "string",
  "username": "string (optional)",
  "full_name": "string (optional)",
  "password": "string",
  "role": "admin | user | viewer",
  "is_active": "boolean"
}
```

### List Users

Retrieve paginated list of all users with filtering options.

```bash
curl -X GET "http://localhost:8000/api/admin/users/?limit=20&offset=0&role=user" \
  -H "X-API-Key: your_api_key"
```

**Query Parameters:**
- `limit`: Number of users per page (default: 50)
- `offset`: Number of users to skip (default: 0)
- `role`: Filter by user role (optional)
- `is_active`: Filter by active status (optional)

**Response:**
```json
{
  "users": [
    {
      "id": "uuid",
      "username": "john_doe",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "role": "user",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "last_login": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "total_count": 150,
    "limit": 20,
    "offset": 0,
    "has_more": true
  }
}
```

### Get User Details

Retrieve detailed information about a specific user.

```bash
curl -X GET "http://localhost:8000/api/admin/users/{user_id}" \
  -H "X-API-Key: your_api_key"
```

### Update User

Update user information and permissions.

```bash
curl -X PUT "http://localhost:8000/api/admin/users/{user_id}" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "John Smith",
    "role": "admin",
    "is_active": false
  }'
```

### Delete User

Remove a user account (soft delete - preserves data integrity).

```bash
curl -X DELETE "http://localhost:8000/api/admin/users/{user_id}" \
  -H "X-API-Key: your_api_key"
```

## Job Management API

### Manual Job Cleanup

Trigger immediate cleanup of old completed jobs.

```bash
curl -X POST "http://localhost:8000/api/admin/jobs/cleanup?max_age_hours=48" \
  -H "X-API-Key: your_api_key"
```

**Parameters:**
- `max_age_hours`: Jobs older than this will be deleted (default: 24)

**Response:**
```json
{
  "success": true,
  "message": "Job cleanup completed successfully. Deleted 150 jobs.",
  "max_age_hours": 48,
  "duration_seconds": 2.5,
  "timestamp": "2024-01-15T10:30:00Z",
  "cleanup_stats": {
    "total_deleted": 150,
    "by_status": {
      "completed": 120,
      "failed": 25,
      "cancelled": 5
    },
    "storage_freed_mb": 2500
  }
}
```

### Get Job Statistics

Retrieve comprehensive job processing statistics.

```bash
curl -X GET "http://localhost:8000/api/admin/jobs/stats" \
  -H "X-API-Key: your_api_key"
```

**Response:**
```json
{
  "total_jobs": 5000,
  "jobs_by_status": {
    "pending": 5,
    "processing": 3,
    "completed": 4850,
    "failed": 142
  },
  "jobs_by_type": {
    "footage_to_video": 1200,
    "image_generation": 800,
    "yt_shorts": 600,
    "audio_speech": 400
  },
  "performance_metrics": {
    "avg_completion_time_seconds": 45.2,
    "success_rate_percent": 97.1,
    "jobs_per_hour": 25
  },
  "recent_activity": {
    "last_hour": 25,
    "last_24_hours": 580,
    "last_week": 3500
  }
}
```

### Scheduler Status

Check the status of the background job scheduler.

```bash
curl -X GET "http://localhost:8000/api/admin/jobs/scheduler/status" \
  -H "X-API-Key: your_api_key"
```

### Force Job Cancellation

Cancel a specific running job.

```bash
curl -X POST "http://localhost:8000/api/admin/jobs/{job_id}/cancel" \
  -H "X-API-Key: your_api_key"
```

## User Roles and Permissions

### Role Hierarchy

1. **Admin**: Full system access
   - Create/modify/delete users
   - Access all admin endpoints
   - View all jobs and data
   - System configuration

2. **User**: Standard access
   - Create content and jobs
   - Access their own data
   - Limited admin viewing

3. **Viewer**: Read-only access
   - View public content
   - No creation permissions
   - Limited API access

### Permission Matrix

| Action | Admin | User | Viewer |
|--------|-------|------|--------|
| Create Users | ✅ | ❌ | ❌ |
| Manage Jobs | ✅ | Own Only | ❌ |
| System Stats | ✅ | Limited | ❌ |
| Content Creation | ✅ | ✅ | ❌ |
| API Access | Full | Standard | Limited |

## System Monitoring

### Health Check

Monitor system health and service availability.

```bash
curl -X GET "http://localhost:8000/api/admin/health" \
  -H "X-API-Key: your_api_key"
```

**Response:**
```json
{
  "status": "healthy",
  "services": {
    "database": "connected",
    "redis": "connected", 
    "s3": "accessible",
    "job_queue": "running"
  },
  "performance": {
    "cpu_usage_percent": 45.2,
    "memory_usage_percent": 67.8,
    "disk_usage_percent": 23.1
  },
  "uptime_seconds": 86400
}
```

### API Usage Statistics

Track API endpoint usage and performance.

```bash
curl -X GET "http://localhost:8000/api/admin/usage" \
  -H "X-API-Key: your_api_key"
```

## Error Handling

### Common Error Responses

**User Not Found:**
```json
{
  "detail": "User not found"
}
```

**Invalid Role:**
```json
{
  "detail": "Invalid role. Must be one of: admin, user, viewer"
}
```

**Insufficient Permissions:**
```json
{
  "detail": "Insufficient permissions to perform this action"
}
```

**Validation Error:**
```json
{
  "detail": [
    {
      "type": "value_error",
      "loc": ["email"],
      "msg": "Invalid email format"
    }
  ]
}
```

## Best Practices

### User Management

1. **Use Strong Passwords**: Enforce minimum 8 characters with mixed case
2. **Regular Audits**: Review user accounts monthly
3. **Role-Based Access**: Assign minimum necessary permissions
4. **Account Monitoring**: Track login activities and suspicious behavior

### Job Management

1. **Regular Cleanup**: Schedule automated cleanup of old jobs
2. **Monitor Performance**: Watch for increasing completion times
3. **Error Analysis**: Review failed jobs for patterns
4. **Resource Management**: Monitor storage and processing resources

### Security

1. **API Key Rotation**: Rotate admin API keys regularly
2. **Audit Logs**: Monitor admin endpoint usage
3. **Rate Limiting**: Implement appropriate request limits
4. **Access Reviews**: Regular permission audits

## Troubleshooting

### Common Issues

**Database Connection Errors:**
```bash
# Check database status
curl -X GET "http://localhost:8000/api/admin/health"
```

**Job Queue Performance Issues:**
```bash
# Check job statistics
curl -X GET "http://localhost:8000/api/admin/jobs/stats"

# Manual cleanup if needed
curl -X POST "http://localhost:8000/api/admin/jobs/cleanup"
```

**User Authentication Problems:**
```bash
# Verify user exists and is active
curl -X GET "http://localhost:8000/api/admin/users/{user_id}"
```

### Performance Optimization

1. **Database Indexing**: Ensure proper indexes on user and job tables
2. **Connection Pooling**: Configure appropriate database connections
3. **Caching**: Implement Redis caching for frequently accessed data
4. **Background Processing**: Use job queues for heavy operations

---

*For technical implementation details, see the source code in `app/routes/admin_users.py` and `app/routes/admin_jobs.py`*